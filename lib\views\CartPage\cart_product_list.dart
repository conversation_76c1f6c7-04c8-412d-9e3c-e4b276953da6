import 'package:alderishop/constants/constants.dart';
import 'package:alderishop/controllers/cart_controller.dart';
import 'package:alderishop/views/CartPage/widgets/cart_list_items_widget.dart';
import 'package:alderishop/views/home/<USER>/CustomText.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class CartProductListWidget extends StatefulWidget {
  const CartProductListWidget({
    super.key,
  });

  @override
  State<CartProductListWidget> createState() => _CartProductListWidgetState();
}

class _CartProductListWidgetState extends State<CartProductListWidget> {
  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
      future:
          Provider.of<CartController>(context, listen: false).getCartItems(),
      builder: (context, snapshot) {
        if (snapshot.connectionState != ConnectionState.done) {
          return const Center(child: CircularProgressIndicator());
        }

        return const CartProductsListShow(
          confirmscreen: true,
        );
      },
    );
  }
}

class CartProductsListShow extends StatelessWidget {
  final bool confirmscreen;
  const CartProductsListShow({
    super.key,
    required this.confirmscreen,
  });

  @override
  Widget build(BuildContext context) {
    var model = Provider.of<CartController>(context).cartItems;

    return Column(
      children: [

        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          scrollDirection: Axis.vertical,
          itemCount: model.length,
          itemBuilder: (BuildContext context, int index) {
            return CartListItemsWidget(
              confirmscreen: confirmscreen,
              model: model[index],
            );
          },
        ),
     
      ],
    );
  }
}
