import 'package:alderishop/controllers/auth_Controller.dart';
import 'package:alderishop/controllers/categores_controller.dart';
import 'package:alderishop/controllers/configration_controller.dart';
import 'package:alderishop/controllers/products_controller.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_pw_validator/Resource/Strings.dart';
import 'package:provider/provider.dart';

final appCtrl = AppController();

class AppController with ChangeNotifier {
  static final AppController _instance = AppController._internal();
  factory AppController() {
    return _instance;
  }

  AppController._internal();
  bool isTheme = false;
  bool isRTL = true;
  static bool isAuth = false;
  String languageCulture = "ar-LY";

  // static int shopType = 1;

  //************************************************************************* */

  // media query ********************

  static int currentLangId = 2;
  int langId = 2;
  Locale currentLang = const Locale('ar-LY');

  // Global MediaQuery
  static MediaQueryData? _mediaQueryData;
  static MediaQueryData get mq => _mediaQueryData!;
  static double W = AppController.mq.size.width;
  static double h = AppController.mq.size.height;
  static void setMq(BuildContext context) {
    // if (_mediaQueryData != null) return;
    print("is seeeet");
    print(currentLangId);
    _mediaQueryData = MediaQuery.of(context);

    W = AppController.mq.size.width;
    h = AppController.mq.size.height;
  }

  //************************************************************************* */
  Future<void> changeLanguage(String lang, BuildContext context) async {
    currentLang = Locale(lang);

    await context.setLocale(currentLang);
    updateCurrentLangId();

    // ignore: use_build_context_synchronously
    Provider.of<CategoryController>(context, listen: false).getCategory();
    // ignore: use_build_context_synchronously
    Provider.of<ProductsController>(context, listen: false)
        .getFeaturedProducts();
    // // ignore: use_build_context_synchronously
    // Provider.of<ProductsController>(context, listen: false)
    //     .getNewProductCategories();

    // ignore: use_build_context_synchronously
    Provider.of<ConfigrationController>(context, listen: false)
        .getPrivacyPolicy();
    // ignore: use_build_context_synchronously
    Provider.of<ConfigrationController>(context, listen: false).getTermsToUse();
    // ignore: use_build_context_synchronously
    // Provider.of<CategoryController>(context, listen: false).subCategory = [];
    // ignore: use_build_context_synchronously

    notifyListeners();
  }

  //=================//=================//=================//=================

  updateCurrentLangId() {
    switch (currentLang.languageCode) {
      case 'ar':
        currentLangId = 2;
        langId = 2;
        AuthController.headers['Accept-Language'] = "ar-LY";
        languageCulture = "ar-LY";

        break;

      case 'tr':
        currentLangId = 3;
        langId = 3;
        AuthController.headers['Accept-Language'] = "tr-TR";
        languageCulture = "tr-TR";

        break;

      default:
        currentLangId = 1;
        AuthController.headers['Accept-Language'] = "en-US";
        languageCulture = "en-US";

        langId = 1;
        break;
    }
  }
}

class PwValidatorStrings implements FlutterPwValidatorStrings {
  @override
  String atLeast = tr("At least - character");

  @override
  String normalLetters = "- Letter".tr();

  @override
  String uppercaseLetters = "- Uppercase letter".tr();

  @override
  String lowercaseLetters = "- Lowercase letter".tr();

  @override
  String numericCharacters = "- Numeric character".tr();

  @override
  String specialCharacters = "- Special character".tr();
}
