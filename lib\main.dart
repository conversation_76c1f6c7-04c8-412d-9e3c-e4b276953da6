import 'package:alderishop/controllers/addresses_controller.dart';
import 'package:alderishop/controllers/app_controller.dart';
import 'package:alderishop/controllers/auth_Controller.dart';
import 'package:alderishop/controllers/cart_controller.dart';
import 'package:alderishop/controllers/categores_controller.dart';
import 'package:alderishop/controllers/configration_controller.dart';
import 'package:alderishop/controllers/coupon_controller.dart';
import 'package:alderishop/controllers/favorites_controller.dart';
import 'package:alderishop/controllers/home_slider_controller.dart';
import 'package:alderishop/controllers/offer_controller.dart';
import 'package:alderishop/controllers/order_controller.dart';

import 'package:alderishop/controllers/products_controller.dart';
import 'package:alderishop/controllers/profile_controller.dart';
import 'package:alderishop/views/home/<USER>';

import 'package:alderishop/route/routs.dart';
import 'package:easy_localization/easy_localization.dart';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);
  await EasyLocalization.ensureInitialized();
  await AuthController.tryAutoLogin();
  runApp(
    EasyLocalization(
      supportedLocales: const [
        Locale('ar'),
        Locale('en'),
        Locale('tr'),
      ],
      fallbackLocale: Locale('ar'),
      path: 'assets/langs',
      saveLocale: true,
      child: MultiProvider(
        providers: [
          ChangeNotifierProvider<AuthController>(
              create: (ctx) => AuthController()),
          ChangeNotifierProvider<CouponController>(
              create: (ctx) => CouponController()),
          ChangeNotifierProvider<ProfileController>(
              create: (ctx) => ProfileController()),
          ChangeNotifierProvider<CategoryController>(
              create: (ctx) => CategoryController()),
          ChangeNotifierProvider<AddressesController>(
              create: (ctx) => AddressesController()),
          ChangeNotifierProvider<HomeSliderController>(
              create: (ctx) => HomeSliderController()),
          ChangeNotifierProvider<ProductsController>(
              create: (ctx) => ProductsController()),
          ChangeNotifierProvider<CartController>(
              create: (ctx) => CartController()),
          ChangeNotifierProvider<OffersController>(
              create: (ctx) => OffersController()),
          ChangeNotifierProvider<ConfigrationController>(
              create: (ctx) => ConfigrationController()),
          ChangeNotifierProvider<FavoritesController>(
              create: (ctx) => FavoritesController()),
          ChangeNotifierProvider<AppController>(
              create: (ctx) => AppController()),
          ChangeNotifierProvider<OrdersController>(
              create: (ctx) => OrdersController()),
        ],
        child: const MyApp(),
      ),
    ),
  );
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  @override
  void initState() {
    // AppController.setMq(context);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
      designSize: const Size(360, 690),
      minTextAdapt: true,
      splitScreenMode: true,
      builder: (BuildContext context, Widget? child) {
        AppController.setMq(context);
        return MaterialApp(
          navigatorKey: navigatorKey,
          debugShowCheckedModeBanner: false,
          theme: ThemeData(
            colorScheme: ColorScheme.fromSeed(seedColor: Colors.deepPurple),
            fontFamily: context.locale.languageCode == "ar"
                ? 'MontserratAR'
                : 'Montserrat',
          ),
          localizationsDelegates: context.localizationDelegates,

          supportedLocales: context.supportedLocales,
          locale: Locale("ar"), //context.locale,
          
          initialRoute: RouteHelper.home,
          // routes: RouteHelper.getRoutes(),
          home: Builder(builder: (context) {
            AppController.setMq(context);
            context.read<AppController>().currentLang = context.locale;
            context.read<AppController>().updateCurrentLangId();
            return const SplashScreen();
          }),
        );
      },
    );
  }
}
