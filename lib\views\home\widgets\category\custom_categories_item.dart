import 'package:alderishop/controllers/favorites_controller.dart';
import 'package:alderishop/data/api/api.dart';
import 'package:alderishop/data/model/Products/productModel/productListModel.dart';
import 'package:alderishop/data/model/categories/categroy_model.dart';
import 'package:alderishop/services/helper.dart';
import 'package:alderishop/views/categories/items/widgets/favoriteItems.dart';
import 'package:flutter/material.dart';
import 'package:alderishop/components/common_cache_image.dart';
// ignore: unused_import
import 'package:alderishop/controllers/app_controller.dart';
import 'package:alderishop/constants/constants.dart';
class CustomCategoryItem extends StatefulWidget {
  final ProductListModel? data;
  final CategoryModel? category;
  final AnimationController? animationController;
  final Animation<double>? animation;
  final bool isGrid;

  const CustomCategoryItem({
    Key? key,
    this.data,
    this.category,
    this.animationController,
    this.animation,
    this.isGrid = true,
  }) : super(key: key);

  @override
  State<CustomCategoryItem> createState() => _CustomCategoryItemState();
}

class _CustomCategoryItemState extends State<CustomCategoryItem> {
  int itemQuantity = 1;

  @override
  Widget build(BuildContext context) {
    FavoritesController.checkIfFromFavorites(widget.data?.id ?? 0);

    return Column(
      children: [
        SizedBox(

          child: Column(
            children: [
              Container(
                color: AppColors.WHITE_COLOR,
        width: AppController.W,
                child: CachedImage(
                 height: widget.isGrid ?  AppController.h * 0.24: AppController.h * 0.476,
                  fit: BoxFit.cover,
                  
                  width: double.infinity,
                  imageUrl: (widget.data?.images?.isNotEmpty ?? false)
                      ? "$baseUrl1/${widget.data!.images!.first}"
                      : "",
                ),
              ),
  
              Padding(
                padding:  EdgeInsets.symmetric(horizontal:  widget.isGrid ?10
                :30,vertical: 10),
                child: Column(
                  children: [
                    Row(
                      children: [
                        Flexible(
                          child: Text(
                            widget.data?.name ?? "",
                            overflow: TextOverflow.ellipsis,
                            style: TextStyle(
                              fontSize: 13,
                              color: AppColors.txtColor,
                              fontWeight: FontWeight.w600,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ],
                    ),
                  const SizedBox(height: 5,),
                    Container(
                      padding: const EdgeInsets.symmetric(vertical: 5, ),
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(5),
                          color: AppColors.container_color.withOpacity(0.3)),
                      child: Center(
                        child: Text(
                          "${widget.data?.price.toString()}",
                          style: TextStyle(
                            color: AppColors.BLACK_COLOR,
                            fontSize: 13,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                    Text(
                      '${widget.data?.discountPrice.toString()} IQD',
                      style: TextStyle(
                        color: AppColors.RED_COLOR,
                        fontSize: 11,
                        fontWeight: FontWeight.bold,
                        decoration: TextDecoration.lineThrough,
                        decorationColor: AppColors.RED_COLOR,
                        decorationThickness: 2.0,
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 7, vertical: 8),
                      child: Row(
                       mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Icon(
                            Icons.star,
                            color: AppColors.bg2,
                            size: 13,
                          ),
                          const SizedBox(width: 2),
                          Text(
                            "${widget.data?.averageRating}",
                            style: TextStyle(
                              color: AppColors.BLACK_COLOR,
                              fontSize: 10,
                            ),
                          ),
                          const SizedBox(width: 5,),
                          Text(
                            " ${widget.data?.totalReviews} ${T('Reviews')}",
                            style: TextStyle(
                              color: AppColors.blue,
                              fontSize: 10,
                            ),
                          ),
                    const      Spacer(),
                          Row(
                            children: [
                              const FavoriteItems(),
                              const SizedBox(width: 5),
                              Container(
                                height: 20,
                                width: 20,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(5),
                                  color: AppColors.PRIMARY_COLOR,
                                ),
                                child: Icon(
                                  size: 20,
                                  Icons.add,
                                  color: AppColors.WHITE_COLOR,
                                ),
                              ),
                            ],
                          )
                        ],
                      ),
                    ),
                  ],
                ),
              )
            ],
          ),
        ),
        const SizedBox(height: 2),
      ],
    );
  }
}
