import 'package:alderishop/components/common_snckbar.dart';
import 'package:alderishop/constants/constants.dart';
import 'package:alderishop/controllers/app_controller.dart';
import 'package:alderishop/controllers/coupon_controller.dart';
import 'package:alderishop/data/model/coupon_model.dart';
import 'package:alderishop/services/helper.dart';
import 'package:alderishop/views/auth/sign_in.dart';
import 'package:alderishop/views/home/<USER>/my_Coupons_Page.dart';
import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:page_transition/page_transition.dart';
import 'package:provider/provider.dart';
import 'package:ticket_clippers/ticket_clippers.dart';

// ignore: must_be_immutable
class CouponItemWidget extends StatelessWidget {
  CouponModel coupons;
  bool isMyCoupon;
  CouponItemWidget({super.key, required this.coupons,required this.isMyCoupon});

  @override
  Widget build(BuildContext context) {
    return ClipPath(
      clipper:  RoundedEdgeClipper(
  edge: Edge.horizontal,
  points: 10,
),

      child: Container(
        height: 140,
        color: Colors.transparent,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            ClipPath(
              clipper: RoundedEdgeClipper(
                edge: Edge.horizontal,
                points: 10,
              ),
              child: Container(
                width: 110,
                color: AppColors.SOFT_BLUE,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      height: 50,
                      width: 50,
                      decoration: BoxDecoration(
                        color: AppColors.WHITE_COLOR,
                        borderRadius: BorderRadius.circular(25),
                      ),
                    ),
                    const SizedBox(
                      height: 5,
                    ),
                    Text(
                      textAlign: TextAlign.center,
                      coupons.name ?? "",
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: AppColors.BLACK_COLOR,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            ClipPath(
              clipper: RoundedEdgeClipper(
                edge: Edge.horizontal,
                points: 10,
              ),
              child: Container(
                width: 210,
                color: AppColors.SOFT_BLUE,
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 12),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          Text(
                            coupons.discountType == 0
                                ? "%"
                                : "IQD",
                            style: TextStyle(
                              fontSize: 20,

                              color: AppColors.BLACK_COLOR,
                            ),
                          ),
                          const SizedBox(width: 1,),
                          Text(
                            coupons.discountType == 0
                                ? coupons.discount.toString()
                                : coupons.discount.toString(),
                            style: TextStyle(
                              fontSize: 35,
                              fontWeight: FontWeight.bold,
                              color: AppColors.BLACK_COLOR,
                            ),
                          ),
                        ],
                      ), 
                      Text(
                        T("Special discount coupon"),
                        style: TextStyle(
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                          color: AppColors.BLACK_COLOR,
                        ),
                      ),
                      const SizedBox(height: 6),
                     _buildOperationInfoRow(context,
 
  coupons.code ?? '',
 isMyCoupon:isMyCoupon ,
  showCopyButton: true,
  valueToCopy: coupons.code ?? '',),
   const SizedBox(height: 2),
            
                     Text(
                        "${T("Price")} : ${coupons.priceByPoints} Points" ,
                        style: TextStyle(
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                          color: AppColors.BLACK_COLOR,
                        ),
                      ), 
                 const SizedBox(
                      height: 5,
                    ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildOperationInfoRow(
      BuildContext context,  String value,
      {bool isTotal = false,
      bool showCopyButton = false,
      bool isMyCoupon = false,
      
      String? valueToCopy}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
  isMyCoupon?
  DottedBorder(
  color: AppColors.PRIMARY_COLOR, // or any color
  strokeWidth: 1,
  borderType: BorderType.RRect,
  radius: const Radius.circular(4),
  dashPattern: [4, 3],
  child: InkWell(
    onTap: () {
       if (showCopyButton && valueToCopy != null && valueToCopy.isNotEmpty) {
         Clipboard.setData(ClipboardData(text: valueToCopy));
       }
                successMsg(context: context, msg: "تم النسخ بنجاح");
    },
    child: Container(
      color: Colors.white,
      height: 36,
      width: AppController.W / 2.5,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            textAlign: TextAlign.center,
            value,
            style: TextStyle(
              color: AppColors.BLACK_COLOR,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.w500,
              fontSize: isTotal ? 16 : 14,
            ),
          ),
          if (showCopyButton && valueToCopy != null && valueToCopy.isNotEmpty)
            IconButton(
              constraints: BoxConstraints.tight(const Size(25, 25)),
              padding: EdgeInsets.zero,
              onPressed: () {
                Clipboard.setData(ClipboardData(text: valueToCopy));
                successMsg(context: context, msg: "تم النسخ بنجاح");
              },
              icon: Icon(
                Icons.copy,
                size: 16,
                color: AppColors.PRIMARY_COLOR.withOpacity(0.7),
              ),
              tooltip: T("Copy to clipboard"),
            ),
        ],
      ),
    ),
  ),
) :ElevatedButton(
            onPressed: () async {
             if( AppController.isAuth){
 pleaseWaitDialog(context: context, isShown: true);

  final result = await Provider.of<CouponController>(context, listen: false)
      .buyCoupon(coupons.id ?? 0,coupons.priceByPoints??0);

  pleaseWaitDialog(context: context, isShown: false);

  if (result.isSuccess == true) {
    successMsg(context: context, msg: T("Coupon purchased successfully"));

    await Provider.of<CouponController>(context, listen: false).getMyCoupons();

    final targetPage = AppController.isAuth
        ? const MyCouponsScreen()
        : const SignInPage();

    // ✅ Use Future.delayed to ensure navigation happens AFTER build
    Future.delayed(const Duration(seconds: 1), () {
      Navigator.of(context).push(
        PageTransition(
          type: AppController.currentLangId == 2
              ? PageTransitionType.leftToRight
              : PageTransitionType.rightToLeftWithFade,
          child: targetPage,
        ),
      );
    });
  } else {
    // ignore: use_build_context_synchronously
    errorMsg(context: context, title: T("تم شراء الكوبون من قبل"));
  }
             } else{
                   Navigator.of(context).push(
        PageTransition(
          type: AppController.currentLangId == 2
              ? PageTransitionType.leftToRight
              : PageTransitionType.rightToLeftWithFade,
          child: const SignInPage(),
        ),
      );
             }

 
},


                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF126C74),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(6),
                  ),
                  padding: const EdgeInsets.symmetric(
                      horizontal: 35, vertical: 6),
                  tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                ),
                child:  Text(
                  T("!BUY NOW"),
                  style:const TextStyle(
                    fontSize: 12,
                    color: Colors.white,
                  ),
                ),
              ),
        
      ],
    );
  }
}
