import 'package:alderishop/components/common_snckbar.dart';
import 'package:alderishop/components/layout.dart';
import 'package:alderishop/constants/constants.dart';
import 'package:alderishop/controllers/order_controller.dart';
import 'package:alderishop/data/model/order/order_model.dart';
import 'package:alderishop/services/helper.dart';
import 'package:alderishop/views/categories/items/items.dart';
import 'package:alderishop/views/categories/widgets/Header.dart';
import 'package:alderishop/views/home/<USER>';
import 'package:alderishop/views/orders/order_details_screen.dart';
import 'package:alderishop/views/orders/order_list_item_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

class MyOrderesScreen extends StatefulWidget {
  const MyOrderesScreen({super.key});

  @override
  State<MyOrderesScreen> createState() => _MyOrderesScreenState();
}

class _MyOrderesScreenState extends State<MyOrderesScreen>
    with AutomaticKeepAliveClientMixin, TickerProviderStateMixin {
  late AnimationController animationController;
  final ScrollController _scrollController = ScrollController();
  late RefreshController _refreshController;
  final TextEditingController _searchController = TextEditingController();
  String searchTerm = "";
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _refreshController = RefreshController(initialRefresh: false);
    animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _onRefresh();
  }

  @override
  void dispose() {
    animationController.dispose();
    _scrollController.dispose();
    _refreshController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  void _onRefresh() async {
    setState(() {
      _isLoading = true;
    });

    try {
      await Provider.of<OrdersController>(context, listen: false).getOrders();
      _refreshController.refreshCompleted();
    } catch (e) {
      _refreshController.refreshFailed();
      if (mounted) {
        errorMsg(context: context, title: T('Failed to load orders'));
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _onLoading() async {
    try {
      await Provider.of<OrdersController>(context, listen: false).getOrders();
      if (mounted) setState(() {});
      _refreshController.loadComplete();
    } catch (e) {
      _refreshController.loadFailed();
    }
  }

  // Filter orders based on search term
  List<Orders> _getFilteredOrders(List<Orders> orders) {
    if (searchTerm.isEmpty) {
      return orders;
    }

    return orders.where((order) {
      final orderCode = order.orderCode?.toLowerCase() ?? '';
      final orderStatus = order.orderStatus?.toString().toLowerCase() ?? '';
      final searchLower = searchTerm.toLowerCase();

      return orderCode.contains(searchLower) ||
          orderStatus.contains(searchLower);
    }).toList();
  }

  // Build search box widget
  Widget _buildSearchBox() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      padding: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: AppColors.BLACK_GREY.withOpacity(0.08),
        border: Border.all(
          color: AppColors.PRIMARY_COLOR.withOpacity(0.1),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.search,
            color: AppColors.PRIMARY_COLOR.withOpacity(0.6),
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: T("Search by order code or status"),
                hintStyle: TextStyle(
                  color: AppColors.BLACK_COLOR.withOpacity(0.5),
                  fontSize: 14,
                ),
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(vertical: 12),
              ),
              style: TextStyle(
                color: AppColors.BLACK_COLOR,
                fontSize: 14,
              ),
              onChanged: (value) {
                setState(() {
                  searchTerm = value;
                });
              },
            ),
          ),
          if (searchTerm.isNotEmpty)
            GestureDetector(
              onTap: () {
                _searchController.clear();
                setState(() {
                  searchTerm = "";
                });
              },
              child: Icon(
                Icons.clear,
                color: AppColors.PRIMARY_COLOR.withOpacity(0.6),
                size: 20,
              ),
            ),
        ],
      ),
    );
  }

  // Build empty state widget
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.shopping_bag_outlined,
            size: 80,
            color: AppColors.PRIMARY_COLOR.withOpacity(0.3),
          ),
          const SizedBox(height: 16),
          Text(
            searchTerm.isNotEmpty
                ? T("No orders found matching your search")
                : T("No orders found"),
            style: TextStyle(
              fontSize: 16,
              color: AppColors.BLACK_COLOR.withOpacity(0.6),
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            searchTerm.isNotEmpty
                ? T("Try adjusting your search terms")
                : T("Your orders will appear here once you make a purchase"),
            style: TextStyle(
              fontSize: 14,
              color: AppColors.BLACK_COLOR.withOpacity(0.4),
            ),
            textAlign: TextAlign.center,
          ),
          if (searchTerm.isEmpty) ...[
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pushReplacement(
                  MaterialPageRoute(builder: (context) => const HomePage()),
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.PRIMARY_COLOR,
                foregroundColor: Colors.white,
                padding:
                    const EdgeInsets.symmetric(horizontal: 32, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text(T("Start Shopping")),
            ),
          ],
        ],
      ),
    );
  }

  // Build loading state
  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(AppColors.PRIMARY_COLOR),
          ),
          const SizedBox(height: 16),
          Text(
            T("Loading your orders..."),
            style: TextStyle(
              color: AppColors.BLACK_COLOR.withOpacity(0.6),
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  // Build orders list
  Widget _buildOrdersList(List<Orders> filteredOrders) {
    return SmartRefresher(
      enablePullDown: true,
      enablePullUp: true,
      controller: _refreshController,
      header: WaterDropHeader(
        waterDropColor: AppColors.PRIMARY_COLOR,
      ),
      footer: CustomFooter(
        builder: (context, mode) {
          Widget body;
          if (mode == LoadStatus.idle) {
            body = Text(T("Pull up to load"));
          } else if (mode == LoadStatus.failed) {
            body = Text(T("Load Failed! Click retry!"));
          } else if (mode == LoadStatus.canLoading) {
            body = Text(T("Release to load more"));
          } else {
            body = Text(T("No more data"));
          }
          return SizedBox(
            height: 55.0,
            child: Center(child: body),
          );
        },
      ),
      onRefresh: _onRefresh,
      onLoading: _onLoading,
      child: ListView.separated(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        itemCount: filteredOrders.length,
        separatorBuilder: (context, index) => const SizedBox(height: 12),
        itemBuilder: (context, index) {
          final order = filteredOrders[index];

          // Create animation for each item
          final animation = Tween<double>(begin: 0.0, end: 1.0).animate(
            CurvedAnimation(
              parent: animationController,
              curve: Interval(
                (1 / filteredOrders.length) * index,
                1.0,
                curve: Curves.fastOutSlowIn,
              ),
            ),
          );

          animationController.forward();

          return AnimatedBuilder(
            animation: animation,
            builder: (context, child) {
              return Transform.translate(
                offset: Offset(0, 50 * (1 - animation.value)),
                child: Opacity(
                  opacity: animation.value,
                  child: InkWell(
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (_) => OrderDetailsPage(
                            myOrderModel: order,
                          ),
                        ),
                      );
                    },
                    borderRadius: BorderRadius.circular(12),
                    child: OrderListItemWidget(
                      orders: order,
                    ),
                  ),
                ),
              );
            },
          );
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    return ApplicationLayout(
      selectedBottomNavbarItem: BottomNavbarItems.home,
      content: Column(
        children: [
          Expanded(
            child: NestedScrollView(
              controller: _scrollController,
              headerSliverBuilder: (context, innerBoxIsScrolled) => [
                SliverPersistentHeader(
                  pinned: true,
                  floating: true,
                  delegate: ContestTabHeader(
                    CategoriesHeader(
                      onTap: () {
                        Navigator.of(context).push(MaterialPageRoute(
                          builder: (context) => const HomePage(),
                        ));
                      },
                      text: T("My Orders"),
                    ),
                  ),
                ),
              ],
              body: Container(
                color: Colors.white,
                child: Column(
                  children: [
                    // Search Box
                    _buildSearchBox(),

                    // Orders Content
                    Expanded(
                      child: Consumer<OrdersController>(
                        builder: (context, orderController, child) {
                          final UserOrderModel? userOrders =
                              orderController.userOrders;
                          final orders = userOrders?.orders ?? [];
                          final filteredOrders = _getFilteredOrders(orders);

                          // Show loading state
                          if (_isLoading && orders.isEmpty) {
                            return _buildLoadingState();
                          }

                          // Show empty state
                          if (orders.isEmpty && !_isLoading) {
                            return _buildEmptyState();
                          }

                          // Show filtered empty state
                          if (filteredOrders.isEmpty && searchTerm.isNotEmpty) {
                            return _buildEmptyState();
                          }

                          // Show orders list
                          return _buildOrdersList(filteredOrders);
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  bool get wantKeepAlive => true;
}
