import 'package:alderishop/components/common_snckbar.dart';
import 'package:alderishop/components/layout.dart';
import 'package:alderishop/constants/constants.dart';
import 'package:alderishop/controllers/order_controller.dart';
import 'package:alderishop/data/model/order/order_model.dart';
import 'package:alderishop/services/helper.dart';
import 'package:alderishop/views/categories/items/items.dart';
import 'package:alderishop/views/categories/widgets/Header.dart';
import 'package:alderishop/views/home/<USER>';
import 'package:alderishop/views/orders/order_details_screen.dart';
import 'package:alderishop/views/orders/order_list_item_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

class MyOrderesScreen extends StatefulWidget {
  const MyOrderesScreen({super.key});

  @override
  State<MyOrderesScreen> createState() => _MyOrderesScreenState();
}

class _MyOrderesScreenState extends State<MyOrderesScreen>
    with AutomaticKeepAliveClientMixin, TickerProviderStateMixin {
  late AnimationController animationController;
  final ScrollController _scrollController = ScrollController();
  late RefreshController _refreshController;
  String searchTerm = "";


  @override
  void initState() {
    super.initState();
    _refreshController = RefreshController(initialRefresh: false);
    animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _onRefresh();
  }

  @override
  void dispose() {
    animationController.dispose();
    _scrollController.dispose();
    _refreshController.dispose();
    super.dispose();
  }

  void _onRefresh() async {
   
    await Provider.of<OrdersController>(context, listen: false).getOrders();
    _refreshController.refreshCompleted();
   
  }

  void _onLoading() async {
    await Provider.of<OrdersController>(context, listen: false).getOrders();
    if (mounted) setState(() {});
    _refreshController.loadComplete();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final orderController = Provider.of<OrdersController>(context);
    final UserOrderModel? userOrders = orderController.userOrders;
  final orders = userOrders?.orders ?? [];
orders.where((order) {
  return order.orderCode?.toLowerCase().contains(searchTerm.toLowerCase()) ?? false;
}).toList();

    return ApplicationLayout(
      selectedBottomNavbarItem: BottomNavbarItems.home,
      content: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 5),
        child: Column(
          children: [
            Expanded(
              child: NestedScrollView(
                controller: _scrollController,
                headerSliverBuilder: (context, innerBoxIsScrolled) => [
                  SliverPersistentHeader(
                    pinned: true,
                    floating: true,
                    delegate: ContestTabHeader(
                      CategoriesHeader(
                        onTap: () {
                          Navigator.of(context).push(MaterialPageRoute(
                            builder: (context) => const HomePage(),
                          ));
                        },
                        text: T("My Orders"),
                      ),
                    ),
                  ),
                ],
                body: Container(
                  color: Colors.white,
                  padding: const EdgeInsets.symmetric(horizontal: 5),
                  child: Column(
                    children: [
                      // Search Box
                      Padding(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 20, vertical: 5),
                        child: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 20),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(10),
                            color: AppColors.BLACK_GREY.withOpacity(0.1),
                          ),
                          child: Row(
                            children: [
                              Expanded(
                                child: TextField(
                                  decoration: InputDecoration(
                                    hintText: T("Search My Orders"),
                                    hintStyle: TextStyle(
                                      color: AppColors.BLACK_COLOR,
                                      fontSize: 12,
                                    ),
                                    border: InputBorder.none,
                                  ),
                                  style: TextStyle(
                                    color: AppColors.BLACK_COLOR,
                                    fontSize: 12,
                                  ),
                                  onChanged: (value) {
                                    setState(() {
                                      searchTerm = value;
                                    });
                                  },
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
        
               const SizedBox(height: 15,)
        ,                      Expanded(
                        child:
                        (userOrders == null || userOrders.orders == null)
              ?  Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              pleaseWaitDialog(context: context, isShown: true,),
          
             
            ],
          ),
        )
              : SmartRefresher(
                          enablePullDown: true,
                          enablePullUp: true,
                          controller: _refreshController,
                          header: const WaterDropHeader(),
                          footer: CustomFooter(
                            builder: (context, mode) {
                              Widget body;
                              if (mode == LoadStatus.idle) {
                                body = Text(T("Pull up to load"));
                              } else if (mode == LoadStatus.failed) {
                                body = Text(T("Load Failed! Click retry!"));
                              } else if (mode == LoadStatus.canLoading) {
                                body = Text(T("Release to load more"));
                              } else {
                                body = Text(T("No more data"));
                              }
                              return SizedBox(
                                height: 55.0,
                                child: Center(child: body),
                              );
                            },
                          ),
                          onRefresh: _onRefresh,
                          onLoading: _onLoading,
                          child: AlignedGridView.count(
                            crossAxisCount: 1,
                            mainAxisSpacing: 12,
                            crossAxisSpacing: 0,
                            physics: const NeverScrollableScrollPhysics(),
                            scrollDirection: Axis.vertical,
                            shrinkWrap: true,
                            itemCount: userOrders.orders?.length ?? 0,
                            itemBuilder: (context, index) {
                              final order = userOrders.orders![index];
                             
                              
                              animationController.forward();
        
                              return InkWell(
                                onTap: () {
                                  Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                      builder: (_) => OrderDetailsPage(
                                        myOrderModel: order,
                                      ),
                                    ),
                                  );
                                },
                                child: Padding(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 5, vertical: 0),
                                  child: OrderListItemWidget(orders: order,
                                ),
                              ));
                            },
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
    
      ),
    );
  }

  @override
  bool get wantKeepAlive => true;
}
