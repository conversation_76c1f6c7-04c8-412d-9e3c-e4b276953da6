import 'package:alderishop/services/helper.dart';
import 'package:alderishop/views/profile/widgets/common_switch_button.dart';
import 'package:flutter/material.dart';

class FilterByReviews extends StatefulWidget {
  final Function(int) onStarCountSelected;

  const FilterByReviews({Key? key, required this.onStarCountSelected})
      : super(key: key);

  @override
  State<FilterByReviews> createState() => _FilterByReviewsState();
}

class _FilterByReviewsState extends State<FilterByReviews> {
  int selectedStars = 0;
  bool switchEnabled = false;

  void selectStar(int starCount) {
    if (switchEnabled) {
      setState(() {
        selectedStars = starCount;
        widget.onStarCountSelected(selectedStars); // Notify parent widget
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Row(
          children: [
            Text(
              T("Filter by Review"),
              style: const TextStyle(
                fontSize: 12,
                color: Colors.black,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(width: 10),
            Row(
              children: List.generate(
                5,
                (index) => GestureDetector(
                  onTap: () {
                    selectStar(index + 1);
                  },
                  child: Container(
                    width: 20, // Set a fixed width for each star
                    height: 20, // Set a fixed height for each star
                    alignment: Alignment.center,
                    child: Image.asset(
                      index < selectedStars
                          ? 'assets/img/star.png'
                          : 'assets/img/star_gray.png',
                      height: 15,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
        CommonSwitchButton(onChanged: (value) {
          setState(() {
            switchEnabled = value;
            if (!value) {
              selectedStars = 0;
              widget.onStarCountSelected(selectedStars);
            }
          });
        }),
      ],
    );
  }
}
