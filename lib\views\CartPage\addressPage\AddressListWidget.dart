import 'package:alderishop/data/model/addresses_model.dart';
import 'package:alderishop/views/CartPage/addressPage/widget/address_list_items_widget.dart';
import 'package:flutter/material.dart';

class AddressListWidget extends StatefulWidget {
  const AddressListWidget({
    super.key,
    required this.data,
  });

  final List<AddressesModel> data;

  @override
  State<AddressListWidget> createState() => _AddressListWidgetState();
}

class _AddressListWidgetState extends State<AddressListWidget> {
  int? selectedAddressId;
  bool showSelectedAddress = false;

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    if (showSelectedAddress && selectedAddressId != null) {
      final selectedAddress =
          widget.data.firstWhere((address) => address.id == selectedAddressId);
      return AddressListItemsWidget(
        model: selectedAddress,
        isSelected: true,
        onChange: (id) {
          selectedAddressId = id;
          print(selectedAddressId);
        },
      );
    }

    // Default view: Address list
    return ListView.builder(
      physics: const NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      itemCount: widget.data.length,
      itemBuilder: (context, index) {
        final isSelected = selectedAddressId == widget.data[index].id;
        return InkWell(
          onTap: () {
            print("ssssssssss$selectedAddressId");
            setState(() {
              selectedAddressId = widget.data[index].id;
              showSelectedAddress = true; // Show only selected address
            });
          },
          child: AddressListItemsWidget(
            model: widget.data[index],
            isSelected: isSelected,
            onChange: (id) {
              setState(() {
                selectedAddressId = id;
              });
            },
          ),
        );
      },
    );
  }
}
