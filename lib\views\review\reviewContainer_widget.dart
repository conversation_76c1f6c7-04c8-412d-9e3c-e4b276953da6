import 'package:alderishop/constants/constants.dart';
import 'package:alderishop/views/home/<USER>/CustomText.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ReviewColorsWidget extends StatefulWidget {
  final int? selectedId;
  final String txt;
  final IconData icon;
  final int id;
  final Function onChangeId;
  const ReviewColorsWidget(
      {super.key,
      required this.txt,
      this.selectedId,
      required this.id,
      required this.onChangeId,
      required this.icon});

  @override
  State<ReviewColorsWidget> createState() => _SizeColorsWidgetState();
}

class _SizeColorsWidgetState extends State<ReviewColorsWidget> {
  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        setState(() {
          widget.onChangeId(widget.id);
        });
      },
      child: Container(
        width: 40.w,
        margin: const EdgeInsets.all(5),
        decoration: BoxDecoration(
          color: widget.id == widget.selectedId
              ? AppColors.PRIMARY_COLOR
              : AppColors.WHITE_COLOR,
          borderRadius: BorderRadius.circular(5.r),
          border: Border.all(color: AppColors.BLACK_GREY),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CustomText(
              text: widget.txt,
              fontSize: 12.sp,
              fontWeight: FontWeight.w400,
              color: widget.id == widget.selectedId
                  ? AppColors.WHITE_COLOR
                  : AppColors.PRIMARY_COLOR,
            ),
            Icon(
              widget.icon,
              color: AppColors.bg1,
              size: 14,
            )
          ],
        ),
      ),
    );
  }
}
