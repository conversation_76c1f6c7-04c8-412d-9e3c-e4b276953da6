import 'package:alderishop/data/api/api.dart';
import 'package:alderishop/data/model/addresses_model.dart';
import 'package:flutter/material.dart';

class AddressesController with ChangeNotifier {
  List<AddressesModel> addresses = [];
  List<AddressesModel> editAddress = [];
  final int _pageSize = 20;
  int pageNumber() {
    return (-addresses.length ~/ _pageSize) + 1;
  }

  Future<void> getAddresses({
    bool resetAndRefresh = true,
  }) async {
    try {
      var url = 'api/Address/GetAddresesForUser';
      var result = await Api.getOne(
        action: url,
      );
      addresses.clear();
      if (result != null && result.data['data'] != null) {
        for (var element in result.data['data']) {
          var data = AddressesModel.fromJson(element);
          addresses.add(data);
        }
      }

      notifyListeners();
    } catch (e) {
      print("Error loading addresses: $e");
    }
  }

  //----------------------------------------------------------------------------
  Future<bool> addAddress({
    required AddressesModel model,
    bool resetAndRefresh = false,
  }) async {
    try {
      model.id = 0;
      model.userId = 0;
      var url = 'api/Address/CreateNewAddress';
      var result = await Api.post(action: url, body: model.toJson());
      if (result != null) {
        addAddressToProviderAfterSuccess(model);
        await getAddresses();
        notifyListeners();
        return true;
      }
      return false;
    } catch (e) {
      print(e);
      return false;
    }
  }

  //------------------------------------------------------------------
  Future<bool> updateAddress({
    required AddressesModel addresses,
    bool resetAndRefresh = false,
  }) async {
    try {
      addresses.state = "";
      var url = 'api/Address/UpdateAddress';
      var result = await Api.put(action: url, body: addresses.toJson());
      if (result != null) {
        if (result.isSuccess == true) {
          mapAddressToProviderAfterSuccess(addresses);
          notifyListeners();
          return true;
        } else {
          return false;
        }
      }
      return false;
    } catch (e) {
      print(e);
      return false;
    }
  }
  //----------------------------------------------------------------------------

  Future<bool> deleteAddress({
    required int id,
    bool resetAndRefresh = false,
  }) async {
    try {
      var url = 'api/Address/DeleteAddress?id=$id';
      var result = await Api.delete(action: url);
      if (result != null) {
        if (result.isSuccess == true) {
          if (addresses.any((element) => element.id == id)) {
            addresses.removeWhere((element) => element.id == id);
          }

          notifyListeners();
          return true;
        } else {
          return false;
        }
      }
      return false;
    } catch (e) {
      print(e);
      return false;
    }
  }
  //----------------------------------------------------------------------------

  void mapAddressToProviderAfterSuccess(AddressesModel model) {
    var oldModelIndex =
        addresses.indexWhere((element) => element.id == model.id);
    if (oldModelIndex != -1) {
      addresses[oldModelIndex] = model;
    }
  }
  //----------------------------------------------------------------------------

  void addAddressToProviderAfterSuccess(AddressesModel model) {
    if (model.id != null) {
      if ((model.id ?? 0) > 0) {
        addresses.add(model);
      }
    }
  }
}
