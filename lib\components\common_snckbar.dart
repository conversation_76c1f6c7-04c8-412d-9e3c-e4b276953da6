//=====================//=====================//=====================//=====================

import 'dart:async';
import 'dart:ui';

import 'package:alderishop/constants/constants.dart';
import 'package:alderishop/controllers/app_controller.dart';

import 'package:alderishop/main.dart';
import 'package:alderishop/services/helper.dart';

import 'package:alderishop/views/home/<USER>/CustomText.dart';

import 'package:another_flushbar/flushbar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:qr_flutter/qr_flutter.dart';

pleaseWaitDialog({required BuildContext context, required bool isShown}) {
  if (!isShown) {
    Navigator.of(context, rootNavigator: true).pop();
  } else {
    showDialog<void>(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 6, sigmaY: 6),
          child: AlertDialog(
            backgroundColor: AppColors.WHITE_COLOR,
            surfaceTintColor: Colors.transparent,
            shape: const RoundedRectangleBorder(
                borderRadius: BorderRadius.all(Radius.circular(20))),
            icon: SizedBox(
              width: AppController.W - 40,
              height: 70,
              child: SvgPicture.asset(
        'assets/img/new/logo.svg',
       
      ),
            ),
            iconColor: AppColors.WHITE_COLOR,
            // <-- SEE HERE
            title: Text(
              T("Please wait"),
              style: TextStyle(color: AppColors.PRIMARY_COLOR),
            ),
          ),
        );
      },
    );
  }
}

//=====================//=====================//=====================//=====================
void successMsg({required BuildContext context, String? title, String? msg}) {
  Flushbar(
    title: title ?? T('success'),
    message: msg ?? T('Successfuly Saved'),
    // mainButton: InkWell(
    //   onTap: () {},
    //   child: Icon(Icons.close),
    // ),
    icon: Icon(Icons.info_outline, size: 28.0, color: Colors.green[300]),
    margin: const EdgeInsets.all(6.0),
    flushbarStyle: FlushbarStyle.FLOATING,
    flushbarPosition: FlushbarPosition.TOP,
    textDirection: Directionality.of(context),
    borderRadius: BorderRadius.circular(12),
    duration: const Duration(seconds: 2),
    leftBarIndicatorColor: Colors.green[300],
  ).show(context);
}

//=====================//=====================//=====================//=====================
void errorMsg(
    {required BuildContext context, required String title, String? msg}) {
  Flushbar(
    title: title,
    message: msg ?? T('Operation has not completed successfuly!'),
    // mainButton: InkWell(
    //   onTap: () {},
    //   child: Icon(Icons.close),
    // ),
    titleSize: 16,
    messageSize: 12,
    icon: Icon(Icons.info_outline, size: 28.0, color: AppColors.RED_COLOR),
    margin: const EdgeInsets.only(left: 15, right: 15, bottom: 10, top: 30),
    flushbarStyle: FlushbarStyle.FLOATING,
    flushbarPosition: FlushbarPosition.TOP,
    backgroundColor: AppColors.SECOUND_COLOR,
    textDirection: Directionality.of(context),
    borderRadius: BorderRadius.circular(12),
    duration: const Duration(seconds: 3),
    titleColor: AppColors.BLACK_COLOR,
    messageColor: AppColors.BLACK_COLOR,

    leftBarIndicatorColor: AppColors.RED_COLOR,
  ).show(context);
}

//=====================//=====================//=====================//=====================
void showQrDialog({
  required BuildContext context,
  String? qrData,
  DateTime? createdAt,
}) {
  final expiryTime = createdAt?.add(const Duration(hours: 24));
  final initialTimeLeft = expiryTime != null
      ? expiryTime.difference(DateTime.now())
      : Duration.zero;

  showDialog(
    context: context,
    builder: (BuildContext context) {
      return _QrDialog(
        qrData: qrData,
        timeLeft: initialTimeLeft,
      );
    },
  );
}
//=====================//=====================//=====================//=====================
class _QrDialog extends StatefulWidget {
  final String? qrData;
  final Duration timeLeft;

  const _QrDialog({
    super.key,
    required this.qrData,
    required this.timeLeft,
  });

  @override
  State<_QrDialog> createState() => _QrDialogState();
}
class _QrDialogState extends State<_QrDialog> {
  late Duration _timeLeft;
  Timer? _timer;

  @override
  void initState() {
    super.initState();
    _timeLeft = widget.timeLeft;

    if (_timeLeft > Duration.zero) {
      _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
        setState(() {
          _timeLeft -= const Duration(seconds: 1);
          if (_timeLeft <= Duration.zero) {
            _timeLeft = Duration.zero; // Clamp to zero
            _timer?.cancel();
          }
        });
      });
    }
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    return "${twoDigits(duration.inHours)}:"
           "${twoDigits(duration.inMinutes % 60)}:"
           "${twoDigits(duration.inSeconds % 60)}";
  }

  @override
  Widget build(BuildContext context) {
    final expired = _timeLeft <= Duration.zero;

    return Stack(
      children: [
        BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 5.0, sigmaY: 5.0),
          child: Container(
            height: AppController.h,
            width: AppController.W,
            color: Colors.white.withOpacity(0.2),
          ),
        ),
        AlertDialog(
          backgroundColor: Colors.transparent,
          content: Container(
            margin: EdgeInsets.zero,
            padding: EdgeInsets.zero,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              color: Colors.transparent,
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  decoration: BoxDecoration(
                    color: AppColors.BLACK_COLOR,
                    borderRadius: const BorderRadius.only(
                      topRight: Radius.circular(10),
                      topLeft: Radius.circular(10),
                    ),
                  ),
                  height: expired ? 100 : 260,
                  width: 200,
                  child: Center(
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(5),
                      child: Padding(
                        padding: const EdgeInsets.only(top: 10),
                        child: Container(
                          color: Colors.white,
                          child: expired
                              ? Container(
                                  color: AppColors.BLACK_COLOR,
                                  alignment: Alignment.center,
                                  child: Text(
                                    T("QR Code Expired"),
                                    style: const TextStyle(
                                        color: Colors.white, fontSize: 16),
                                    textAlign: TextAlign.center,
                                  ),
                                )
                              : Column(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    QrImageView(
                                      padding: const EdgeInsets.all(9),
                                      data: widget.qrData ?? "",
                                      version: QrVersions.auto,
                                      backgroundColor: AppColors.WHITE_COLOR,
                                      size: 180.0,
                                      gapless: true,
                                    ),
                                    SizedBox(height: 5),
                                    Text(
                                      widget.qrData ?? "",
                                      style: TextStyle(
                                          color: AppColors.BLACK_COLOR,
                                          fontSize: 14),
                                      textAlign: TextAlign.center,
                                    ),
                                    SizedBox(height: 8),
                                    Text(
                                      _formatDuration(_timeLeft),
                                      style: const TextStyle(
                                          fontSize: 14,
                                          fontWeight: FontWeight.bold,
                                          color: Colors.redAccent),
                                    ),
                                  ],
                                ),
                        ),
                      ),
                    ),
                  ),
                ),
                Container(
                  width: 200,
                  decoration: BoxDecoration(
                      color: AppColors.BLACK_COLOR,
                      borderRadius: const BorderRadius.only(
                          bottomLeft: Radius.circular(10),
                          bottomRight: Radius.circular(10))),
                  child: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Text(
                      textAlign: TextAlign.center,
                      expired ? "" : T("SCAN ME"),
                      style: const TextStyle(fontSize: 28, color: Colors.white),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}

//=====================//=====================//=====================//=====================
void doneMsg({required BuildContext context, int? orderId}) {
  showDialog(
    context: context,
    builder: (context) => AlertDialog(
      shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.all(Radius.circular(10.0))),
      insetPadding: const EdgeInsets.symmetric(horizontal: 75),
      content: SizedBox(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CustomText(
              text: T('Your order has been received'),
              fontSize: 13,
              fontWeight: FontWeight.w600,
              color: AppColors.BLACK_COLOR,
            ),
            SizedBox(height: AppController.h * 0.01),
            CustomText(
              text: T('Thank you for your purchase!'),
              fontSize: 12,
              fontWeight: FontWeight.w400,
              color: AppColors.BLACK_GREY,
            ),
            SizedBox(height: AppController.h * 0.01),
            Image.asset(
              'assets/img/done.png',
              height: 50,
            ),
            SizedBox(height: AppController.h * 0.01),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CustomText(
                  text: T('Your order number:'),
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: AppColors.BLACK_GREY,
                ),
                CustomText(
                  text: orderId.toString(),
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: AppColors.RED_COLOR,
                ),
              ],
            ),
          ],
        ),
      ),
    ),
  );

  Future.delayed(const Duration(seconds: 3), () {
    Navigator.of(context).pop(); // Dismiss the dialog after 3 seconds
  });
}

//===============================================================
Future<bool?> showConfirmDialog({
  String? title,
  int? orderId,
  String? content,
  String? backText,
  String? confirmText,
}) async {
  title ??= T('Confirm Operation');
  content ??= T('');

  return showDialog<bool>(
    context: navigatorKey.currentContext!,
    builder: (BuildContext context) {
      return AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        contentPadding: EdgeInsets.zero,
        backgroundColor: AppColors.PRIMARY_COLOR,
        title: Text(
          title!,
          textAlign: TextAlign.center,
          style: TextStyle(
              color: AppColors.WHITE_COLOR,
              fontWeight: FontWeight.normal,
              fontSize: 14),
        ),
        content: SingleChildScrollView(
          child: ListBody(
            children: [
              Text(content!),
            ],
          ),
        ),
        actions: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                decoration: BoxDecoration(
                    color: AppColors.RED_COLOR,
                    borderRadius: BorderRadius.circular(12)),
                width: AppController.W / 3.2,
                child: TextButton(
                  child: Text(
                    confirmText ?? T('Confirm'),
                    style: TextStyle(
                        color: AppColors.WHITE_COLOR,
                        fontWeight: FontWeight.bold),
                  ),
                  onPressed: () {
                    Navigator.of(context).pop(true);
                  },
                ),
              ),
              const SizedBox(
                width: 10,
              ),
              Container(
                width: AppController.W / 3.2,
                decoration: BoxDecoration(
                    color: AppColors.brown,
                    borderRadius: BorderRadius.circular(12)),
                child: TextButton(
                  child: Text(
                    backText ?? T('Cancel'),
                    style: TextStyle(
                        color: AppColors.WHITE_COLOR,
                        fontWeight: FontWeight.bold),
                  ),
                  onPressed: () {
                    Navigator.of(context).pop(false);
                  },
                ),
              ),
            ],
          ),
        ],
      );
    },
  );
}
