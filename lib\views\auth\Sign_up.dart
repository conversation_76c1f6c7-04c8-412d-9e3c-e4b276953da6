import 'package:alderishop/components/CustomInputField.dart';
import 'package:alderishop/components/common_snckbar.dart';
import 'package:alderishop/constants/constants.dart';
import 'package:alderishop/controllers/app_controller.dart';
import 'package:alderishop/controllers/auth_Controller.dart';
import 'package:alderishop/data/model/auth_model.dart';
import 'package:alderishop/services/helper.dart';
import 'package:alderishop/views/profile/widgets/customContanier.dart';
import 'package:alderishop/views/auth/sign_in.dart';
import 'package:alderishop/components/common_text_field.dart';
import 'package:alderishop/views/home/<USER>/%C4%B0magebgWidget.dart';
import 'package:alderishop/views/home/<USER>/CustomText.dart';
import 'package:flutter/material.dart';
import 'package:flutter_pw_validator/flutter_pw_validator.dart';
import 'package:provider/provider.dart';

class SignUp extends StatefulWidget {
  const SignUp({super.key});

  @override
  State<SignUp> createState() => _SignUpState();
}

class _SignUpState extends State<SignUp> {
  final TextEditingController _passwordController = TextEditingController();
  final GlobalKey<FlutterPwValidatorState> validatorKey =
      GlobalKey<FlutterPwValidatorState>();
  bool passwordValid = false;
  DateTime? selectedDate;
  var model = RegisterModel();
  int selectedAccountType = 1;
  String? dateOfBirthDisplay;

  void _showErrorSnackBar(String fieldName) {
    String errorMessage = AppController.currentLangId == 2
        ? '${T("Please")} ${T("write")} $fieldName '
        : '${T("Please")} $fieldName ${T("write")}';
    errorMsg(context: context, title: T("Error"), msg: errorMessage);
  }

  void _signUp() async {
    if (model.firstName == null) {
      _showErrorSnackBar(T('First Name'));
    } else if (model.lastName == null) {
      _showErrorSnackBar(T('Last Name'));
    } else if (model.dateOfBirth == null) {
      _showErrorSnackBar(T('Date of Birth'));
    } else if (model.gender == null) {
      // Show a snackbar, dialog, or inline error
      _showErrorSnackBar('Please select a gender.');
    } else if (model.email == null) {
      // Show a snackbar, dialog, or inline error
      _showErrorSnackBar(T('Email'));
    } else if (model.phoneNumber == null) {
      _showErrorSnackBar(T('Phone'));
    } else if (model.password == null) {
      _showErrorSnackBar(T('Password'));
    } else if (selectedAccountType != 1 && selectedAccountType != 2) {
      _showErrorSnackBar(T('Account type'));
    } else if (!passwordValid) {
      _showErrorSnackBar(T('Password in the required format'));

      return; // Stop sign-up
    } else {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(
          child: CircularProgressIndicator(),
        ),
      );
      model.userType = UserTypes.values[selectedAccountType];
      var result = await Provider.of<AuthController>(context, listen: false)
          .register(context, model);

      // ignore: use_build_context_synchronously

      if (result.isSuccess) {
        Navigator.of(context).push(
          MaterialPageRoute(builder: (context) => const SignInPage()),
        );
        // ignore: use_build_context_synchronously
        successMsg(context: context, msg: T("Registration is completed"));
        // ignore: use_build_context_synchronously
      } else {
        // ignore: unrelated_type_equality_checks
        if (result.errors == T('User already exists')) {
          // ignore: use_build_context_synchronously
          errorMsg(
            title: "",
            context: context,
            msg: T(
                'This email has already been used. Please use a different email.'),
          );
        } else {
          // ignore: use_build_context_synchronously
          errorMsg(
            title: "",
            context: context,
            msg: T('The information has already been registered before'),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
        child: Column(
          children: [
            ImageBgWidget(
              height: 260,
            ),
            SizedBox(
              height: AppController.h * 0.05,
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 50),
              child: Column(
                children: [
                  Center(
                    child: CustomText(
                        text: T('New User'),
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: AppColors.BLACK_COLOR),
                  ),
                  SizedBox(
                    height: AppController.h * 0.01,
                  ),
                  
                CustomInputField(
  hintText: T('Full Name'),
  backgroundColor: const Color(0xFFB3E5FC).withOpacity(0.4),
  onChanged: (value) {
    setState(() {
      final parts = value.split(' ');
      if (parts.isNotEmpty) {
        model.firstName = parts[0];
        model.lastName = parts.length > 1 ? parts.sublist(1).join(' ') : ''; 
      } else {
        model.firstName = '';
        model.lastName = '';
      }
    });
  },
),

                  SizedBox(
                    height: AppController.h * 0.01,
                  ),
                  // Row(
                  //   children: [
                  //     Expanded(
                  //       child: GestureDetector(
                  //         onTap: () {
                  //           setState(() {
                  //             model.gender = Gender.male;
                  //           });
                  //           print(
                  //               "Selected gender index: ${model.gender!.index}");
                  //         },
                  //         child: Container(
                  //           padding: const EdgeInsets.symmetric(vertical: 12),
                  //           decoration: BoxDecoration(
                  //             color: model.gender == Gender.male
                  //                 ? AppColors.brown.withOpacity(0.8)
                  //                 : Colors.white,
                  //             borderRadius: BorderRadius.circular(8),
                  //             border: Border.all(
                  //                 color: model.gender == Gender.male
                  //                     ? AppColors.brown
                  //                     : Colors.grey),
                  //           ),
                  //           alignment: Alignment.center,
                  //           child: Row(
                  //             mainAxisAlignment: MainAxisAlignment.center,
                  //             children: [
                  //               Icon(
                  //                 Icons.male,
                  //                 color: model.gender == Gender.male
                  //                     ? Colors.white
                  //                     : AppColors.PRIMARY_COLOR,
                  //               ),
                  //               const SizedBox(width: 6),
                  //               CustomText(
                  //                 text: T('Male'),
                  //                 fontSize: 14,
                  //                 fontWeight: FontWeight.w500,
                  //                 color: model.gender == Gender.male
                  //                     ? Colors.white
                  //                     : AppColors.BLACK_COLOR,
                  //               ),
                  //             ],
                  //           ),
                  //         ),
                  //       ),
                  //     ),
                  //     const SizedBox(width: 14),
                  //     Expanded(
                  //       child: GestureDetector(
                  //         onTap: () {
                  //           setState(() {
                  //             model.gender = Gender.female;
                  //           });
                  //         },
                  //         child: Container(
                  //           padding: const EdgeInsets.symmetric(vertical: 12),
                  //           decoration: BoxDecoration(
                  //             color: model.gender == Gender.female
                  //                 ? AppColors.brown.withOpacity(0.8)
                  //                 : Colors.white,
                  //             borderRadius: BorderRadius.circular(8),
                  //             border: Border.all(
                  //                 color: model.gender == Gender.female
                  //                     ? AppColors.brown
                  //                     : Colors.grey),
                  //           ),
                  //           alignment: Alignment.center,
                  //           child: Row(
                  //             mainAxisAlignment: MainAxisAlignment.center,
                  //             children: [
                  //               Icon(
                  //                 Icons.female,
                  //                 color: model.gender == Gender.female
                  //                     ? Colors.white
                  //                     : AppColors.PRIMARY_COLOR,
                  //               ),
                  //               const SizedBox(width: 6),
                  //               CustomText(
                  //                 text: T('Female'),
                  //                 fontSize: 14,
                  //                 fontWeight: FontWeight.w500,
                  //                 color: model.gender == Gender.female
                  //                     ? Colors.white
                  //                     : AppColors.BLACK_COLOR,
                  //               ),
                  //             ],
                  //           ),
                  //         ),
                  //       ),
                  //     ),
                  //   ],
                  // ),
              
                  CustomInputField(
                    hintText: T('Email (optional)'),
                    backgroundColor: const Color(0xFFB3E5FC).withOpacity(0.4),
                    onChanged: (value) {
                      setState(() {
                        model.email = value;
                      });
                    },
                  ),
                  SizedBox(
                    height: AppController.h * 0.01,
                  ),
                  Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      color: const Color(0xFFB3E5FC).withOpacity(0.4),
                    ),
                    child: MyPhoneField(
                      textInputAction: TextInputAction.next,
                      hintText: T(''),
                      onChanged: (val) {
                        model.phoneNumber = val.countryCode + val.number;
                      },
                      onCountryChanged: (val) {},
                    ),
                  ),
                  SizedBox(
                    height: AppController.h * 0.02,
                  ),
                  // MyDatePicker(
                  //   initialVal: selectedDate ?? DateTime.now(),
                  //   backColor: const Color(0xFFB3E5FC).withOpacity(0.4),
                  //   caption: dateOfBirthDisplay ?? T('Date of Birth'),
                  //   onSave: (DateTime date) {
                  //     setState(() {
                  //       selectedDate = date;
                  //       final months = [
                  //         'Jan',
                  //         'Feb',
                  //         'Mar',
                  //         'Apr',
                  //         'May',
                  //         'Jun',
                  //         'Jul',
                  //         'Aug',
                  //         'Sep',
                  //         'Oct',
                  //         'Nov',
                  //         'Dec'
                  //       ];
                  //       dateOfBirthDisplay =
                  //           "${date.day} ${months[date.month - 1]} ${date.year}";
                  //       model.dateOfBirth = date;
                  //     });
                  //   },
                  // ),
                  // SizedBox(
                  //   height: AppController.h * 0.01,
                  // ),
                  CustomInputField(
                    controller: _passwordController,
                    hintText: T('Password'),
                    isPassword: true,
                    keyboardType: TextInputType.visiblePassword,
                    backgroundColor: const Color(0xFFB3E5FC).withOpacity(0.4),
                    onChanged: (value) {
                      setState(() {
                        model.password = value;
                      });
                    },
                  ),
                  Container(
                    alignment: AppController.mq.size.width > 450
                        ? Alignment.centerLeft
                        : Alignment.center,
                    margin: const EdgeInsets.only(top: 2),
                    child: FlutterPwValidator(
                      strings: PwValidatorStrings(),
                      key: validatorKey,
                      controller: _passwordController,
                      minLength: 8,
                      uppercaseCharCount: 1,
                      numericCharCount: 3,
                      specialCharCount: 1,
                      normalCharCount: 3,
                      width: AppController.mq.size.width > 450
                          ? 350
                          : AppController.mq.size.width - 80,
                      height: AppController.mq.size.width > 450 ? 160 : 130,
                      onSuccess: () {
                        setState(() {
                          passwordValid = true;
                        });
                      },
                      onFail: () {
                        setState(() {
                          passwordValid = false;
                        });
                      },
                    ),
                  ),
                  SizedBox(
                    height: AppController.h * 0.01,
                  ),
                  // Column(
                  //   children: [
                  //     _buildRadioOption(
                  //       id: 1,
                  //       title: T("User Account"),
                  //       icon: Icons.person, // أيقونة لحساب المستخدم
                  //     ),
                  //     const SizedBox(height: 8), // مسافة بين العناصر
                  //     _buildRadioOption(
                  //       id: 2,
                  //       title: T("Business Account"),
                  //       icon: Icons.business, // أيقونة لحساب تجاري
                  //     ),
                  //   ],
                  // ),
                  SizedBox(
                    height: AppController.h * 0.01,
                  ),
                  InkWell(
                      onTap: () async {
                        _signUp();
                      },
                      child: CustomContainer(
                        width: AppController.W,
                        text: T("Sign Up"),
                        color: AppColors.brown,
                      )),
                  SizedBox(
                    height: AppController.h * 0.03,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRadioOption(
      {required int id, required String title, required IconData icon}) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey, width: 1.5), // تحديد الحدود
        borderRadius: BorderRadius.circular(10), // تدوير الحواف
      ),
      child: RadioListTile<int>(
        title: Row(
          children: [
            Icon(icon, color: AppColors.PRIMARY_COLOR), // أيقونة قبل النص
            const SizedBox(width: 8),
            Text(
              title,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: Colors.black,
              ),
            ),
          ],
        ),
        value: id,
        groupValue: selectedAccountType,
        onChanged: (int? newId) {
          if (newId != null) {
            setState(() {
              selectedAccountType = newId;
            });
          }
        },
        activeColor: AppColors.PRIMARY_COLOR,
      ),
    );
  }
}
