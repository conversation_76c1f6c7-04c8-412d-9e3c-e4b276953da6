import 'package:alderishop/constants/constants.dart';
import 'package:alderishop/data/model/configration/configrationModel.dart';
import 'package:flutter/material.dart';

class CheckBoxList extends StatefulWidget {
  final List<ComboBoxDataModel> options;
  final Function(int?, bool) onChange;
  final List<int?> initialValue;
  final double? fontSize;
  final Color? color;
  final FontWeight? fontWeight;
  const CheckBoxList(
      {Key? key,
      required this.options,
      required this.onChange,
      required this.initialValue,
      this.fontWeight,
      this.color,
      this.fontSize})
      : super(key: key);

  @override
  State<CheckBoxList> createState() => _CheckBoxListState();
}

class _CheckBoxListState extends State<CheckBoxList> {
  late Map<int?, bool> values;

  @override
  void initState() {
    super.initState();
    values = {for (var option in widget.options) option.id: false};
    for (var id in widget.initialValue) {
      if (values.contains<PERSON>ey(id)) {
        values[id] = true;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return ListView(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      children: values.keys.map((int? id) {
        String name =
            widget.options.firstWhere((option) => option.id == id).name;
        return CheckboxListTile(
          activeColor: AppColors.Theard_COLOR,
          title: Text(
            name,
            style: TextStyle(
                fontSize: widget.fontSize ?? 12,
                color: widget.color ?? AppColors.BLACK_COLOR,
                fontWeight: widget.fontWeight ?? FontWeight.w400),
          ),
          value: values[id],
          onChanged: (bool? value) {
            setState(() {
              values[id] = value ?? false;
              widget.onChange(id, value ?? false);
            });
          },
        );
      }).toList(),
    );
  }
}
