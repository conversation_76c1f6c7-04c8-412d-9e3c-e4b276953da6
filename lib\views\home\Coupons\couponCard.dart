import 'package:alderishop/components/common_snckbar.dart';
import 'package:alderishop/constants/constants.dart';
import 'package:alderishop/controllers/app_controller.dart';
import 'package:alderishop/controllers/coupon_controller.dart';
import 'package:alderishop/data/model/coupon_model.dart';
import 'package:alderishop/services/helper.dart';
import 'package:alderishop/views/auth/sign_in.dart';
import 'package:alderishop/views/home/<USER>/my_Coupons_Page.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:page_transition/page_transition.dart';
import 'package:provider/provider.dart';

class CouponCard extends StatefulWidget {
  final CouponModel coupons;
  const CouponCard({
    super.key,
    required this.coupons,
  });

  @override
  State<CouponCard> createState() => _CouponCardState();
}

class _CouponCardState extends State<CouponCard> {
  @override
  Widget build(BuildContext context) {
      
    return Stack(
  children: [
    Container(
      width: 280,
      padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 5),
      decoration: BoxDecoration(
        color: const Color(0xFFD1D1BC),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
        
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
            _buildOperationInfoRow(
  context,
 
  widget.coupons.code ?? '',
   T(""),
  showCopyButton: true,
  valueToCopy: widget.coupons.code ?? '',
),

                    
                  ],
                ),
      
                Text(
                  widget.coupons.name ?? '',
                  style: const TextStyle(
                    fontSize: 13,
                    color: Colors.black54,
                  ),
                ),
        
                Text(
                  "${widget.coupons.discount?.toInt()} ",
                  style: const TextStyle(
                    fontSize: 28,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  "Price: ${widget.coupons.priceByPoints} point",
                  style: const TextStyle(
                    fontSize: 11,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
          ),

          // Right Image & Button Column
          Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Image.asset(
                "assets/img/new/coupon.png",
                height: 60,
                width: 60,
                fit: BoxFit.contain,
              ),
              const SizedBox(height: 12),
              ElevatedButton(
            onPressed: () async {
             if( AppController.isAuth){
 pleaseWaitDialog(context: context, isShown: true);

final result = await Provider.of<CouponController>(context, listen: false)
    .buyCoupon(widget.coupons.id ?? 0, widget.coupons.priceByPoints??0);


  pleaseWaitDialog(context: context, isShown: false);

  if (result.isSuccess == true) {
    successMsg(context: context, msg: T("Coupon purchased successfully"));

    await Provider.of<CouponController>(context, listen: false).getMyCoupons();

    final targetPage = AppController.isAuth
        ? const MyCouponsScreen()
        : const SignInPage();

    // ✅ Use Future.delayed to ensure navigation happens AFTER build
    Future.delayed(const Duration(seconds: 1), () {
      Navigator.of(context).push(
        PageTransition(
          type: AppController.currentLangId == 2
              ? PageTransitionType.leftToRight
              : PageTransitionType.rightToLeftWithFade,
          child: targetPage,
        ),
      );
    });
  } else {
    // ignore: use_build_context_synchronously
    errorMsg(context: context, title: T("تم شراء الكوبون من قبل"));
  }
             } else{
                   Navigator.of(context).push(
        PageTransition(
          type: AppController.currentLangId == 2
              ? PageTransitionType.leftToRight
              : PageTransitionType.rightToLeftWithFade,
          child: const SignInPage(),
        ),
      );
             }

 
},


                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF126C74),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(6),
                  ),
                  padding: const EdgeInsets.symmetric(
                      horizontal: 14, vertical: 6),
                  tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                ),
                child: const Text(
                  "!BUY NOW",
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.white,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    ),

    // Decorative left circle
    Positioned(
      left: -12,
      top: 60,
      child: Container(
        width: 30,
        height: 30,
        decoration: BoxDecoration(
          color: Colors.white,
          shape: BoxShape.circle,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.15),
              blurRadius: 3,
              offset: const Offset(1, 1),
            ),
          ],
        ),
      ),
    ),

    // Decorative right circle
    Positioned(
      right: -12,
      top: 60,
      child: Container(
        width: 30,
        height: 30,
        decoration: BoxDecoration(
          color: Colors.white,
          shape: BoxShape.circle,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.15),
              blurRadius: 3,
              offset: const Offset(1, 1),
            ),
          ],
        ),
      ),
    ),
     
  ],
)
;
  }
}
  Widget _buildOperationInfoRow(
      BuildContext context, String label, String value,
      {bool isTotal = false,
      bool showCopyButton = false,
      String? valueToCopy}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
       
        Text(
          value,
          style: TextStyle(
            color: AppColors.BLACK_COLOR,
            fontWeight: isTotal ? FontWeight.bold : FontWeight.w500,
            fontSize: isTotal ? 16 : 14,
          ),
        ),Text(
          label,
          style: TextStyle(
            color: AppColors.PRIMARY_COLOR,
            fontSize: 14,
            
          ),
        ), if (showCopyButton &&
                valueToCopy != null &&
                valueToCopy.isNotEmpty)
              IconButton(
                constraints: BoxConstraints.tight(const Size(25, 25)),
                padding: EdgeInsets.zero,
                onPressed: () {
                  Clipboard.setData(ClipboardData(text: valueToCopy));
                 successMsg(context: context,msg: "تم النسخ بنجاح");
                },
                icon: Icon(
                  Icons.copy,
                  size: 16,
                  color: AppColors.PRIMARY_COLOR.withOpacity(0.7),
                ),
                tooltip: T("Copy to clipboard"),
              ),
        
      ],
    );
  }
// Your existing TicketClipper stays unchanged
class TicketClipper extends CustomClipper<Path> {
  @override
  Path getClip(Size size) {
    const radius = 12.0;
    final path = Path();
    path.moveTo(0, 0);
    path.lineTo(0, size.height / 2 - radius);
    path.arcToPoint(
      Offset(0, size.height / 2 + radius),
      radius: const Radius.circular(radius),
      clockwise: false,
    );
    path.lineTo(0, size.height);
    path.lineTo(size.width, size.height);
    path.lineTo(size.width, size.height / 2 + radius);
    path.arcToPoint(
      Offset(size.width, size.height / 2 - radius),
      radius: const Radius.circular(radius),
      clockwise: false,
    );
    path.lineTo(size.width, 0);
    path.close();
    return path;
  }

  @override
  bool shouldReclip(CustomClipper<Path> oldClipper) => false;
}
