import 'package:alderishop/constants/constants.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';
import 'package:alderishop/components/common_image_viewer.dart';

class CommonImageSlider extends StatefulWidget {
  final List<String> imagePaths;
  final bool autoPlay;
  final double height;

  const CommonImageSlider({
    super.key,
    required this.imagePaths,
    this.autoPlay = true,
    this.height = 390,
  });

  @override
  State<CommonImageSlider> createState() => _CommonImageSliderState();
}

class _CommonImageSliderState extends State<CommonImageSlider> {
  int _currentIndex = 0;

  @override
  Widget build(BuildContext context) {
    if (widget.imagePaths.isEmpty) return const SizedBox();

    return Column(
      children: [
        CarouselSlider.builder(
          itemCount: widget.imagePaths.length,
          itemBuilder: (_, index, __) => GestureDetector(
            onTap: () => Navigator.push(
              context,
              MaterialPageRoute(
                builder: (_) => ImageViewer(
                  files: widget.imagePaths,
                  index: index,
                ),
              ),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: Image.network(
                widget.imagePaths[index],
                width: double.infinity,
                fit: BoxFit.cover,
                errorBuilder: (_, __, ___) =>
                    const Center(child: Icon(Icons.broken_image)),
              ),
            ),
          ),
          options: CarouselOptions(
            height: widget.height,
            autoPlay:true,
            enableInfiniteScroll: widget.imagePaths.length > 1,
            enlargeCenterPage: true,
            viewportFraction: 0.9,
            onPageChanged: (i, _) => setState(() => _currentIndex = i),
          ),
        ),
        const SizedBox(height: 8),
        AnimatedSmoothIndicator(
          activeIndex: _currentIndex,
          count: widget.imagePaths.length,
          effect: ScrollingDotsEffect(
            activeDotColor: AppColors.PRIMARY_COLOR,
            dotColor: Colors.red,
            dotHeight: 6,
            dotWidth: 6,
          ),
        ),
      ],
    );
  }
}
