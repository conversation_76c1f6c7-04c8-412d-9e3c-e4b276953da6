import 'package:alderishop/constants/constants.dart';
import 'package:alderishop/controllers/app_controller.dart';
import 'package:flutter/material.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';
import 'package:alderishop/components/common_image_viewer.dart';

class CommonImageSlider extends StatefulWidget {
  final List<String> imagePaths;
  final bool autoPlay;
  final double height;

  const CommonImageSlider({
    Key? key,
    required this.imagePaths,
    this.autoPlay = true,
    this.height = 390,
  }) : super(key: key);

  @override
  State<CommonImageSlider> createState() => _CommonImageSliderState();
}

class _CommonImageSliderState extends State<CommonImageSlider> {
  int _currentIndex = 0;
  late final PageController _pageController;

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    if (widget.autoPlay) {
      _startAutoPlay();
    }
  }

  void _startAutoPlay() {
    Future.delayed(const Duration(seconds: 3), () {
      if (!mounted || !widget.autoPlay) return;
      int nextPage = (_currentIndex + 1) % widget.imagePaths.length;
      _pageController.animateToPage(
        nextPage,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
      setState(() {
        _currentIndex = nextPage;
      });
      _startAutoPlay();
    });
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.imagePaths.isEmpty) {
      return const SizedBox();
    }

    return Column(
      children: [
        SizedBox(
          height: widget.height,
          width: AppController.W,
          child: PageView.builder(
            controller: _pageController,
            itemCount: widget.imagePaths.length,
            onPageChanged: (index) {
              setState(() {
                _currentIndex = index;
              });
            },
            itemBuilder: (context, index) {
              final imagePath = widget.imagePaths[index];
              return GestureDetector(
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (_) => ImageViewer(
                        files: widget.imagePaths,
                        index: index,
                      ),
                    ),
                  );
                },
                child: Image.network(
                  imagePath,
                  width: double.infinity,
                  fit: BoxFit.cover,
                  errorBuilder: (_, __, ___) => const Center(child: Icon(Icons.broken_image)),
                ),
              );
            },
          ),
        ),
        const SizedBox(height: 8),
        AnimatedSmoothIndicator(
          activeIndex: _currentIndex,
          count: widget.imagePaths.length,
          effect:  ScrollingDotsEffect(
            activeDotColor: AppColors.PRIMARY_COLOR,
            dotColor: Colors.grey,
            dotHeight: 1,
            dotWidth: 12,
          ),
        ),
      ],
    );
  }
}
