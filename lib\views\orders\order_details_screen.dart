import 'dart:async';
import 'package:alderishop/components/common_snckbar.dart';
import 'package:alderishop/components/layout.dart';
import 'package:alderishop/constants/constants.dart';
import 'package:alderishop/controllers/app_controller.dart';
import 'package:alderishop/data/model/order/order_model.dart';
import 'package:alderishop/services/helper.dart';
import 'package:alderishop/views/categories/widgets/Header.dart';
import 'package:alderishop/views/orders/order_details_container.dart';
import 'package:alderishop/views/orders/orders_screen.dart';
import 'package:alderishop/views/orders/status_color_widget.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:page_transition/page_transition.dart';
import 'package:timelines_plus/timelines_plus.dart';
import '../home/<USER>/CustomText.dart';
// ignore: must_be_immutable
class OrderDetailsPage extends StatefulWidget {
  final Orders myOrderModel;
  final orderStatuses = OrderStatus.values;
  const OrderDetailsPage({
    super.key,
    required this.myOrderModel,
  });

  @override
  State<OrderDetailsPage> createState() => _OrderDetailsPageState();
}

IconData getOrderStatusIcon(OrderStatus status) {
  switch (status) {
    case OrderStatus.PaymentPending:
      return Icons.payment;
    case OrderStatus.Pending:
      return Icons.hourglass_empty;
    case OrderStatus.Confirmed:
      return Icons.check_circle;
    case OrderStatus.Preparing:
      return Icons.kitchen;
    case OrderStatus.OutForDelivery:
      return Icons.delivery_dining;
    case OrderStatus.Delivered:
      return Icons.home;
    case OrderStatus.Canceled:
      return Icons.cancel;
    case OrderStatus.Returned:
      return Icons.reply;
    case OrderStatus.Refunded:
      return Icons.attach_money;
    case OrderStatus.Failed:
      return Icons.error;
    default:
      return Icons.help_outline;
  }
}

String _getStatusText(OrderStatus status) {
  switch (status) {
    case OrderStatus.PaymentPending:
      return T('Payment Pending');
    case OrderStatus.Pending:
      return T('Pending');
    case OrderStatus.Confirmed:
      return T('Confirmed');
    case OrderStatus.Preparing:
      return T('Preparing');
    case OrderStatus.OutForDelivery:
      return T('Out For Delivery');
    case OrderStatus.Delivered:
      return T('Delivered');
    case OrderStatus.Canceled:
      return T('Canceled');
    case OrderStatus.Returned:
      return T('Returned');
    case OrderStatus.Refunded:
      return T('Refunded');
    case OrderStatus.Failed:
      return T('Failed');
    default:
      return T('Unknown');
  }
}

Color _getStatusColor(OrderStatus status) {
  switch (status) {
    case OrderStatus.PaymentPending:
      return Colors.grey;
    case OrderStatus.Pending:
      return Colors.orange;
    case OrderStatus.Confirmed:
      return Colors.blue;
    case OrderStatus.Preparing:
      return Colors.purple;
    case OrderStatus.OutForDelivery:
      return Colors.green;
    case OrderStatus.Delivered:
      return Colors.greenAccent[700]!;
    case OrderStatus.Canceled:
      return Colors.red;
    case OrderStatus.Returned:
      return Colors.brown;
    case OrderStatus.Refunded:
      return Colors.pink;
    case OrderStatus.Failed:
      return Colors.black;
    default:
      return Colors.white;
  }
}

OrderStatus intToOrderStatus(int? value) {
  if (value == null || value < 0 || value >= OrderStatus.values.length) {
    return OrderStatus.PaymentPending;
  }
  return OrderStatus.values[value];
}

class _OrderDetailsPageState extends State<OrderDetailsPage> {
  
  late Duration _timeLeft = Duration.zero; 
  Timer? _timer;

  @override
  void initState() {
    super.initState();
    _startCountdownIfNeeded();
  }
void _startCountdownIfNeeded() {
 if (widget.myOrderModel.deliveryType == 0 &&
    widget.myOrderModel.orderStatus == OrderStatus.Preparing.index)
 {
    final orderTime = DateTime.parse(widget.myOrderModel.updatedAt.toString());
    final now = DateTime.now();

    // Countdown duration (for UI/testing)
    const countdownDuration = Duration(hours:24);

    // Actual expiry time (24 hours after order created)
    orderTime.add(const Duration(hours: 24));

    final countdownDeadline = orderTime.add(countdownDuration);

    // Calculate how much time left for 5 minutes countdown
    final remaining = countdownDeadline.difference(now);

    if (remaining.isNegative) {
      _timeLeft = Duration.zero;
    } else {
      _timeLeft = remaining;
      _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
        setState(() {
          _timeLeft -= const Duration(seconds: 1);
          if (_timeLeft <= Duration.zero) {
            _timer?.cancel();
          }
        });
      });
    }
  }
}

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    return '${twoDigits(duration.inHours)}:${twoDigits(duration.inMinutes % 60)}:${twoDigits(duration.inSeconds % 60)}';
  }
  Widget build(BuildContext context) {
    final createdOnUtc = DateTime.parse(widget.myOrderModel.date.toString());
    final qrData = widget.myOrderModel.invoiceRefrenceCodeERP;
final isQrVisible = widget.myOrderModel.deliveryType == 0 && (_timeLeft > Duration.zero);
var ewe="";
    return ApplicationLayout(
      selectedBottomNavbarItem: BottomNavbarItems.none,
      content: SingleChildScrollView(
        child: Column(
          children: [
            CategoriesHeader(
              text: T('OrderDetails'),
              onTap: () {
                Navigator.push(
                    context,
                    PageTransition(
                      type: AppController.currentLangId == 2
                          ? PageTransitionType.rightToLeft
                          : PageTransitionType.leftToRight,
                      child: const MyOrderesScreen(),
                    ));
              },
            ),
            Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
        
                SizedBox(
                  width: double.infinity,
                  height: widget.myOrderModel.orderStatusHistory!.isNotEmpty
                      ? 80
                      : 0,
                  child: Timeline.tileBuilder(
                    scrollDirection: Axis.horizontal,
                    builder: TimelineTileBuilder.connected(
                      itemCount: widget.myOrderModel.orderStatusHistory!.length,
                      contentsBuilder: (context, index) => Padding(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 15, vertical: 5),
                        child: Column(
                          children: [
                            Text(
                              _getStatusText(
                                intToOrderStatus(widget.myOrderModel
                                    .orderStatusHistory![index].orderStatus),
                              ),
                              style: const TextStyle(fontSize: 10),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                      indicatorBuilder: (context, index) {
                        final orderStatusInt = widget.myOrderModel
                            .orderStatusHistory![index].orderStatus;
                        final status = intToOrderStatus(orderStatusInt);

                        final isLast = index ==
                            widget.myOrderModel.orderStatusHistory!.length - 1;

                        return DotIndicator(
                          size: 30,
                          color: isLast
                              ? _getStatusColor(status)
                              : AppColors.BLACK_GREY,
                          child: Icon(
                            getOrderStatusIcon(status),
                            size: 16,
                            color: Colors.white,
                          ),
                        );
                      },
                      connectorBuilder: (context, index, type) {
                        if (index == 9) {
                          return null;
                        }
                        return SolidLineConnector(
                          color: Colors.grey.withOpacity(0.5),
                        );
                      },
                    ),
                  ),
                )
              ],
            ),
            const SizedBox(
              height: 10,
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 25, vertical: 10),
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 15, vertical: 10),
                decoration: BoxDecoration(
                    border: Border.all(color: AppColors.WHITE_COLOR),
                    color: AppColors.bg3,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.2),
                        spreadRadius: 2,
                        blurRadius: 5,
                        offset: const Offset(0, 3),
                      ),
                    ],
                    borderRadius: BorderRadius.circular(15)),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        CustomText(
                          text: T('Order Code'),
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: AppColors.BLACK_COLOR,
                        ),
                        CustomText(
                          text: widget.myOrderModel.id.toString(),
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: AppColors.PRIMARY_COLOR,
                        ),
                        OrderStatusWidget(
                          orderStatus: widget.myOrderModel.orderStatus,
                        )
                      ],
                    ),
                    SizedBox(
                      height: AppController.h * 0.005,
                    ),
                    Container(
                      height: 1.h,
                      width: 350.w,
                      color: AppColors.BLACK_GREY,
                    ),
                    SizedBox(
                      height: AppController.h * 0.005,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        CustomText(
                          text: T('Order Date'),
                          fontSize: 11,
                          fontWeight: FontWeight.w300,
                          color: AppColors.BLACK_COLOR,
                        ),
                        CustomText(
                          text: DateFormat('dd/MM/yyyy').format(createdOnUtc),
                          fontSize: 11,
                          fontWeight: FontWeight.w300,
                          color: AppColors.BLACK_COLOR,
                        ),
                      ],
                    ),
                    SizedBox(
                      height: AppController.h * 0.02,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        CustomText(
                          text: T(
                              '${T("Delivery type")} : ${widget.myOrderModel.deliveryType}'),
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: AppColors.BLACK_COLOR,
                        ),
                        InkWell(
                          onTap: () {
                            showQrDialog(context: context, qrData: qrData);
                          },
                          child: Container(
                              color: AppColors.WHITE_COLOR,
                              child: Image.asset("assets/img/new/qrcode.png")),
                        ),
                      ],
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        CustomText(
                          text:
                              "${T("Total")}  : ${widget.myOrderModel.total ?? ""} IQD",
                          fontSize: 11,
                          fontWeight: FontWeight.w100,
                          color: AppColors.BLACK_COLOR,
                        ),
                      ],
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                         if (isQrVisible)
                    CustomText(
                      text: _formatDuration(_timeLeft),
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                      color: Colors.redAccent,
                    ),
                      ],
                    ),
                    FutureBuilder(
                        future: null,
                        builder: (context, snapshot) {
                          return ListView.builder(
                            shrinkWrap: true,
                            physics: const NeverScrollableScrollPhysics(),
                            itemCount: widget.myOrderModel.orderItem?.length,
                            itemBuilder: (context, index) {
                              if (widget.myOrderModel.orderItem != null) {
                                return OrderDetailsContainer(
                                  myOrderModel:
                                      widget.myOrderModel.orderItem?[index],
                                );
                              }
                              return null;
                            },
                          );
                        }),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
