import 'package:alderishop/data/model/addresses_model.dart';
import 'package:alderishop/views/CartPage/addressPage/widget/address_list_items_widget.dart';
import 'package:flutter/material.dart';

class AddressListWidgetForSelect extends StatefulWidget {
  const AddressListWidgetForSelect({
    super.key,
    required this.data,
  });

  final List<AddressesModel> data;

  @override
  State<AddressListWidgetForSelect> createState() =>
      _AddressListWidgetForSelectState();
}

class _AddressListWidgetForSelectState
    extends State<AddressListWidgetForSelect> {
  int? selectedAddressId;
  bool showSelectedAddress = false;

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return 
  
    ListView.builder(
      physics: const NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      itemCount: widget.data.length>4?4:widget.data.length,
      itemBuilder: (context, index) {
        final isSelected = selectedAddressId == widget.data[index].id;
        return InkWell(
          onTap: () {
            setState(() {
              selectedAddressId = widget.data[index].id;
              showSelectedAddress = true;
            });
          },
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 15),
            child: AddressListItemsWidget(
              model: widget.data[index],
              isSelected: isSelected,
              onChange: (id) {
                setState(() {
                  selectedAddressId = id;
                });
              },
            ),
          ),
        );
      },
    );
  }
}
