import 'package:alderishop/constants/constants.dart';
import 'package:alderishop/data/model/configration/configrationModel.dart';
import 'package:flutter/material.dart';

class RadioListBuilder extends StatefulWidget {
  final List<ComboBoxDataModel> options;
  final Function(int?, bool) onChange;
  final int? initialValue; // New parameter for initial value

  const RadioListBuilder(
      {Key? key,
      required this.options,
      required this.onChange,
      this.initialValue})
      : super(key: key);

  @override
  RadioListBuilderState createState() => RadioListBuilderState();
}

class RadioListBuilderState extends State<RadioListBuilder> {
  int? selectedValue;

  @override
  void initState() {
    super.initState();
    selectedValue = widget.initialValue; // Set initial value
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      mainAxisSize: MainAxisSize.min,
      children: widget.options.map((option) {
        return RadioListTile(
          activeColor: AppColors.Theard_COLOR,
          dense: true,
          title: Text(option.name),
          value: option.id, // Use option.id as value
          groupValue: selectedValue,
          onChanged: (value) {
            setState(() {
              selectedValue = value as int?; // Cast value to int
              widget.onChange(
                  selectedValue, true); // Call onChange with ID and true
            });
          },
        );
      }).toList(),
    );
  }
}
