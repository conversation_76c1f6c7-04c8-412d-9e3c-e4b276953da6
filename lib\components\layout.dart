
import 'package:alderishop/components/common_buttom_mavbar_widget.dart';
import 'package:alderishop/constants/constants.dart';
import 'package:alderishop/controllers/app_controller.dart';
import 'package:alderishop/services/helper.dart';
import 'package:alderishop/views/CartPage/cart_items_screen.dart';
import 'package:alderishop/views/home/<USER>/my_Coupons_Page.dart';
import 'package:alderishop/views/home/<USER>/SearchPage.dart';
import 'package:alderishop/views/orders/orders_screen.dart';
import 'package:alderishop/views/setting/Privacy_Policy_Page.dart';
import 'package:alderishop/views/profile/profile.dart';
import 'package:alderishop/views/auth/sign_in.dart';
import 'package:alderishop/views/home/<USER>';
import 'package:alderishop/views/home/<USER>/appbar.dart';
import 'package:alderishop/views/setting/terms_to_use_Page.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:page_transition/page_transition.dart';
import 'package:url_launcher/url_launcher.dart';
import '../views/favorite/favorite.dart';

void _launchURL(BuildContext context) async {
  final Uri url = Uri.parse('https://pal4it.com');
  await launchUrl(
    url,
    mode: LaunchMode.externalApplication,
  );
}

enum BottomNavbarItems {
  home,
  favorite,
  shoppingcard,
  categories,
  profile,
  none,
}

class Layout extends StatefulWidget {
  const Layout({
    super.key,
    required this.content,
    required this.selectedBottomNavbarItem,
  });
  final Widget content;
  final BottomNavbarItems selectedBottomNavbarItem;

  @override
  // ignore: library_private_types_in_public_api
  _LayoutState createState() => _LayoutState();
}

class _LayoutState extends State<Layout> with SingleTickerProviderStateMixin {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  late PageController _pageController;
  final int _selectedIndex = 0;

  @override
  void initState() {
    _pageController = PageController(initialPage: 0);
    _pageController.addListener(_handleTabSelection);
    super.initState();
  }

  void _handleTabSelection() {
    setState(() {});
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.WHITE_COLOR,
      key: _scaffoldKey,
      resizeToAvoidBottomInset: false,
      appBar: const MyAppBar(),
      body: widget.content,
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
      endDrawer: Drawer(
        backgroundColor: AppColors.PRIMARY_COLOR.withOpacity(0.5),
        width: 180,
        child: ListView(
          padding: EdgeInsets.only(right: 10.w, left: 10.w),
          children: [
            SizedBox(
              height: AppController.h * 0.1,
            ),
            SizedBox(
              height: 45,
              child: ListTile(
                title: Text(
                  T('Home'),
                  style: TextStyle(
                      fontSize: 15,
                      color: AppColors.WHITE_COLOR,
                      fontWeight: FontWeight.w500),
                ),
                selected: _selectedIndex == 0,
                onTap: () {
                  Navigator.push(
                      context,
                      PageTransition(
                          type: AppController.currentLangId == 2
                              ? PageTransitionType.leftToRight
                              : PageTransitionType.rightToLeftWithFade,
                          child: const HomePage()));
                },
              ),
            ),
            SizedBox(
              height: 45,
              child: ListTile(
                title: Text(
                  T('My Orders'),
                  style: TextStyle(
                      fontSize: 15,
                      color: AppColors.WHITE_COLOR,
                      fontWeight: FontWeight.w500),
                ),
                selected: _selectedIndex == 5,
                onTap: () {
                  if (AppController.isAuth) {
                  } else {
                    Navigator.push(
                        context,
                        PageTransition(
                            type: AppController.currentLangId == 2
                                ? PageTransitionType.leftToRight
                                : PageTransitionType.rightToLeftWithFade,
                            child: const SignInPage()));
                  }
                },
              ),
            ),
            SizedBox(
              height: 45,
              child: ListTile(
                title: Text(
                  T('My Cart'),
                  style: TextStyle(
                      fontSize: 15,
                      color: AppColors.WHITE_COLOR,
                      fontWeight: FontWeight.w500),
                ),
                selected: _selectedIndex == 3,
                onTap: () {
                  if (AppController.isAuth) {
                    Navigator.push(
                        context,
                        PageTransition(
                            type: AppController.currentLangId == 2
                                ? PageTransitionType.leftToRight
                                : PageTransitionType.rightToLeftWithFade,
                            child: const CartItemsScreen()));
                  } else {
                    Navigator.push(
                        context,
                        PageTransition(
                            type: AppController.currentLangId == 2
                                ? PageTransitionType.leftToRight
                                : PageTransitionType.rightToLeftWithFade,
                            child: const SignInPage()));
                  }
                },
              ),
            ),
            SizedBox(
              height: 45,
              child: ListTile(
                title: Text(
                  T('My Favorites'),
                  style: TextStyle(
                      fontSize: 15,
                      color: AppColors.WHITE_COLOR,
                      fontWeight: FontWeight.w500),
                ),
                selected: _selectedIndex == 2,
                onTap: () {
                  if (AppController.isAuth) {
                    Navigator.push(
                        context,
                        PageTransition(
                            type: AppController.currentLangId == 2
                                ? PageTransitionType.leftToRight
                                : PageTransitionType.rightToLeftWithFade,
                            child: const FavoritePage()));
                  } else {
                    Navigator.push(
                        context,
                        PageTransition(
                            type: AppController.currentLangId == 2
                                ? PageTransitionType.leftToRight
                                : PageTransitionType.rightToLeftWithFade,
                            child: const SignInPage()));
                  }
                },
              ),
            ),
            SizedBox(
              height: 45,
              child: ListTile(
                title: Text(
                  T('Account Information'),
                  style: TextStyle(
                      fontSize: 15,
                      color: AppColors.WHITE_COLOR,
                      fontWeight: FontWeight.w500),
                ),
                selected: _selectedIndex == 4,
                onTap: () {
                  if (AppController.isAuth) {
                    Navigator.push(
                        context,
                        PageTransition(
                            type: AppController.currentLangId == 2
                                ? PageTransitionType.leftToRight
                                : PageTransitionType.rightToLeftWithFade,
                            child: const ProfileScreen()));
                  } else {
                    Navigator.push(
                        context,
                        PageTransition(
                            type: AppController.currentLangId == 2
                                ? PageTransitionType.leftToRight
                                : PageTransitionType.rightToLeftWithFade,
                            child: const SignInPage()));
                  }
                },
              ),
            ),
            SizedBox(
              height: 45,
              child: ListTile(
                title: Text(
                  T('Privacy Policy'),
                  style: TextStyle(
                      fontSize: 15,
                      color: AppColors.WHITE_COLOR,
                      fontWeight: FontWeight.w500),
                ),
                selected: _selectedIndex == 7,
                onTap: () {
                  Navigator.push(
                      context,
                      PageTransition(
                          type: AppController.currentLangId == 2
                              ? PageTransitionType.leftToRight
                              : PageTransitionType.rightToLeftWithFade,
                          child: const PrivacyPolicyPage()));
                },
              ),
            ),
            SizedBox(
              height: 45,
              child: ListTile(
                title: Text(
                  T('Terms of use'),
                  style: TextStyle(
                      fontSize: 15,
                      color: AppColors.WHITE_COLOR,
                      fontWeight: FontWeight.w500),
                ),
                selected: _selectedIndex == 7,
                onTap: () {
                  Navigator.push(
                      context,
                      PageTransition(
                          type: AppController.currentLangId == 2
                              ? PageTransitionType.leftToRight
                              : PageTransitionType.rightToLeftWithFade,
                          child: const TermsToUsePage()));
                },
              ),
            ),
            SizedBox(
              height: AppController.h * 0.28,
            ),
            SizedBox(
              height: 40,
              child: ListTile(
                title: Text(
                  'Powered By :',
                  style: TextStyle(
                      fontSize: 11,
                      color: AppColors.WHITE_COLOR,
                      fontWeight: FontWeight.w500),
                ),
                selected: _selectedIndex == 6,
                onTap: () {},
              ),
            ),
            InkWell(
              onTap: () async {
                final Uri url = Uri.parse('https://pal4it.com');
                await launchUrl(
                  url,
                  mode: LaunchMode.externalApplication,
                );
              },
              child: ListTile(
                title: Row(
                  children: [
                    Image.asset(
                      'assets/img/pal4it.png',
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
     bottomNavigationBar: const CommonButtomNavBarWidget(selectedIndex: 3), // 3 = home
    );
  }
}

//----------------------------------------------------------------------

class ApplicationLayout extends StatefulWidget {
  const ApplicationLayout({
    super.key,
    required this.content,
    required this.selectedBottomNavbarItem,
  });
  final Widget content;
  final BottomNavbarItems selectedBottomNavbarItem;

  @override
  // ignore: library_private_types_in_public_api
  _ApplicationLayoutState createState() => _ApplicationLayoutState();
}

class _ApplicationLayoutState extends State<ApplicationLayout>
    with SingleTickerProviderStateMixin {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  late PageController _pageController;
  final int _selectedIndex = 0;

  @override
  void initState() {
    _pageController = PageController(initialPage: 0);
    _pageController.addListener(_handleTabSelection);
    super.initState();
  }

  void _handleTabSelection() {
    setState(() {});
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
        key: _scaffoldKey,
        resizeToAvoidBottomInset: false,
        appBar: const MyAppBar(),
        body: widget.content,
 
        drawer: Drawer(
          backgroundColor: AppColors.PRIMARY_COLOR,
          width: 210,
          child: ListView(
            padding: const EdgeInsets.symmetric(horizontal: 10),
            children: [
              SizedBox(
                height: AppController.h * 0.16,
              ),
              SizedBox(
                height: 40,
                child: ListTile(
                  title: Text(
                    T('Home'),
                    style: TextStyle(
                        fontSize: 15,
                        color: AppColors.WHITE_COLOR,
                        fontWeight: FontWeight.w500),
                  ),
                  selected: _selectedIndex == 0,
                  onTap: () {
                    Navigator.push(
                        context,
                        PageTransition(
                            type: AppController.currentLangId == 2
                                ? PageTransitionType.leftToRight
                                : PageTransitionType.rightToLeftWithFade,
                            child: const HomePage()));
                  },
                ),
              ),
              SizedBox(
                height: 40,
                child: ListTile(
                  title: Text(
                    T('My Favorites'),
                    style: TextStyle(
                        fontSize: 15,
                        color: AppColors.WHITE_COLOR,
                        fontWeight: FontWeight.w500),
                  ),
                  selected: _selectedIndex == 2,
                  onTap: () {
                    if (AppController.isAuth) {
                      Navigator.push(
                          context,
                          PageTransition(
                              type: AppController.currentLangId == 2
                                  ? PageTransitionType.leftToRight
                                  : PageTransitionType.rightToLeftWithFade,
                              child: const FavoritePage()));
                    } else {
                      Navigator.push(
                          context,
                          PageTransition(
                              type: AppController.currentLangId == 2
                                  ? PageTransitionType.leftToRight
                                  : PageTransitionType.rightToLeftWithFade,
                              child: const SignInPage()));
                    }
                  },
                ),
              ),
              SizedBox(
                height: 40,
                child: ListTile(
                  title: Text(
                    T('Search'),
                    style: TextStyle(
                        fontSize: 15,
                        color: AppColors.WHITE_COLOR,
                        fontWeight: FontWeight.w500),
                  ),
                  selected: _selectedIndex == 2,
                  onTap: () {
                    Navigator.push(
                        context,
                        PageTransition(
                            type: AppController.currentLangId == 2
                                ? PageTransitionType.leftToRight
                                : PageTransitionType.rightToLeftWithFade,
                            child: const SearchPage()));
                  },
                ),
              ),
              SizedBox(
                height: 40,
                child: ListTile(
                  title: Text(
                    T('My Orders'),
                    style: TextStyle(
                        fontSize: 15,
                        color: AppColors.WHITE_COLOR,
                        fontWeight: FontWeight.w500),
                  ),
                  selected: _selectedIndex == 5,
                  onTap: () {
                    if (AppController.isAuth) {
                      Navigator.push(
                          context,
                          PageTransition(
                              type: AppController.currentLangId == 2
                                  ? PageTransitionType.leftToRight
                                  : PageTransitionType.rightToLeftWithFade,
                              child: const MyOrderesScreen()));
                    } else {
                      Navigator.push(
                          context,
                          PageTransition(
                              type: AppController.currentLangId == 2
                                  ? PageTransitionType.leftToRight
                                  : PageTransitionType.rightToLeftWithFade,
                              child: const SignInPage()));
                    }
                  },
                ),
              ),
              SizedBox(
                height: 40,
                child: ListTile(
                  title: Text(
                    T('My Cart'),
                    style: TextStyle(
                        fontSize: 15,
                        color: AppColors.WHITE_COLOR,
                        fontWeight: FontWeight.w500),
                  ),
                  selected: _selectedIndex == 3,
                  onTap: () {
                    if (AppController.isAuth) {
                      Navigator.push(
                          context,
                          PageTransition(
                              type: AppController.currentLangId == 2
                                  ? PageTransitionType.leftToRight
                                  : PageTransitionType.rightToLeftWithFade,
                              child: const CartItemsScreen()));
                    } else {
                      Navigator.push(
                          context,
                          PageTransition(
                              type: AppController.currentLangId == 2
                                  ? PageTransitionType.leftToRight
                                  : PageTransitionType.rightToLeftWithFade,
                              child: const SignInPage()));
                    }
                  },
                ),
              ),
              SizedBox(
                height: 40,
                child: ListTile(
                  title: Text(
                    T('My Account'),
                    style: TextStyle(
                        fontSize: 15,
                        color: AppColors.WHITE_COLOR,
                        fontWeight: FontWeight.w500),
                  ),
                  selected: _selectedIndex == 4,
                  onTap: () {
                    if (AppController.isAuth) {
                      Navigator.push(
                          context,
                          PageTransition(
                              type: AppController.currentLangId == 2
                                  ? PageTransitionType.leftToRight
                                  : PageTransitionType.rightToLeftWithFade,
                              child: const ProfileScreen()));
                    } else {
                      Navigator.push(
                          context,
                          PageTransition(
                              type: AppController.currentLangId == 2
                                  ? PageTransitionType.leftToRight
                                  : PageTransitionType.rightToLeftWithFade,
                              child: const SignInPage()));
                    }
                  },
                ),
              ),
   
              SizedBox(
                height: 40,
                child: ListTile(
                  title: Text(
                    T('My Coupons'),
                    style: TextStyle(
                        fontSize: 15,
                        color: AppColors.WHITE_COLOR,
                        fontWeight: FontWeight.w500),
                  ),
                  selected: _selectedIndex == 4,
                  onTap: () {
                    if (AppController.isAuth) {
                      Navigator.push(
                          context,
                          PageTransition(
                              type: AppController.currentLangId == 2
                                  ? PageTransitionType.leftToRight
                                  : PageTransitionType.rightToLeftWithFade,
                              child: const MyCouponsScreen()));
                    } else {
                      Navigator.push(
                          context,
                          PageTransition(
                              type: AppController.currentLangId == 2
                                  ? PageTransitionType.leftToRight
                                  : PageTransitionType.rightToLeftWithFade,
                              child: const SignInPage()));
                    }
                  },
                ),
              ),
        
              SizedBox(
                height: AppController.h * 0.3,
              ),
              SizedBox(
                height: 40,
                child: ListTile(
                  title: Text(
                    'Powered By :',
                    style: TextStyle(
                        fontSize: 11,
                        color: AppColors.WHITE_COLOR,
                        fontWeight: FontWeight.w500),
                  ),
                  selected: _selectedIndex == 6,
                  onTap: () {},
                ),
              ),
              InkWell(
                onTap: () async {
                  _launchURL(context);
                },
                child: ListTile(
                  title: Row(
                    children: [
                      Image.asset(
                        'assets/img/pal4it.png',
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
        bottomNavigationBar: const CommonButtomNavBarWidget(selectedIndex: 3)); // 3 = home);
  }
}
