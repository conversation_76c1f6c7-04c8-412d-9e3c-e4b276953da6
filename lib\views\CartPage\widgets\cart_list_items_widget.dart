import 'package:alderishop/components/common_cache_image.dart';
import 'package:alderishop/components/common_quantity_selector.dart';
import 'package:alderishop/components/common_snckbar.dart';
import 'package:alderishop/constants/constants.dart';
import 'package:alderishop/controllers/app_controller.dart';
import 'package:alderishop/controllers/cart_controller.dart';
import 'package:alderishop/data/api/api.dart';
import 'package:alderishop/data/model/cart/cart_model.dart';
import 'package:alderishop/services/helper.dart';

import 'package:alderishop/views/categories/items/items_Details.dart';
import 'package:alderishop/views/home/<USER>/CustomText.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class CartListItemsWidget extends StatefulWidget {
  final bool confirmscreen; // Fixed this line to allow external assignment
  final CartModel? model;

  CartListItemsWidget({super.key, required this.confirmscreen, this.model});

  @override
  State<CartListItemsWidget> createState() => _CartListItemsWidgetState();
}

class _CartListItemsWidgetState extends State<CartListItemsWidget> {
  @override
  Widget build(BuildContext context) {
    print(widget.model);
    return Column(
      children: [
        if (widget.confirmscreen)
          InkWell(
            onTap: () async {
              bool result =
                  await Provider.of<CartController>(context, listen: false)
                      .clearCart();
              if (result) {
                setState(() {
                  widget.model?.cartItems?.clear();
                });

                successMsg(
                    // ignore: use_build_context_synchronously
                    context: context,
                    msg: T('All items removed from the cart!'));
              } else {
                // ignore: use_build_context_synchronously
                errorMsg(
                    context: context, title: T('Failed to clear the cart!'));
              }
            },
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 5, vertical: 5),
                    decoration: BoxDecoration(
                      border: Border.all(color: AppColors.RED_COLOR),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      T("Clear All"),
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.normal,
                        color: AppColors.RED_COLOR,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ListView.builder(
          itemCount: widget.model?.cartItems?.length ?? 0,
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemBuilder: (context, index) {
            final cartItem = widget.model?.cartItems?[index];

            return Column(
  
              children: [
                Row(              
                  children: [
                    InkWell(
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => ItemDetails(
                              item: cartItem?.itemId,
                            ),
                          ),
                        );
                      },
                      child: Padding(
                               padding: const EdgeInsets.symmetric(horizontal: 15),
                        child: SizedBox(
                          height: 100,
                          width: 100,
                          child: CachedImage(
                            fit: BoxFit.contain,
                            imageUrl: "$baseUrl1/${cartItem?.itemImage}",
                          ),
                        ),
                      ),
                    ),
                    Column(
                                      mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,              
                      children: [
                        SizedBox(
                          width: AppController.W / 2.3,
                          child: CustomText(
                            text: cartItem?.itemName ?? "",
                            fontSize: 13,
                            fontWeight: FontWeight.w600,
                            color: AppColors.PRIMARY_COLOR,
                          ),
                        ),
                                      (cartItem?.selectedOptions?.isNotEmpty ?? false)?
                        SizedBox(
                          width: AppController.W / 2.3,
                          child: RichText(
                            text: TextSpan(
                              children: cartItem?.selectedOptions
                                  ?.asMap()
                                  .entries
                                  .map((entry) {
                                int index = entry.key;
                                var option = entry.value;
                                return TextSpan(
                                  text: option.name ?? '',
                                  style: TextStyle(
                                    fontSize: 12,
                                    fontWeight: FontWeight.w600,
                                    color: AppColors.PRIMARY_COLOR,
                                  ),
                                  children: index !=
                                          cartItem.selectedOptions!
                                                  .length -
                                              1
                                      ? [
                                          const TextSpan(
                                              text: " | ",
                                              style: TextStyle(
                                                  color: Colors.grey))
                                        ]
                                      : [],
                                );
                              }).toList(),
                            ),
                          ),
                        ):SizedBox(height: 0,),
                        SizedBox(
                          width: AppController.W/1.8,
                          child: Row(
                                 
                            children: [
                              InkWell(
                            onTap: () async {
                              bool result = await Provider.of<CartController>(
                                      context,
                                      listen: false)
                                  .removeItemFromCart(
                                CartHelperModel(
                                  productId: cartItem?.itemId,
                                  quantity: cartItem?.quantity,
                                  cartItemId: cartItem?.id,
                                  selectedProductAttributeOptionIds: cartItem
                                      ?.selectedOptions
                                      ?.map((option) => option.id)
                                      .whereType<int>()
                                      .toList(),
                                ),
                              );
                                  
                              if (result) {
                                setState(() {
                                  widget.model?.cartItems?.removeAt(index);
                                });
                              }
                            },
                            child: Container(
                              decoration: BoxDecoration(
                                color: Colors.red,
                                borderRadius: BorderRadius.circular(5),
                              ),
                              height: 25,
                              width: 30,
                              child: Center(
                                child: Image.asset(
                                  'assets/img/delete.png',
                                  height: 20,
                                  width: 20,
                                ),
                              ),
                            ),
                          ),                                    
                           Spacer(),
                              QuantitySelector(
                                initialValue: cartItem?.quantity ?? 0,
                                onChangeCount: (newQuantity) {
                                  Provider.of<CartController>(context,
                                          listen: false)
                                      .onChangeProductQuantityInCart(
                                    newquantity: CartHelperModel(
                                      productId: cartItem?.itemId,
                                      quantity: newQuantity,
                                      cartItemId: cartItem?.id,
                                      selectedProductAttributeOptionIds:
                                          cartItem?.selectedOptions
                                              ?.map((option) => option.id)
                                              .whereType<int>()
                                              .toList(),
                                    ),
                                  );
                                },
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(
                          height: 5,
                        ),
                        SizedBox(
                          child: CustomText(
                            text:
                                " ${T("Price")} :${cartItem?.itemPrice ?? 0}",
                            fontSize: 13,
                            fontWeight: FontWeight.w600,
                            color: AppColors.PRIMARY_COLOR,
                          ),
                        ),
                        const SizedBox(
                          height: 5,
                        ),
                       
                      ],
                    )
                  ],
                ),
                Padding(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 20, vertical: 5),
                  child: Divider(
                    color: AppColors.PRIMARY_COLOR,
                    height: 10,
                  ),
                )
              ],
            );
          },
        ),
      ],
    );
  }
}
