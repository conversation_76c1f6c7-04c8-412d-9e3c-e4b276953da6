class favoriteModel {
  int? id;
  int? userId;
  int? productId;
  String? itemName;
  String? itemImage;
  double? itemPrice;
  int? itemCount;

  favoriteModel(
      {this.id,
      this.userId,
      this.productId,
      this.itemName,
      this.itemImage,
      this.itemPrice,
      this.itemCount});

  favoriteModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    userId = json['userId'];
    productId = json['productId'];
    itemName = json['itemName'];
    itemImage = json['itemImage'];
    itemPrice = json['itemPrice'];
    itemCount = json['itemCount'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['userId'] = this.userId;
    data['productId'] = this.productId;
    data['itemName'] = this.itemName;
    data['itemImage'] = this.itemImage;
    data['itemPrice'] = this.itemPrice;
    data['itemCount'] = this.itemCount;
    return data;
  }
}
