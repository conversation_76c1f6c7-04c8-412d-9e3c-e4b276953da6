import 'package:alderishop/controllers/app_controller.dart';
import 'package:alderishop/data/model/language/translate_model.dart';
import 'package:easy_localization/easy_localization.dart';

String T(String key) => tr(key);

String? getNameTranslated(List<TranslateModel> data) {
  var lanId = AppController.currentLangId;

  if (data.isNotEmpty) {
    if (data.any((element) =>
        element.languageId == lanId && element.localeKey == "Name")) {
      return data
          .firstWhere((element) =>
              element.languageId == lanId && element.localeKey == "Name")
          .localeValue;
    } else {
      return null;
    }
  }
  return null;
}

String? getShortDescriptionTranslated(List<TranslateModel> data) {
  var lanId = AppController.currentLangId;

  if (data.isNotEmpty) {
    if (data.any((element) =>
        element.languageId == lanId &&
        element.localeKey == "ShortDescription")) {
      return data
          .firstWhere((element) =>
              element.languageId == lanId &&
              element.localeKey == "ShortDescription")
          .localeValue;
    } else {
      return null;
    }
  }
  return null;
}

String? getFullDescriptionTranslated(List<TranslateModel> data) {
  var lanId = AppController.currentLangId;

  if (data.isNotEmpty) {
    if (data.any((element) =>
        element.languageId == lanId &&
        element.localeKey == "FullDescription")) {
      return data
          .firstWhere((element) =>
              element.languageId == lanId &&
              element.localeKey == "FullDescription")
          .localeValue;
    } else {
      return null;
    }
  }
  return null;
}
