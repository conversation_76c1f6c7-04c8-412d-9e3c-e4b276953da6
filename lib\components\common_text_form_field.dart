import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CommonTextField extends StatefulWidget {
  const CommonTextField({
    super.key,
    required this.label,
    this.hint,
    this.obscureText,
    this.keyboardType,
    this.controller,
    this.validator,
    this.onSaved,
    this.onChanged,
    this.onTap,
    this.isPassword = false,
    this.enabled,
    this.suffixIcon,
    this.initialValue,
  });
  final String? initialValue;
  final String label;
  final String? hint;
  final bool? obscureText;
  final TextInputType? keyboardType;
  final TextEditingController? controller;
  final String? Function(String?)? validator;
  final Function(String?)? onSaved;
  final Function(String)? onChanged;
  final Function()? onTap;
  final bool? enabled;
  final Widget? suffixIcon;
  final bool isPassword;

  @override
  State<CommonTextField> createState() => _CommonTextFieldState();
}

class _CommonTextFieldState extends State<CommonTextField> {
  bool _obscureText = true;
  void _toggle() {
    setState(() {
      _obscureText = !_obscureText;
    });
  }

  @override
  void setState(VoidCallback fn) {
    super.setState(fn);
  }

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      initialValue: widget.initialValue,
      keyboardType: widget.keyboardType,
     textAlign: TextAlign.start,

      decoration: commonInputDecoration(
        widget.label,
        widget.hint,
        widget.isPassword
            ? Container(
                padding: const EdgeInsets.only(top: 20),
                margin: const EdgeInsets.only(right: 20),
                width: 1,
                height: 2,
                child: IconButton(
                  focusNode: FocusNode(skipTraversal: true),
                  icon: Icon(
                    _obscureText ? Icons.visibility : Icons.visibility_off,
                    color: const Color(0xff42474d),
                    size: 15,
                  ),
                  onPressed: () {
                    _toggle();
                  },
                ),
              )
            : null,
      ),
      obscureText: widget.isPassword && _obscureText,
      controller: widget.controller,
      validator: widget.validator,
      onSaved: widget.onSaved,
      onChanged: widget.onChanged,
      onTap: widget.onTap,
      enabled: widget.enabled,
    );
  }
}
InputDecoration commonInputDecoration(
  String label,
  String? hint,
  Widget? suffixIcon,
) {
  return InputDecoration(
    alignLabelWithHint: true,
    contentPadding: const EdgeInsets.all(6),
    labelText: label,
    labelStyle: TextStyle(
      fontSize: 12.sp,
    ),
    filled: true,
    fillColor: Colors.transparent,
    hintText: hint ?? "",
    hintStyle: const TextStyle(),
    suffixIcon: suffixIcon,
    floatingLabelBehavior: FloatingLabelBehavior.auto,

    // 🔽 Add these borders for underline effect in black
    enabledBorder: const UnderlineInputBorder(
      borderSide: BorderSide(color: Colors.black38),
    ),
    focusedBorder: const UnderlineInputBorder(
      borderSide: BorderSide(color: Colors.black38, width: 2),
    ),
    disabledBorder: const UnderlineInputBorder(
      borderSide: BorderSide(color: Colors.black38), // Optional: lighter for disabled
    ),
  );
}
