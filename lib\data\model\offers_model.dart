enum OfferType { Product, Category }

class OffersModel {
  int? id;
  String? title;
  String? description;
  String? imageUrl;
  int? order;
  bool? isActive;
  OfferType? offerType;
  int? referenceId;

  OffersModel(
      {this.id,
      this.title,
      this.description,
      this.imageUrl,
      this.order,
      this.isActive,
      this.offerType,
      this.referenceId});

  OffersModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    title = json['title'];
    description = json['description'];
    imageUrl = json['imageUrl'];
    order = json['order'];
    isActive = json['isActive'];
    offerType = OfferType.values[json['offerType']];
    referenceId = json['referenceId'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['title'] = this.title;
    data['description'] = this.description;
    data['imageUrl'] = this.imageUrl;
    data['order'] = this.order;
    data['isActive'] = this.isActive;
    data['offerType'] = this.offerType?.index;
    data['referenceId'] = this.referenceId;
    return data;
  }
}
