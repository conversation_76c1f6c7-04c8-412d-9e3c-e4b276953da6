import 'package:alderishop/components/layout.dart';
import 'package:alderishop/constants/constants.dart';
import 'package:alderishop/controllers/app_controller.dart';
import 'package:alderishop/controllers/cart_controller.dart';
import 'package:alderishop/services/helper.dart';
import 'package:alderishop/views/CartPage/cart_product_list.dart';
import 'package:alderishop/views/CartPage/confirm_cart_screen.dart';
import 'package:alderishop/views/home/<USER>';
import 'package:alderishop/views/home/<USER>/CustomText.dart';
import "package:alderishop/views/profile/widgets/customContanier.dart";
import 'package:alderishop/views/categories/widgets/Header.dart';
import 'package:flutter/material.dart';
import 'package:page_transition/page_transition.dart';
import 'package:provider/provider.dart';

class CartItemsScreen extends StatefulWidget {
  const CartItemsScreen({super.key});

  @override
  State<CartItemsScreen> createState() => _CartItemsScreenState();
}

class _CartItemsScreenState extends State<CartItemsScreen> {
  bool? selected = false;
  late Future<void> _cartFuture;

@override
void initState() {
  super.initState();
  _cartFuture = Provider.of<CartController>(context, listen: false).getCartItems();
}

  @override
Widget build(BuildContext context) {


  return ApplicationLayout(
  selectedBottomNavbarItem: BottomNavbarItems.shoppingcard,
  content: FutureBuilder<void>(
    future: _cartFuture,
    builder: (context, snapshot) {
      if (snapshot.connectionState == ConnectionState.waiting) {
        return const Center(child: CircularProgressIndicator());
      } else if (snapshot.hasError) {
        return const Center(child: Text('حدث خطأ أثناء تحميل السلة'));
      }

      var model = Provider.of<CartController>(context).cartItems.first.totalItems;

      if (model==0) {
        return Padding(
          padding: EdgeInsets.symmetric(
            vertical: AppController.h / 3,
            horizontal: AppController.W / 5,
          ),
          child: Column(
            children: [
              CustomText(
                text: "لايوجد عناصر في السلة",
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppColors.PRIMARY_COLOR,
              ),
              const SizedBox(height: 10),
              InkWell(
                onTap: () {
                  Navigator.push(
                    context,
                    PageTransition(
                      type: AppController.currentLangId == 2
                          ? PageTransitionType.leftToRight
                          : PageTransitionType.rightToLeftWithFade,
                      child: const HomePage(),
                    ),
                  );
                },
                child: CustomContainer(
                  text: "ابدأ بالتسوق",
                  color: AppColors.brown,
                  width: AppController.W / 1.5,
                ),
              ),
            ],
          ),
        );
      }

      return SingleChildScrollView(
        child: Column(
          children: [
            CategoriesHeader(text: T('My Cart')),
            const CartProductListWidget(),
            const SizedBox(height: 5),
            InkWell(
              onTap: () {
                Navigator.of(context).pushReplacement(
                  MaterialPageRoute(
                    builder: (context) => const ConfirmCartScreen(),
                  ),
                );
              },
              child: CustomContainer(
                width: AppController.W / 1.12,
                color: AppColors.PRIMARY_COLOR,
                text: T('Confirm Cart'),
              ),
            ),
          ],
        ),
      );
    },
  ),
)
;
}

}
