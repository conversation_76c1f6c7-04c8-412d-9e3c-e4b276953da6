import 'package:alderishop/components/common_cache_image.dart';
import 'package:alderishop/controllers/app_controller.dart';
import 'package:alderishop/data/api/api.dart';
import 'package:alderishop/data/model/categories/categroy_model.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';

// ignore: must_be_immutable
class Subcategoriesitem extends StatefulWidget {
  CategoryModel category;
  final bool isGrid;
  Subcategoriesitem({super.key, required this.category, required this.isGrid});

  @override
  State<Subcategoriesitem> createState() => _SubcategoriesitemState();
}

class _SubcategoriesitemState extends State<Subcategoriesitem> {
  @override
  Widget build(BuildContext context) {
    final images = widget.category.images ?? [];
    return Stack(
      children: [
        images.length > 1
            ? CarouselSlider(
                options: CarouselOptions(
                  height: widget.isGrid
                      ? AppController.h * 0.24
                      : AppController.h * 0.476,
                  viewportFraction: 1.0,
                  autoPlay: true,
                  enlargeCenterPage: false,
                ),
                items: images.map((imageObj) {
                  return CachedImage(
                    height: AppController.h * 0.25,
                    width: AppController.W,
                    imageUrl: imageObj.fileName != null
                        ? "$baseUrl1/${imageObj.fileName}"
                        : '',
                    fit: BoxFit.contain,
                  );
                }).toList(),
              )
            : CachedImage(
                height: AppController.h * 0.25,
                width: AppController.W,
                imageUrl: images.isNotEmpty && images.first.fileName != null
                    ? "$baseUrl1/${images.first.fileName}"
                    : '',
                fit: BoxFit.contain,
              ),
        Positioned(
          bottom: 1,
          width: widget.isGrid ? AppController.W / 2 : AppController.W / 2.1,
          child: Container(
            padding: EdgeInsets.symmetric(
                horizontal: widget.isGrid ? 10 : 30, vertical: 10),
            width: AppController.W / 2,
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.bottomCenter,
                end: Alignment.topCenter,
                colors: [
                  Colors.black87,
                  Colors.black45,
                  Colors.black12,
                ],
              ),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
            ),
            child: Center(
              child: Text(
                widget.category.name ?? "",
                textAlign: TextAlign.center,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  shadows: [
                    Shadow(
                      blurRadius: 2,
                      color: Colors.black54,
                      offset: Offset(1, 1),
                    )
                  ],
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}
