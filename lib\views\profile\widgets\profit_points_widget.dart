import 'package:alderishop/constants/constants.dart';
import 'package:alderishop/controllers/auth_Controller.dart';
import 'package:alderishop/services/helper.dart';
import 'package:flutter/material.dart';
class ProfitPointsWidget extends StatefulWidget {
  const ProfitPointsWidget({super.key});

  @override
  State<ProfitPointsWidget> createState() => _ProfitPointsWidgetState();
}

class _ProfitPointsWidgetState extends State<ProfitPointsWidget> {
  late Future<double> _profitPointsFuture;

  @override
  void initState() {
    super.initState();
    _loadProfitPoints();
  }

  void _loadProfitPoints() {
    _profitPointsFuture = AuthController.getProfitPointOnBuy();
  }

  void refreshProfitPoints() {
    setState(() {
      _loadProfitPoints();
    });
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<double>(
      future: _profitPointsFuture,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const CircularProgressIndicator();
        } else if (snapshot.hasError) {
          return Text('${T("Error")}: ${snapshot.error}');
        } else if (snapshot.hasData) {
          double displayPoints = snapshot.data!;
          return Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              color: AppColors.PRIMARY_COLOR,
            ),
            padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 30),
            child: Text(
              displayPoints.toStringAsFixed(0),
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: AppColors.WHITE_COLOR,
                fontSize: 20,
              ),
            ),
          );
        } else {
          return Text(T("No profit points available."));
        }
      },
    );
  }
}
