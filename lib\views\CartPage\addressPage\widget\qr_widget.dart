import 'dart:ui';
import 'package:alderishop/controllers/app_controller.dart';
import 'package:flutter/material.dart';
import 'package:qr_flutter/qr_flutter.dart';

class QrCodeWidget extends StatelessWidget {
  final String qrData;
  final bool showText;

  const QrCodeWidget({super.key, required this.qrData, required this.showText});

  @override
  Widget build(BuildContext context) {
    return Center(
        child: Stack(
      children: [
        if (showText) ...[
          BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 5.0, sigmaY: 5.0),
            child: Container(
              height: AppController.h,
              width: AppController.W,
              color: Colors.white.withOpacity(0.2),
            ),
          ),
        ],
        AlertDialog(
          content: Container(
            margin: EdgeInsets.zero,
            padding: EdgeInsets.zero,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  decoration: const BoxDecoration(
                      color: Colors.black,
                      borderRadius: const BorderRadius.only(
                          topRight: Radius.circular(10),
                          topLeft: Radius.circular(10))),
                  height: 200,
                  width: 200,
                  child: Center(
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(5),
                      child: QrImageView(
                        data: qrData,
                        version: QrVersions.auto,
                        backgroundColor: Colors.white,
                        size: 180.0,
                        gapless: true,
                      ),
                    ),
                  ),
                ),
                Container(
                  width: 200,
                  decoration: const BoxDecoration(
                      color: Colors.black,
                      borderRadius: const BorderRadius.only(
                          bottomLeft: Radius.circular(10),
                          bottomRight: Radius.circular(10))),
                  child: const Text(
                    textAlign: TextAlign.center,
                    "SCAN ME",
                    style: TextStyle(fontSize: 32, color: Colors.white),
                  ),
                ),
                const SizedBox(height: 20),
              ],
            ),
          ),
        ),
      ],
    ));
  }
}
