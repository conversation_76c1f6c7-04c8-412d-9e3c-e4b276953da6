class TopicsModel {
  Body? body;
  int? id;

  TopicsModel({this.body, this.id});

  TopicsModel.fromJson(Map<String, dynamic> json) {
    body = json['Body'] != null ? new Body.fromJson(json['Body']) : null;
    id = json['Id'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.body != null) {
      data['Body'] = this.body!.toJson();
    }
    data['Id'] = this.id;
    return data;
  }
}

class Body {
  String? value;

  Body({this.value});

  Body.fromJson(Map<String, dynamic> json) {
    value = json['Value'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['Value'] = this.value;
    return data;
  }
}
