import 'package:alderishop/components/common_snckbar.dart';
import 'package:alderishop/constants/constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_simple_calculator/flutter_simple_calculator.dart';


 showBottomSheetWithCalculator(BuildContext context) {
  // Save the root context (main scaffold context)
  final rootContext = context;
  double calculaterValue = 0.0;

  showModalBottomSheet(
   
    context: rootContext,
    builder: (BuildContext context) {
      return Dialog(
        insetPadding: EdgeInsets.zero,
        child: Stack(
          children: [
            SimpleCalculator(
              onChanged: (key, value, expression) {
                calculaterValue = value ?? 0.0;
              },
            ),
            Positioned(
              top: 50,
              left: 10,
              child: InkWell(
                onTap: () async {
                  await Clipboard.setData(
                      ClipboardData(text: calculaterValue.toString()));

                  // Show SnackBar using the root context
                  // ignore: use_build_context_synchronously
                  successMsg(context: context,msg: "تم نسخ: $calculaterValue");
                  // ScaffoldMessenger.of(rootContext).showSnackBar(
                  //   SnackBar(
                  //     content: Text("تم نسخ: $calculaterValue"),
                  //     duration: const Duration(seconds: 2),
                  //   ),
                  // );
                },
                child: Icon(
                  Icons.copy,
                  color: AppColors.WHITE_COLOR,
                  size: 25,
                ),
              ),
            ),
          ],
        ),
      );
    },
  );
}
