import 'package:alderishop/data/api/api.dart';
import 'package:alderishop/data/model/order/order_model.dart';
import 'package:alderishop/data/model/response.dart';
import 'package:flutter/material.dart';

class OrdersController with ChangeNotifier {
  List<UserOrderModel> myOrders = [];
  UserOrderModel? userOrders;
    final int _pageSize = 20;
  Future<void> getOrders() async {
    try {
      var result = await Api.getOne(
        useBaseUrl2: false,
        action: "api/Orders/GetOrderForUser?skip=${myOrders.length}&take=${_pageSize}",
      );

      if (result.data != null && result.data['data'] != null) {
        userOrders = UserOrderModel.fromJson(result.data['data']);
        notifyListeners();
      }
    } catch (e) {
      print("Error fetching orders: $e");
    }
  }

//------------------------------------------------------------------------------
  Future<ResponseResultModel> createOrder(Orders order) async {
    try {
      order.id = 0;
      order.date = DateTime.now();
      order.isActive = true;
      order.isComplated = false;
      order.isDeleted = false;
      order.address = null;
      var url = 'api/Orders/CreateOrder';
      var result = await Api.post(
        action: url,
        body: order.toJson(),
      );
      if (result != null) {
        if (result.isSuccess == true) {
          return result;
        } else {
          return result;
        }
      }
      notifyListeners();
      return ResponseResultModel(isSuccess: false);
    } catch (e) {
      return ResponseResultModel(isSuccess: false);
    }
  }

  getOrderDetails({int? orderId}) {}
//   Future<void> getOrderDetails({
//     int? orderId,
//   }) async {
//     try {
//       orderItems = null;
//       var url = 'api/v1/OrderRest?orderId=$orderId';
//       var result = await Api.getOne(
//         action: url,
//       );
//       if (result.data != null) {
//         var model = NewOrderItemsModel.fromJson(result.data["Items"][0]);

//         List<OrderItems> emptyOrderList = [];
//         for (var item in (model.orderItems ?? emptyOrderList)) {
//           List<AttributeSelection> emptyList = [];
//           for (var atter in (item.attributeSelection ?? emptyList)) {
//             if (item.attributeAsString != null) {
//               item.attributeAsString =
//                   '${item.attributeAsString}${atter.key}: ${atter.value} \n';
//             } else {
//               item.attributeAsString = '${atter.key}: ${atter.value} \n';
//             }
//           }
//         }
//         orderItems = model;
//       }
//     } catch (e) {
//       print('Error fetching data: $e');
//     }
//   }
// }
}
