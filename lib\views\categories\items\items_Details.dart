import 'package:alderishop/components/common_cache_image.dart';
import 'package:alderishop/components/common_snckbar.dart';
import 'package:alderishop/components/layout.dart';
import 'package:alderishop/controllers/app_controller.dart';
import 'package:alderishop/controllers/cart_controller.dart';
import 'package:alderishop/controllers/coupon_controller.dart';
import 'package:alderishop/controllers/favorites_controller.dart';
import 'package:alderishop/controllers/products_controller.dart';
import 'package:alderishop/data/api/api.dart';
import 'package:alderishop/data/model/Products/productModel/productModel.dart';
import 'package:alderishop/data/model/cart/cart_model.dart';

import 'package:alderishop/services/helper.dart';
import 'package:alderishop/views/auth/sign_in.dart';
import 'package:alderishop/views/categories/items/widgets/detailsPageWigets/reated_product.dart';
import 'package:alderishop/views/home/<USER>/couponItemW;dget/couponItemWidget.dart';
import 'package:alderishop/views/profile/widgets/customContanier.dart';
import 'package:alderishop/views/categories/items/widgets/detailsPageWigets/description_widget.dart';
import 'package:alderishop/components/common_image_Slider.dart';
import 'package:alderishop/views/categories/items/widgets/detailsPageWigets/product_details_title_widget.dart';
import 'package:alderishop/views/categories/items/widgets/favoriteItems.dart';

import 'package:alderishop/views/categories/items/widgets/headerWidget.dart';

import 'package:flutter/material.dart';
import 'package:page_transition/page_transition.dart';
import 'package:provider/provider.dart';

import '../../../constants/constants.dart';

// ignore: must_be_immutable
class ItemDetails extends StatefulWidget {
  final int? item;

  const ItemDetails({
    super.key,
    required this.item,
  });

  @override
  State<ItemDetails> createState() => _ItemDetailsState();
}

class _ItemDetailsState extends State<ItemDetails> {
  int itemQuantity = 1;
bool isImageLoading = false;
  List<Options> selectedAtter = [];
final GlobalKey<_SelectedImageWidgetState> _imageWidgetKey = GlobalKey<_SelectedImageWidgetState>();

String? selectedImageUrl;
void _changeImage(String image) {
  setState(() {
    isImageLoading = true;
    selectedImageUrl = "$baseUrl1/$image";
  });

  
}


  @override
  Widget build(BuildContext context) {
    var data= Provider.of<CouponController>(context).couponsForAll;

    FavoritesController.checkIfFromFavorites(widget.item ?? 0);
    return ApplicationLayout(
      selectedBottomNavbarItem: BottomNavbarItems.none,
      content: FutureBuilder(
          future: Provider.of<ProductsController>(context, listen: false)
              .getProductById(itemId: widget.item!),
          builder: (
            context,
            snapshot,
          ) {
            if (snapshot.connectionState != ConnectionState.done) {
              return const Center(child: CircularProgressIndicator());
            }
            final dataProvider = Provider.of<ProductsController>(
              context,
              listen: false,
            );
            final product = dataProvider.productDetails;
 if (product == null) {
    return const Center(child: Text("Product not found"));
  }
            itemQuantity = dataProvider.productDetails?.quantity ?? 1;

            return SingleChildScrollView(
              child: Column(
                children: [
                  HeaderWidget(
                    onTap: () {
                      Navigator.of(context).pop();
                    },
                    text: product.name ?? "",
                  ),
(product.images?.isNotEmpty ?? false)
  ? (selectedImageUrl != null
     ? SelectedImageWidget(
                key: _imageWidgetKey,
                initialImageUrl: selectedImageUrl ?? "$baseUrl1/${product.images!.first.base64File}",
              )
      : CommonImageSlider(
          height: 450,
          imagePaths: product.images!
              .map((e) => "$baseUrl1/${e.base64File}")
              .toList(),
          autoPlay: false,
        ))
  : CachedImage(
      borderRadius: BorderRadius.circular(20),
      fit: BoxFit.cover,
      imageUrl: '',
    ),



                  const SizedBox(height: 15),
                  //----------------------- name------------------------------------------
                  Padding(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 12, vertical: 2),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        ProductDetailsTitleWidget(
                          item: product,
                        ),

                      
                      ],
                    ),
                  ),

                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 15),
                    child: Row(                 
                      children: [
                        Icon(
                            Icons.star,
                            color: AppColors.bg2,
                            size: 13,
                          ),
                          const SizedBox(width: 2),
                          Text(
                            "${product.productRating}",
                            style: TextStyle(
                              color: AppColors.BLACK_COLOR,
                              fontSize: 10,
                            ),
                          ),
                          Spacer(),
                        FavoriteItems(
                          item: widget.item,
                        )
                      ],
                    ),
                  ),  SizedBox(
                    height: AppController.h * 0.01,
                  ),
                  //---------------------Add to Cart ---------------------------
                  InkWell(
                    onTap: () async {
                      if (AppController.isAuth) {
                        pleaseWaitDialog(context: context, isShown: true);

                        var result = await Provider.of<CartController>(context,
                                listen: false)
                            .addItemToCart(
                          CartHelperModel(
                            selectedProductAttributeOptionIds: selectedAtter
                                .map(
                                  (e) => e.id ?? 0,
                                )
                                .toList(), // Pass the full list of Options objects
                            productId: widget.item,
                            quantity:
                                dataProvider.productDetails?.quantity ?? 1,
                          ),
                        );

                        // ignore: use_build_context_synchronously
                        pleaseWaitDialog(context: context, isShown: false);

                        if (result) {
                          // ignore: use_build_context_synchronously
                          successMsg(context: context);
                        } else {
                          // Show error message
                          // ignore: use_build_context_synchronously
                          errorMsg(context: context, title: '');
                        }
                      } else {
                        Navigator.push(
                            context,
                            PageTransition(
                                type: AppController.currentLangId == 2
                                    ? PageTransitionType.leftToRight
                                    : PageTransitionType.rightToLeftWithFade,
                                child: const SignInPage()));
                      }
                    },
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 22),
                      child: CustomContainer(icon: true,
                        width: AppController.W,
                        
                        color: AppColors.PRIMARY_COLOR,
                        text: T('Add To Cart'),
                      ),
                    ),
                  ),
                
                  const SizedBox(
                    height: 15,
                  ),
                  // //-----------------------DescriptionDetails---------------------
                DetailsDiscrptionWidget(
  item: product,
  onChoseAtter: (Options selectedOption, List<Options> optionsList) {
    selectedAtter = optionsList;

    String? imageToShow = selectedOption.image;

    if (imageToShow == null || imageToShow.isEmpty) {
      final optionWithImage = optionsList.firstWhere(
        (opt) => opt.image != null && opt.image!.isNotEmpty,
        orElse: () => Options(image: null),
      );
      imageToShow = optionWithImage.image;
    }

    if (imageToShow != null && imageToShow.isNotEmpty) {
      _imageWidgetKey.currentState?.updateImage("$baseUrl1/$imageToShow");
    }
  },
),


                  SizedBox(
                    height: AppController.h * 0.01,
                  ),
                  // //---------------------Related Product--------------------------
                  RelatedProductsListWidget(
                    isGrid: true,
                    products: product.relatedProducts ?? [],
                  ),
                   if (AppController.isAuth)
             Padding(
                   padding: const EdgeInsets.symmetric(horizontal: 30),
                   child: Column(
                     children: [
                      Row(
  children: [
    Text(T("Coupons"))
  ],
),
    
                       ListView.builder(
                                     shrinkWrap: true,
                                     physics: const NeverScrollableScrollPhysics(),
                                     scrollDirection: Axis.vertical,
                                     itemCount: data.length,
                                     itemBuilder: (context, index) {
                                       return Padding(
                        padding: const EdgeInsets.symmetric(vertical: 7),
                        child: CouponItemWidget(
                          coupons: data[index],
                        ),
                                       );
                                     },
                                   ),
                     ],
                   ),
                 ),

                ],
              ),
            );
          }),
    );
  }
}
class SelectedImageWidget extends StatefulWidget {
  final String? initialImageUrl;

  const SelectedImageWidget({Key? key, this.initialImageUrl}) : super(key: key);

  @override
  State<SelectedImageWidget> createState() => _SelectedImageWidgetState();
}

class _SelectedImageWidgetState extends State<SelectedImageWidget> {
  String? imageUrl;

  @override
  void initState() {
    super.initState();
    imageUrl = widget.initialImageUrl;
  }

  void updateImage(String newImageUrl) {
    setState(() {
      imageUrl = newImageUrl;
    });
  }

  @override
  Widget build(BuildContext context) {
    if (imageUrl == null || imageUrl!.isEmpty) {
      return Container(
        height: 450,
        color: Colors.grey[200],
        child: const Center(child: Text("No Image")),
      );
    }
    return CachedImage(
      height: 450,
      fit: BoxFit.cover,
      imageUrl: imageUrl!,
    );
  }
}
