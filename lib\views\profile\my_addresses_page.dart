import 'package:alderishop/components/layout.dart';
import 'package:alderishop/controllers/app_controller.dart';
import 'package:alderishop/services/helper.dart';
import 'package:alderishop/views/CartPage/addressPage/adresses_widget.dart';
import 'package:alderishop/components/common_header_widget.dart';
import 'package:flutter/material.dart';

class MyAddressesPage extends StatefulWidget {
  const MyAddressesPage({super.key});

  @override
  State<MyAddressesPage> createState() => _MyAddressesPageState();
}

class _MyAddressesPageState extends State<MyAddressesPage> {
  @override
  Widget build(BuildContext context) {
    return ApplicationLayout(
      content: SingleChildScrollView(
        child: Column(
          children: [
            CommonCategoriesHeader(
                text: T('My Addresses'),
                onTap: () {
                  Navigator.of(context).pop();
                }),
            MyAdressesWidget(
              height: AppController.h,
              axisDirection: Axis.vertical,
              isAccountPage: false,
              showText: false,
            ),
          ],
        ),
      ),
      selectedBottomNavbarItem: BottomNavbarItems.profile,
    );
  }
}
