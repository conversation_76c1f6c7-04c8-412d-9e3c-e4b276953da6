import 'package:flutter/material.dart';
import 'package:flutter_custom_clippers/flutter_custom_clippers.dart';

// ignore: must_be_immutable
class ImageBgWidget extends StatelessWidget {
  double? height;
  ImageBgWidget({super.key, this.height});

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // Shadow layer
        PhysicalShape(
          clipper: OvalBottomBorderClipper(),
          elevation: 6,
          shadowColor: Colors.black.withOpacity(0.15),
          color: Colors.transparent,
          child: Container(
            height: height ?? 170,
            color: Colors.white.withOpacity(0), // needed to render shadow
          ),
        ),

        // Image layer with border
        ClipPath(
          clipper: OvalBottomBorderClipper(),
          child: Container(
            height: height ?? 170,
            decoration: BoxDecoration(
              border: Border.all(
                color: Colors.white,
                width: 2,
              ),
            ),
            child: Image.asset(
              'assets/img/new/bg.png',
              width: double.infinity,
              fit: BoxFit.cover,
            ),
          ),
        ),
      ],
    );
  }
}
