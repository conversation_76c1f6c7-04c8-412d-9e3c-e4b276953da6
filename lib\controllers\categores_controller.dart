import 'package:alderishop/data/api/api.dart';
import 'package:alderishop/data/model/categories/categroy_model.dart';
import 'package:flutter/widgets.dart';

class CategoryController with ChangeNotifier {
  List<CategoryModel> category = [];
 List<CategoryModel> subcategories = [];
  Future<void> getCategory({
    bool resetAndRefresh = true,
  }) async {
    try {
      var url = 'Catalog/categories';
      var result = await Api.getOne(
        action: url,
      );
      category.clear();
      if (result != null && result.data['data'] != null) {
        for (var element in result.data['data']) {
          var data = CategoryModel.fromJson(element);
          category.add(data);
        }
      }

      notifyListeners();
    } catch (e) {
      // ignore: avoid_print
      print(e);
    }
  }

  //--------------------------------------------------------------------------
    Future<void> getSubCategories({
    required int parentId,
    bool resetAndRefresh = true,
  }) async {
    try {
      var url = 'Catalog/categories/$parentId';
      var result = await Api.getOne(action: url);
      if (resetAndRefresh) subcategories.clear();

      if (result != null && result.data['data'] != null) {
        for (var element in result.data['data']) {
          var data = CategoryModel.fromJson(element);
          subcategories.add(data);
        }
      }

      notifyListeners();
    } catch (e) {
      print(e);
    }
  }
}
