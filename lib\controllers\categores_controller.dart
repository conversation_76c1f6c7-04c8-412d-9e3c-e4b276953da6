import 'package:alderishop/data/api/api.dart';
import 'package:alderishop/data/model/categories/categroy_model.dart';
import 'package:flutter/widgets.dart';

class CategoryController with ChangeNotifier {
  List<CategoryModel> category = [];
  List<CategoryModel> subcategories = [];

  // Stack to maintain navigation history for subcategories
  final List<List<CategoryModel>> _subcategoryStack = [];
  final List<int> _parentIdStack = [];
  Future<void> getCategory({
    bool resetAndRefresh = true,
  }) async {
    try {
      var url = 'Catalog/categories';
      var result = await Api.getOne(
        action: url,
      );
      category.clear();
      if (result != null && result.data['data'] != null) {
        for (var element in result.data['data']) {
          var data = CategoryModel.fromJson(element);
          category.add(data);
        }
      }

      notifyListeners();
    } catch (e) {
      // ignore: avoid_print
      print(e);
    }
  }

  //--------------------------------------------------------------------------
  Future<void> getSubCategories({
    required int parentId,
    bool resetAndRefresh = true,
    bool pushToStack = true,
  }) async {
    try {
      var url = 'Catalog/SubCategoriesByParent?parentId=$parentId';
      var result = await Api.getOne(action: url);

      // Push current state to stack before changing
      if (pushToStack && subcategories.isNotEmpty) {
        _subcategoryStack.add(List.from(subcategories));
        _parentIdStack.add(_parentIdStack.isEmpty ? 0 : _parentIdStack.last);
      }

      if (resetAndRefresh) subcategories.clear();

      if (result != null && result.data['data'] != null) {
        for (var element in result.data['data']) {
          var data = CategoryModel.fromJson(element);
          subcategories.add(data);
        }
      }

      // Add current parent to stack
      if (pushToStack) {
        _parentIdStack.add(parentId);
      }

      notifyListeners();
    } catch (e) {
      print(e);
    }
  }

  // Method to go back to previous subcategories
  void popSubCategories() {
    if (_subcategoryStack.isNotEmpty) {
      subcategories = _subcategoryStack.removeLast();
      _parentIdStack.removeLast();
      notifyListeners();
    }
  }

  // Method to clear the navigation stack
  void clearSubCategoryStack() {
    _subcategoryStack.clear();
    _parentIdStack.clear();
  }

  // Method to manually push current state to stack
  void pushCurrentStateToStack(int parentId) {
    if (subcategories.isNotEmpty) {
      _subcategoryStack.add(List.from(subcategories));
      _parentIdStack.add(parentId);
    }
  }
}
