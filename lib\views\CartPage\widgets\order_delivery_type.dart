import 'package:alderishop/constants/constants.dart';
import 'package:alderishop/controllers/app_controller.dart';
import 'package:alderishop/data/model/order/order_model.dart';
import 'package:alderishop/services/helper.dart';
import 'package:alderishop/views/CartPage/addressPage/adresses_widget.dart';
import 'package:alderishop/views/CartPage/addressPage/widget/addAddressPage.dart';
import 'package:alderishop/views/home/<USER>/CustomText.dart';
import 'package:flutter/material.dart';
import 'package:page_transition/page_transition.dart';

class OrderDeliveryType extends StatefulWidget {
  final Function(DeliveryType deliveryType)? onclick;
  final Function(int? selectedAddressId)? onAddressSelected;

  const OrderDeliveryType({
    this.onclick,
    this.onAddressSelected,
    super.key,
  });

  @override
  State<OrderDeliveryType> createState() => _OrderDeliveryTypeState();
}

class _OrderDeliveryTypeState extends State<OrderDeliveryType> {
  bool? selected = false;
  int? selectedAddressId;

  int? _selectedContainer = 1;

  @override
  Widget build(BuildContext context) {
    // final data =
    //     Provider.of<OrdersController>(context, listen: false).orderItems;
    // final qrData = data?.id.toString();
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 5),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              GestureDetector(
                onTap: () {
                  setState(() {
                    _selectedContainer = 1;
                  });
                  widget.onclick?.call(DeliveryType.ToAddress);
                },
                child: Container(
                  width: AppController.W / 2.2,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 10, vertical: 12),
                  decoration: BoxDecoration(
                    color: _selectedContainer == 1
                        ? AppColors.PRIMARY_COLOR
                        : Colors.grey,
                    borderRadius: AppController.currentLangId != 2
                        ? const BorderRadius.only(
                            topLeft: Radius.circular(12),
                            bottomLeft: Radius.circular(12))
                        : const BorderRadius.only(
                            topRight: Radius.circular(12),
                            bottomRight: Radius.circular(12)),
                  ),
                  child: Center(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Column(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              T('Delivar to Address'),
                              style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold),
                            ),
                            Text(
                              T('By chosing this option\nthe order will be deliverd\nto Selected Address'),
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 9,
                              ),
                            ),
                          ],
                        ),
                        Image.asset("assets/img/new/location.png")
                      ],
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 5),
              GestureDetector(
                onTap: () {
                  setState(() {
                    _selectedContainer = 2;
                  });
                  widget.onclick?.call(DeliveryType.FromPlace);
                },
                child: Container(
                  width: AppController.W / 2.2,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
                  decoration: BoxDecoration(
                      color: _selectedContainer == 2
                          ? AppColors.PRIMARY_COLOR
                          : Colors.grey,
                      borderRadius: AppController.currentLangId != 2
                          ? const BorderRadius.only(
                              topRight: Radius.circular(12),
                              bottomRight: Radius.circular(12))
                          : const BorderRadius.only(
                              topLeft: Radius.circular(12),
                              bottomLeft: Radius.circular(12))),
                  child: Center(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        InkWell(
                            onTap: () {
                              widget.onAddressSelected!(null);
                            },
                            child: Image.asset(
                              "assets/img/new/qrcode.png",
                            )),
                        Column(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              T('Pick Form Store'),
                              style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold),
                            ),
                            Text(
                              T('By chosing this option \nyou should pick your \norder from Store'),
                              style: const TextStyle(
                                  color: Colors.white, fontSize: 9),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 15),
        if (_selectedContainer == 1)
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 10),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    CustomText(
                      text: T('My Addresses'),
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: AppColors.BLACK_COLOR,
                    ),
                    InkWell(
                      onTap: () {
                        Navigator.push(
                            context,
                            PageTransition(
                                type: AppController.currentLangId == 2
                                    ? PageTransitionType.leftToRight
                                    : PageTransitionType.rightToLeftWithFade,
                                child: const AddAddressesPage()));
                      },
                      child: Icon(
                        Icons.add_circle_sharp,
                        size: 25,
                        color: AppColors.PRIMARY_COLOR,
                      ),
                    )
                  ],
                ),
                MyAdressesWidget(
                  axisDirection: Axis.horizontal,
                    isAccountPage: false,
                    showText: true,
                    onAddressSelected: (
                      int selectedAddressId,
                    ) {
                      setState(() {
                        this.selectedAddressId = selectedAddressId;
                      });
                      if (widget.onAddressSelected != null) {
                        widget.onAddressSelected!(selectedAddressId);
                      }
                    }),
              ],
            ),
          )
        else if (_selectedContainer == 2)
          const SizedBox()
        // const QrCodeWidget(
        //   showText: false,
        //   qrData: "",
        // )
      ],
    );
  }
}
