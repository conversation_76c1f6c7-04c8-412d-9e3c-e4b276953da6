import 'dart:async';

import 'package:alderishop/components/common_snckbar.dart';
import 'package:alderishop/constants/constants.dart';
import 'package:alderishop/controllers/app_controller.dart';
import 'package:alderishop/data/model/order/order_model.dart';
import 'package:alderishop/services/helper.dart';
import 'package:alderishop/views/home/<USER>/CustomText.dart';
import 'package:alderishop/views/orders/status_color_widget.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
// Ensure this import is correct

class OrderListItemWidget extends StatefulWidget {
  final Orders orders;

  const OrderListItemWidget({
    Key? key,
    required this.orders,
  }) : super(key: key);

  @override
  State<OrderListItemWidget> createState() => _OrderListItemWidgetState();
}

class _OrderListItemWidgetState extends State<OrderListItemWidget> {
  String _getDeliveryTypeText() {
    switch (widget.orders.deliveryType) {
      case 0:
        return T('FromPlace');
      case 1:
        return T('ToAddress');
      default:
        return 'Unknown';
    }
  }

  late Duration _timeLeft = Duration.zero;
  Timer? _timer;

  @override
  void initState() {
    super.initState();

    _startCountdownIfNeeded();
  }

  void _startCountdownIfNeeded() {
    if (widget.orders.deliveryType == 0 &&
        widget.orders.orderStatus == OrderStatus.Preparing.index) {
      final orderTime = DateTime.parse(widget.orders.updatedAt.toString());
      final now = DateTime.now();

      final countdownDeadline = orderTime.add(const Duration(hours: 24));
      final remaining = countdownDeadline.difference(now);

      if (remaining.isNegative) {
        _timeLeft = Duration.zero;
      } else {
        _timeLeft = remaining;
        _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
          if (!mounted) return;
          setState(() {
            _timeLeft -= const Duration(seconds: 1);
            if (_timeLeft <= Duration.zero) {
              _timer?.cancel();
            }
          });
        });
      }
    }
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    return '${twoDigits(duration.inHours)}:${twoDigits(duration.inMinutes % 60)}:${twoDigits(duration.inSeconds % 60)}';
  }

  @override
  Widget build(BuildContext context) {
    final createdOnUtc = DateTime.parse(widget.orders.createdAt.toString());
    final isQrVisible = widget.orders.deliveryType == 0 &&
        widget.orders.orderStatus == OrderStatus.Preparing.index &&
        _timeLeft > Duration.zero;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Container(
        decoration: BoxDecoration(
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.2),
              spreadRadius: 1,
              blurRadius: 5,
              offset: Offset(0, 3),
            ),
          ],
          color: AppColors.bg3,
          borderRadius: BorderRadius.circular(10),
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  CustomText(
                    text: '${T("Order Code")} ',
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: AppColors.BLACK_COLOR,
                  ),
                  CustomText(
                    text: '${widget.orders.id}',
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: AppColors.PRIMARY_COLOR,
                  ),
                  OrderStatusWidget(
                    orderStatus: widget.orders.orderStatus,
                  )
                ],
              ),
              SizedBox(height: AppController.h * 0.005),
              Container(
                height: 1.h,
                width: 350.w,
                color: AppColors.BLACK_GREY,
              ),
              SizedBox(height: AppController.h * 0.005),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  CustomText(
                    text: T('Order Date'),
                    fontSize: 11,
                    fontWeight: FontWeight.w300,
                    color: AppColors.BLACK_COLOR,
                  ),
                  CustomText(
                    text: DateFormat('dd/MM/yyyy').format(createdOnUtc),
                    fontSize: 11,
                    fontWeight: FontWeight.w300,
                    color: AppColors.BLACK_COLOR,
                  ),
                ],
              ),
              SizedBox(height: AppController.h * 0.01),
              Row(
                children: [
                  CustomText(
                    text: '${T("Delivery type")}  : ${_getDeliveryTypeText()}',
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: AppColors.BLACK_COLOR,
                  ),
                  Spacer(),
                  if (isQrVisible)
                    InkWell(
                      onTap: () {
                        // ignore: unrelated_type_equality_checks
                        if (widget.orders.deliveryType == 0) {
                          final createdAt = DateTime.parse(
                              widget.orders.createdAt.toString());

                          // Real expiry is 24 hours
                          final realExpiryTime =
                              createdAt.add(const Duration(hours: 24));
                          final now = DateTime.now();

                          if (now.isAfter(realExpiryTime)) {
                            errorMsg(
                                context: context,
                                msg: "QR Code expired",
                                title: "");
                            return;
                          }

                          // Show QR if not expired according to 24h rule
                          showQrDialog(
                            context: context,
                            qrData: widget.orders.invoiceRefrenceCodeERP,
                            createdAt: createdAt,
                          );
                        } else {
                          showQrDialog(
                            context: context,
                            qrData: widget.orders.invoiceRefrenceCodeERP,
                          );
                        }
                      },
                      child: Container(
                        color: AppColors.WHITE_COLOR,
                        child: Image.asset("assets/img/new/qrcode.png"),
                      ),
                    ),
                  if (isQrVisible) const SizedBox(),
                ],
              ),
              Row(
                children: [
                  CustomText(
                    text: "${T("Total")} : ${widget.orders.total ?? ""} IQD",
                    fontSize: 11,
                    fontWeight: FontWeight.bold,
                    color: AppColors.BLACK_COLOR,
                  ),
                  const Spacer(),
                  if (isQrVisible)
                    CustomText(
                      text: _formatDuration(_timeLeft),
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                      color: Colors.redAccent,
                    ),
                ],
              ),
              SizedBox(
                height: 5,
              ),
              if (isQrVisible)
                const Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Icon(
                      Icons.warning,
                      color: Colors.red,
                      size: 18,
                    ),
                    CustomText(
                      text:
                          " نحذير : لايمكنك استلام طلبك بعد انتهاء المدة المحددة",
                      fontSize: 8,
                      fontWeight: FontWeight.bold,
                      color: Colors.redAccent,
                    ),
                  ],
                ),
            ],
          ),
        ),
      ),
    );
  }
}
