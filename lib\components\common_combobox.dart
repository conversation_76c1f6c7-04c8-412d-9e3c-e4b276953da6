import 'package:alderishop/constants/constants.dart';
import 'package:alderishop/data/model/configration/configrationModel.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../controllers/app_controller.dart';

List<ComboBoxDataModel>? servicesGroup;

class MyComboBox extends StatelessWidget {
  final String caption;
  final String labelText;
  final int? selectedValue;
  final String modalTitle;
  final List<ComboBoxDataModel> data;
  final Function(int? id, String name) onSelect;
  final bool isSmallLookup;
  final double? width;
  final double? fontSize;
  final Color? backColor;
  final Color? fontColor;
  final double? height;
  final double? borderRadius;
  final bool? isShowLabel;
  final double? borderWidth;
  final bool? isDisabled;

  const MyComboBox({
    Key? key,
    required this.caption,
    required this.onSelect,
    required this.modalTitle,
    this.isSmallLookup = false,
    this.width,
    this.backColor,
    this.fontColor,
    this.fontSize = 15,
    this.borderRadius = 11,
    this.height,
    this.selectedValue,
    required this.data,
    required this.labelText,
    this.isShowLabel,
    this.borderWidth,
    this.isDisabled = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: width ?? AppController.W,
      height: height ?? 65,
      child: InkWell(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            isShowLabel == null && isShowLabel != true
                ? Text(
                    labelText,
                    style: TextStyle(
                      fontWeight: FontWeight.normal,
                      color: AppColors.SOFT_GREY,
                      fontSize: 12.sp,
                    ),
                  )
                : const SizedBox(
                    height: 0,
                  ),
            Card(
              margin: EdgeInsets.zero,
              color: backColor,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 5),
                decoration: BoxDecoration(
                  border: Border(
                    bottom: BorderSide(
                      color: Colors.transparent,
                      width: borderWidth ?? 0.5,
                    ),
                    right: const BorderSide(color: Colors.transparent),
                    left: const BorderSide(color: Colors.transparent),
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    Expanded(
                      child: Center(
                        child: Text(
                          caption,
                          overflow: TextOverflow.ellipsis,
                          textAlign: TextAlign.center,
                          style: TextStyle(
                              fontSize: fontSize,
                              fontWeight: FontWeight.normal,
                              color: fontColor ?? AppColors.PRIMARY_COLOR),
                        ),
                      ),
                    ),
                    Icon(
                      Icons.arrow_drop_down,
                      color: fontColor ?? AppColors.PRIMARY_COLOR,
                      size: isSmallLookup ? 18 : 20,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
        onTap: () {
          isDisabled == false
              ? showModalBottomSheet<void>(
                  shape: const RoundedRectangleBorder(
                      borderRadius:
                          BorderRadius.vertical(top: Radius.circular(25.0))),
                  isScrollControlled: true,
                  context: context,
                  builder: (_) {
                    return SizedBox(
                      child: ComboBoxModal(
                        data: data,
                        selectedValue: selectedValue,
                        title: modalTitle,
                        onSelect: onSelect,
                      ),
                    );
                  },
                )
              : null;
        },
      ),
    );
  }
}

class ComboBoxModal extends StatefulWidget {
  const ComboBoxModal({
    Key? key,
    required this.title,
    required this.onSelect,
    this.selectedValue,
    required this.data,
  }) : super(key: key);

  final List<ComboBoxDataModel> data;
  final String title;
  final int? selectedValue;
  final Function(int? id, String name) onSelect;

  @override
  _ComboBoxModalState createState() => _ComboBoxModalState();
}

class _ComboBoxModalState extends State<ComboBoxModal> {
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(
        top: 8,
        bottom: AppController.mq.viewInsets.bottom,
      ),
      height: AppController.mq.viewInsets.bottom +
          (AppController.mq.size.height / 2.8),
      child: Column(
        children: [
          SizedBox(
            height: 30,
            child: Center(
              child: Text(
                widget.title,
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 18, color: AppColors.PRIMARY_COLOR),
              ),
            ),
          ),
          const Divider(thickness: 1),
          Expanded(
            child: ComboBoxListItems(
              data: widget.data,
              onSelect: widget.onSelect,
              selectedValue: widget.selectedValue,
            ),
          )
        ],
      ),
    );
  }
}

class ComboBoxListItems extends StatefulWidget {
  const ComboBoxListItems(
      {super.key,
      required this.data,
      required this.onSelect,
      this.selectedValue});
  final List<ComboBoxDataModel> data;
  final Function(int? id, String name) onSelect;
  final int? selectedValue;
  @override
  State<ComboBoxListItems> createState() => _ComboBoxListItemsState();
}

class _ComboBoxListItemsState extends State<ComboBoxListItems> {
  List<ComboBoxDataModel> myArr = [];
  ScrollController scrollController = ScrollController();
  @override
  void initState() {
    myArr = widget.data;
    super.initState();
    if (widget.selectedValue != null) {
      var selectedValue =
          myArr.firstWhere((element) => element.id == widget.selectedValue);
      var indexOfSelected = myArr.indexOf(selectedValue);
      print(indexOfSelected);

      WidgetsBinding.instance.addPostFrameCallback((_) {
        scrollController.animateTo(indexOfSelected * 40,
            duration: const Duration(milliseconds: 400), curve: Curves.easeIn);
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Expanded(
          child: ListView.builder(
            controller: scrollController,
            itemCount: myArr.length,
            itemBuilder: (context, index) {
              return Column(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 15),
                    child: InkWell(
                      splashColor: AppColors.PRIMARY_COLOR.withOpacity(0.5),
                      onTap: () {
                        Navigator.of(context).pop();
                        widget.onSelect(myArr[index].id, myArr[index].name);
                      },
                      child: Center(
                        child: Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: Text(
                            myArr[index].name,
                            style: TextStyle(
                              color: myArr[index].id == widget.selectedValue
                                  ? AppColors.PRIMARY_COLOR
                                  : AppColors.BLACK_COLOR,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                  Container(
                    color: Colors.grey,
                    width: AppController.W,
                    height: 1,
                  )
                ],
              );
            },
          ),
        ),
      ],
    );
  }
}
