class LoginModel {
  String? emailOrUserName;
  String? password;
  bool? rememberMe;
  bool? isCustomerApp;

  LoginModel(
      {this.emailOrUserName,
      this.password,
      this.rememberMe,
      this.isCustomerApp});

  LoginModel.fromJson(Map<String, dynamic> json) {
    emailOrUserName = json['emailOrUserName'];
    password = json['password'];
    rememberMe = json['rememberMe'];
    isCustomerApp = json['isCustomerApp'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['emailOrUserName'] = this.emailOrUserName;
    data['password'] = this.password;
    data['rememberMe'] = this.rememberMe;
    data['isCustomerApp'] = this.isCustomerApp;
    return data;
  }
}

class RegisterModel {
  String? firstName;
  String? lastName;
  String? phoneNumber;
  String? email;
  String? password;
  DateTime? dateOfBirth;
  UserTypes? userType;
  Gender? gender;
  RegisterModel(
      {this.firstName,
      this.lastName,
      this.phoneNumber,
      this.email,
      this.userType,
      this.dateOfBirth,
      this.gender,
      this.password});

  RegisterModel.fromJson(Map<String, dynamic> json) {
    firstName = json['firstName'];
    lastName = json['lastName'];
    phoneNumber = json['phoneNumber'];
    email = json['email'];
    dateOfBirth = json['dateOfBirth'];
    gender = json['gender'];
    userType = json['userType'];
    password = json['password'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['firstName'] = this.firstName;
    data['lastName'] = this.lastName;
    data['phoneNumber'] = this.phoneNumber;
    data['email'] = this.email;
    data['dateOfBirth'] = this.dateOfBirth?.toIso8601String();
    data['gender'] = this.gender?.index;
    data['userType'] = this.userType?.index;
    data['password'] = this.password;
    return data;
  }
}

enum UserTypes {
  admin,
  customer,
  businessCustomer,
  moderator, // مشرف
  contentManager, // مسؤول محتوى
}

enum Gender {
  unknown,
  male,
  female,
}
