import 'package:alderishop/controllers/app_controller.dart';
import 'package:alderishop/controllers/products_controller.dart';
import 'package:alderishop/views/categories/items/items_Details.dart';
import 'package:alderishop/views/home/<USER>/category/custom_categories_item.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:page_transition/page_transition.dart';
import 'package:provider/provider.dart';

class NewProductsWidget extends StatefulWidget {
    final bool isGrid;
  const NewProductsWidget({Key? key, required this.isGrid}) : super(key: key);

  @override
  State<NewProductsWidget> createState() => _NewProductsWidgetState();
}

class _NewProductsWidgetState extends State<NewProductsWidget> {
  @override
  Widget build(BuildContext context) {
    final dataProvider = Provider.of<ProductsController>(context);
    final itemList = dataProvider.newProducts;
    return AlignedGridView.count(
      crossAxisCount: widget.isGrid ? (AppController.W > 700 ? 3 : 2) : 1,

      crossAxisSpacing: 0,
      physics: const NeverScrollableScrollPhysics(),
      scrollDirection: Axis.vertical,
      shrinkWrap: true,
      itemCount: itemList.length > 4 ? 4 : itemList.length,
      itemBuilder: (BuildContext context, int index) {
        return InkWell(
          onTap: () {
            Navigator.push(
                context,
                PageTransition(
                  type: AppController.currentLangId == 2
                      ? PageTransitionType.leftToRight
                      : PageTransitionType.rightToLeftWithFade,
                  child: ItemDetails(
                    item: itemList[index].id,
                  ),
                ));
          },
          child: CustomCategoryItem(
                   isGrid: widget.isGrid,
            data: itemList[index],
          ),
        );
      },
    );
  }
}
