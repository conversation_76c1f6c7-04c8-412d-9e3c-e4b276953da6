import 'package:alderishop/components/layout.dart';
import 'package:alderishop/constants/constants.dart';
import 'package:alderishop/controllers/app_controller.dart';
import 'package:alderishop/services/helper.dart';
import 'package:alderishop/views/Chat/chat_page.dart';
import 'package:alderishop/views/categories/widgets/Header.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';

class ChatsPage extends StatefulWidget {
  const ChatsPage({super.key});

  @override
  State<ChatsPage> createState() => _ChatsPageState();
}

class _ChatsPageState extends State<ChatsPage> {
  @override
  Widget build(BuildContext context) {
    return ApplicationLayout(
      selectedBottomNavbarItem: BottomNavbarItems.none,
      content: Column(
        children: [
          const CategoriesHeader(text: "Chats"),
          Expanded(
            child: Padding(
              padding: const EdgeInsetsDirectional.symmetric(
                  horizontal: 20, vertical: 10),
              child: ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                scrollDirection: Axis.vertical,
                itemCount: 3,
                itemBuilder: (context, index) {
                  return InkWell(
                    onTap: () {
                      Navigator.of(context).pushReplacement(MaterialPageRoute(
                          builder: (context) => const ChatPage()));
                    },
                    child: Row(
                      children: [
                        Padding(
                          padding: const EdgeInsets.symmetric(vertical: 7),
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 25, vertical: 10),
                            width: AppController.W - 40,
                            decoration: BoxDecoration(
                                border: Border(
                                    bottom: BorderSide(
                                        color: AppColors.BLACK_GREY))),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Image.asset("assets/img/new/chats.png"),
                                Column(
                                  children: [
                                    Text(
                                      "محمد الاحمد",
                                      style: TextStyle(
                                          color: AppColors.BLACK_COLOR,
                                          fontWeight: FontWeight.bold,
                                          fontSize: 18),
                                    ),
                                    Text(T("Default Date"),
                                        style: TextStyle(
                                            color: AppColors.BLACK_COLOR,
                                            fontWeight: FontWeight.bold,
                                            fontSize: 15)),
                                  ],
                                ),
                                Image.asset("assets/img/new/chat_bubble.png")
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }
}
