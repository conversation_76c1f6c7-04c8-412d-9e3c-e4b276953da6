import 'package:flutter/material.dart';

class ProfileController extends ChangeNotifier {
  ProfileController();

  // Future<bool> updateProfile({
  //   ProfileModel? profileModel,
  //   bool resetAndRefresh = false,
  // }) async {
  //   try {
  //     var url =
  //         'api/v1/account/UpdateCustomerInfo/${AuthController.getCustomerId()}';
  //     var result = await Api.put(action: url, body: profileModel?.toJson());
  //     if (result != null) {
  //       if (result.isSuccess == true) {
  //         profileModel;
  //         notifyListeners();
  //         return true;
  //       } else {
  //         return false;
  //       }
  //     }
  //     return false;
  //   } catch (e) {
  //     print(e);
  //     return false;
  //   }
  // }
}
