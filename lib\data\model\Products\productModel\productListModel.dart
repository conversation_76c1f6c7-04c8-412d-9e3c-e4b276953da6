import 'package:alderishop/data/model/Products/productModel/productModel.dart';

class ProductListModel {
  int? id;
  String? name;
  String? shortDescription;
  double? price;
  double? discountPrice;
  double? averageRating;
  int? totalReviews;
  String? sku;
  bool? popularProduct;
  bool? isWishListAddedd;
  List<String>? images;
  List<ProductAttribute>? attribute;

  ProductListModel(
      {this.id,
      this.name,
      this.totalReviews,
      this.averageRating,
      this.shortDescription,
      this.price,
      this.discountPrice,
      this.sku,
      this.popularProduct,
      this.isWishListAddedd,
      this.images,
      this.attribute});

  ProductListModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
        totalReviews = json['totalReviews'];
    name = json['name'];
    shortDescription = json['shortDescription'];
    price = double.parse(json['price'].toString());
    averageRating = double.parse(json['averageRating'].toString());
    discountPrice = double.parse(json['discountPrice'].toString());

    sku = json['sku'];
    popularProduct = json['popularProduct'];
    isWishListAddedd = json['isWishListAddedd'];
    // Deserialize images as a list of Images objects
    if (json['images'] is List) {
      images = List<String>.from(json['images'].map((e) => e.toString()));
    } else {
      images = [];
    }
    if (json['attribute'] != null) {
      attribute = <ProductAttribute>[];
      json['attribute'].forEach((v) {
        attribute!.add(new ProductAttribute.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = id;
     data['totalReviews'] = totalReviews;
    data['name'] = name;
    data['shortDescription'] = shortDescription;
    data['price'] = price;
    data['averageRating'] = averageRating;
    data['discountPrice'] = discountPrice;

    data['sku'] = sku;
    data['popularProduct'] = popularProduct;
    data['isWishListAddedd'] = isWishListAddedd;
    data['images'] = images;
    if (attribute != null) {
      data['attribute'] = attribute!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class ProductReview {
  final int? id;
  final int? productId;
  final int? rating;
  final int? helpfulYesTotal;
  final int? helpfulNoTotal;

  ProductReview({
    this.id,
    this.productId,
    this.rating,
    this.helpfulYesTotal,
    this.helpfulNoTotal,
  });

  factory ProductReview.fromJson(Map<String, dynamic> json) {
    return ProductReview(
      id: json['Id'],
      productId: json['ProductId'],
      rating: json['Rating'],
      helpfulYesTotal: json['HelpfulYesTotal'],
      helpfulNoTotal: json['HelpfulNoTotal'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'Id': id,
      'ProductId': productId,
      'Rating': rating,
      'HelpfulYesTotal': helpfulYesTotal,
      'HelpfulNoTotal': helpfulNoTotal,
    };
  }
}

class Images {
  int? id;
  String? base64File;
  int? productImageType;

  Images({this.id, this.base64File, this.productImageType});

  Images.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    base64File = json['base64File'];
    productImageType = json['productImageType'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = id;
    data['base64File'] = base64File;
    data['productImageType'] = productImageType;
    return data;
  }
}
