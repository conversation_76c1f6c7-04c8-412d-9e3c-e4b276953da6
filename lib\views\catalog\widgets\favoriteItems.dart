import 'package:alderishop/components/common_snckbar.dart';
import 'package:alderishop/constants/constants.dart';
import 'package:alderishop/controllers/app_controller.dart';
import 'package:alderishop/controllers/favorites_controller.dart';
import 'package:alderishop/views/auth/sign_in.dart';
import 'package:flutter/material.dart';
import 'package:page_transition/page_transition.dart';
import 'package:provider/provider.dart';

class FavoriteItems extends StatefulWidget {
  final int? item;

  const FavoriteItems({super.key, this.item});

  @override
  State<FavoriteItems> createState() => _FavoriteItemsState();
}

class _FavoriteItemsState extends State<FavoriteItems> {
  bool isFavorited = false;
  @override
  void initState() {
    super.initState();
    _checkFavoriteStatus();
  }

  void _checkFavoriteStatus() {
    setState(() {
      isFavorited = FavoritesController.checkIfFromFavorites(widget.item ?? 0);
    });
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () async {
        if (AppController.isAuth) {
          if (isFavorited == true) {
            var result =
                await Provider.of<FavoritesController>(context, listen: false)
                    .removeItemFromFavorites(widget.item ?? 0);
            setState(() {
              isFavorited = false;
            });
            if (result) {
              // ignore: use_build_context_synchronously
              successMsg(
                  context: context,
                  title: "تمت العملية بنجاح",
                  msg: "تم حذف المنتج من المفضلة بنجاح");
            } else {
              errorMsg(
                  // ignore: use_build_context_synchronously
                  context: context,
                  msg: "لم يتم حذف المنتج  من المفضلة يرجى المحاولة لاحقا",
                  title: "تحذير");
            }
          } else {
            var result =
                await Provider.of<FavoritesController>(context, listen: false)
                    .addItemToFavorites(widget.item ?? 0);
            setState(() {
              isFavorited = true;
            });
            if (result) {
              successMsg(
                // ignore: use_build_context_synchronously
                context: context,
                title: "تمت العملية بنجاح",
                msg: "تمت اضافة المنتج  الى المفضلة بنجاح",
              );
            } else {
              errorMsg(
                  // ignore: use_build_context_synchronously
                  context: context,
                  msg: "لم تتم اضافة المنتج  الى المفضلة يرجى المحاولة لاحقا",
                  title: "تحذير");
            }
          }
        } else {
          Navigator.push(
              context,
              PageTransition(
                  type: AppController.currentLangId == 2
                      ? PageTransitionType.leftToRight
                      : PageTransitionType.rightToLeftWithFade,
                  child: const SignInPage()));
        }
      },
      child: Icon(
        isFavorited == true ? Icons.favorite : Icons.favorite_border_outlined,
        color:
            isFavorited == true ? AppColors.RED_COLOR : AppColors.PRIMARY_COLOR,
        size: 25,
      ),
    );
  }
}
