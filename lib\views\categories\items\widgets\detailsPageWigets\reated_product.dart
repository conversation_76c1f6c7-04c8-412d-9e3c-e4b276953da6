import 'package:alderishop/constants/constants.dart';
import 'package:alderishop/controllers/app_controller.dart';
import 'package:alderishop/data/model/Products/productModel/productListModel.dart';
import 'package:alderishop/services/helper.dart';
import 'package:alderishop/views/home/<USER>/CustomText.dart';
import 'package:flutter/material.dart';
import 'package:page_transition/page_transition.dart';
import 'package:alderishop/views/categories/items/items_Details.dart';

import '../../../../home/<USER>/category/custom_categories_item.dart';

class RelatedProductsListWidget extends StatefulWidget {
  final List<ProductListModel> products;
  final bool isGrid;

  const RelatedProductsListWidget({Key? key, required this.products, required this.isGrid})
      : super(key: key);

  @override
  // ignore: library_private_types_in_public_api
  _RelatedProductsListWidgetState createState() =>
      _RelatedProductsListWidgetState();
}

class _RelatedProductsListWidgetState extends State<RelatedProductsListWidget> {
 @override
Widget build(BuildContext context) {
  return SizedBox(
    child: Column(
      children: [
        if (widget.products.isNotEmpty)
          Padding(
            padding: const EdgeInsets.only(right: 15, left: 15, top: 5),
            child: Row(
              children: [
                CustomText(
                  text: T('Related Products'),
                  fontSize: 13,
                  fontWeight: FontWeight.w700,
                  color: AppColors.PRIMARY_COLOR,
                ),
              ],
            ),
          ),
        const SizedBox(height: 5),

        widget.products.isNotEmpty
            ? SizedBox(
                width: AppController.W,
                height: AppController.h/2.4,
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  itemCount: widget.products.length > 4 ? 4 : widget.products.length,
                  itemBuilder: (BuildContext context, int index) {
                    return InkWell(
                      onTap: () {
                        Navigator.push(
                          context,
                          PageTransition(
                            type: AppController.currentLangId == 2
                                ? PageTransitionType.leftToRight
                                : PageTransitionType.rightToLeftWithFade,
                            child: ItemDetails(
                              item: widget.products[index].id,
                            ),
                          ),
                        );
                      },
                      child: SizedBox(
                        width: AppController.W/2,
                        child: CustomCategoryItem(
                          isGrid: widget.isGrid,
                          data: widget.products[index],
                        ),
                      ),
                    );
                  },
                ),
              )
            : const SizedBox.shrink(),
      ],
    ),
  );
}
}