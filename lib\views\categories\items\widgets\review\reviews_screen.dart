import 'package:alderishop/components/layout.dart';
import 'package:alderishop/constants/constants.dart';
import 'package:alderishop/controllers/products_controller.dart';
import 'package:alderishop/services/helper.dart';
import 'package:alderishop/views/home/<USER>/CustomText.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class AllReviewsScreen extends StatefulWidget {
  const AllReviewsScreen({super.key});

  @override
  State<AllReviewsScreen> createState() => _AllReviewsScreenState();
}

String _formatDate(String? dateTimeStr) {
  if (dateTimeStr == null || dateTimeStr.isEmpty) return "";
  try {
    DateTime dateTime = DateTime.parse(dateTimeStr);
    return "${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')}";
  } catch (e) {
    return dateTimeStr;
  }
}

class _AllReviewsScreenState extends State<AllReviewsScreen> {
  int selectedIndex = 0;

  @override
  Widget build(BuildContext context) {
    var data = Provider.of<ProductsController>(context).reviews;

    // Filtered list based on selected index
    var filteredData = selectedIndex == 0
        ? data
        : data.where((review) => review.rate == selectedIndex).toList();

    int totalStars = data.fold(0, (sum, review) => sum + (review.rate ?? 0));
    double average = data.isNotEmpty ? totalStars / data.length : 0;

    final List<String> filters = [T("All"), "1", "2", "3", "4", "5"];

    return ApplicationLayout(
      content: SingleChildScrollView(
        child: Column(
          children: [
            Container(
              margin: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
              width: double.infinity,
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                color: AppColors.WHITE_COLOR,
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withOpacity(0.1),
                    spreadRadius: 5,
                    blurRadius: 1,
                    offset: const Offset(0, 3),
                  ),
                ],
                borderRadius: BorderRadius.circular(5),
              ),
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      CustomText(
                        text: T("Total Reviews"),
                        fontSize: 13,
                        fontWeight: FontWeight.w600,
                        color: AppColors.PRIMARY_COLOR,
                      ),
                      Row(
                        children: [
                          const Icon(Icons.star, color: Colors.amber, size: 18),
                          const SizedBox(width: 4),
                          CustomText(
                            text: average.toStringAsFixed(1),
                            fontSize: 13,
                            fontWeight: FontWeight.w600,
                            color: AppColors.PRIMARY_COLOR,
                          ),
                        ],
                      ),
                    ],
                  ),
                  const SizedBox(height: 10),
                  SizedBox(
                    height: 35,
                    child: ListView.builder(
                      scrollDirection: Axis.horizontal,
                      itemCount: filters.length,
                      itemBuilder: (context, index) {
                        final isSelected = index == selectedIndex;
                        return GestureDetector(
                          onTap: () {
                            setState(() {
                              selectedIndex = index;
                            });
                          },
                          child: Container(
                            margin: const EdgeInsets.symmetric(horizontal: 4),
                            padding: const EdgeInsets.symmetric(
                                horizontal: 8, vertical: 5),
                            decoration: BoxDecoration(
                              color: isSelected
                                  ? Colors.orange
                                  : Colors.grey.shade300,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Row(
                              children: [
                                Text(
                                  filters[index],
                                  style: TextStyle(
                                    color: isSelected
                                        ? Colors.white
                                        : Colors.black,
                                  ),
                                ),
                                const SizedBox(width: 4),
                                const Icon(Icons.star,
                                    color: Colors.amber, size: 18),
                              ],
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
            ListView.builder(
              scrollDirection: Axis.vertical,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: filteredData.length,
              itemBuilder: (context, index) {
                final review = filteredData[index];
                return Container(
                  margin:
                      const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: AppColors.WHITE_COLOR,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.grey.withOpacity(0.1),
                        spreadRadius: 5,
                        blurRadius: 1,
                        offset: const Offset(0, 3),
                      ),
                    ],
                    borderRadius: BorderRadius.circular(5),
                  ),
                  child: Padding(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(
                          height: 15,
                          child: ListView.builder(
                            scrollDirection: Axis.horizontal,
                            itemCount: review.rate ?? 0,
                            itemBuilder: (BuildContext context, int _) {
                              return Image.asset(
                                'assets/img/star.png',
                                height: 5,
                              );
                            },
                          ),
                        ),
                        const SizedBox(height: 5),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            CustomText(
                              text: "${review.customerName ?? ""} | ",
                              fontSize: 11,
                              fontWeight: FontWeight.w400,
                              color: AppColors.SOFT_GREY,
                            ),
                            CustomText(
                              text: _formatDate(review.createdAt),
                              fontSize: 10,
                              fontWeight: FontWeight.w400,
                              color: AppColors.SOFT_GREY,
                            ),
                          ],
                        ),
                        const SizedBox(height: 5),
                        CustomText(
                          text: review.comment ?? "",
                          fontSize: 12,
                          fontWeight: FontWeight.w700,
                          color: AppColors.SOFT_GREY,
                        ),
                        const SizedBox(height: 5),
                        CustomText(
                          text: "Product : ${review.productName ?? ""}",
                          fontSize: 11,
                          fontWeight: FontWeight.w400,
                          color: AppColors.SOFT_GREY,
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ],
        ),
      ),
      selectedBottomNavbarItem: BottomNavbarItems.home,
    );
  }
}
