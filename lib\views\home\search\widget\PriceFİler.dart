import 'package:alderishop/constants/constants.dart';
import 'package:alderishop/controllers/app_controller.dart';
import 'package:alderishop/services/helper.dart';
import 'package:flutter/material.dart';

class PricefilerWidget extends StatefulWidget {
  const PricefilerWidget({super.key});

  @override
  State<PricefilerWidget> createState() => _PricefilerWidgetState();
}

class _PricefilerWidgetState extends State<PricefilerWidget> {
  final TextEditingController maxPriceController = TextEditingController();
  final TextEditingController minPriceController = TextEditingController();
  @override
  void dispose() {
    maxPriceController.dispose();
    minPriceController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: Container(
            height: 30,
            padding: const EdgeInsets.only(bottom: 3),
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(5),
                border: Border.all(color: AppColors.BLACK_COLOR)),
            child: TextField(
              controller: maxPriceController,
              textAlign: TextAlign.center,
              decoration: InputDecoration(
                hintText: T("Maximum "),
                hintStyle: TextStyle(
                  color: AppColors.BLACK_COLOR.withOpacity(0.6),
                  fontSize: 12,
                ),
                border: InputBorder.none,
              ),
              style: TextStyle(
                color: AppColors.BLACK_COLOR,
                fontSize: 12,
              ),
              onChanged: (value) {
                setState(() {});
              },
            ),
          ),
        ),
        const SizedBox(width: 10),
        Expanded(
          child: Container(
            height: 30,
            padding: const EdgeInsets.only(bottom: 3),
            width: AppController.W / 3,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(5),
                border: Border.all(color: AppColors.BLACK_COLOR)),
            child: TextField(
              controller: minPriceController,
              textAlign: TextAlign.center,
              decoration: InputDecoration(
                hintText: T("Minimum"),
                hintStyle: TextStyle(
                  color: AppColors.BLACK_COLOR.withOpacity(0.6),
                  fontSize: 12,
                ),
                border: InputBorder.none,
              ),
              style: TextStyle(
                color: AppColors.BLACK_COLOR,
                fontSize: 12,
              ),
              onChanged: (value) {
                setState(() {});
              },
            ),
          ),
        ),
      ],
    );
  }
}
