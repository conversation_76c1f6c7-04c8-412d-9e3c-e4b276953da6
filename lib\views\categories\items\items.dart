import 'package:alderishop/controllers/products_controller.dart';
import 'package:alderishop/data/model/categories/categroy_model.dart';
import 'package:alderishop/views/categories/items/items_Details.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';

class ProductGrid extends StatelessWidget {
  final ProductsController providerData;
  final AnimationController? animationController;

  const ProductGrid({
    Key? key,
    required this.providerData,
    required this.animationController,
    required CategoryModel categoryItem,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height,
      padding: const EdgeInsets.symmetric(horizontal: 5),
      color: Colors.white,
      child: SingleChildScrollView(
        child: AlignedGridView.count(
          crossAxisCount: MediaQuery.of(context).size.width > 700 ? 3 : 2,
          mainAxisSpacing: 10,
          crossAxisSpacing: 5,
          physics: const NeverScrollableScrollPhysics(),
          scrollDirection: Axis.vertical,
          shrinkWrap: true,
          itemCount: providerData.productData.length,
          itemBuilder: (BuildContext context, int index) {
            final int count = providerData.productData.length;

            Tween<double>(begin: 0.0, end: 1.0).animate(
              CurvedAnimation(
                parent: animationController!,
                curve: Interval((1 / count) * index, 1.0,
                    curve: Curves.fastOutSlowIn),
              ),
            );
            animationController?.forward();

            return InkWell(
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => ItemDetails(
                      item: providerData.productData[index].id,
                    ),
                  ),
                );
              },
              child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 2),
                  child: Text("")
                  // CustomCategoryItem(
                  //   width: 170,
                  //   animation: animation,
                  //   animationController: animationController,
                  //   data: providerData.productData[index],
                  // ),
                  ),
            );
          },
        ),
      ),
    );
  }
}

class ContestTabHeader extends SliverPersistentHeaderDelegate {
  ContestTabHeader(
    this.searchUI,
  );
  final Widget searchUI;

  @override
  Widget build(
      BuildContext context, double shrinkOffset, bool overlapsContent) {
    return searchUI;
  }

  @override
  double get maxExtent => 50.0;

  @override
  double get minExtent => 50.0;

  @override
  bool shouldRebuild(SliverPersistentHeaderDelegate oldDelegate) {
    return true;
  }
}
