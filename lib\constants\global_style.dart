/*
this is global style
This file is used to styling a whole application
 */

import 'package:alderishop/constants/constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class GlobalStyle {
  // appBar
  static const Color appBarIconThemeColor = Colors.black;
  static const double appBarElevation = 0;
  static const SystemUiOverlayStyle appBarSystemOverlayStyle =
      SystemUiOverlayStyle.dark;
  static const Color appBarBackgroundColor = Colors.white;
  static const TextStyle appBarTitle =
      TextStyle(fontSize: 18, color: Colors.black);
  /*
  this is used for height at product card using gridDelegate
  if you change font size or using custom font such as google font, sometimes there is an error said
  "Bottom overflowed by xx pixel" depends on the font height (Every font has a different height)
  so you need to change below
  */
  static const double gridDelegateRatio = 0.625; // lower is more longer
  static const double gridDelegateFlashsaleRatio =
      0.597; // lower is more longer
  static const double horizontalProductHeightMultiplication =
      1.90; // higher is more longer

  // styling product card
  static TextStyle productName =
      TextStyle(fontSize: 12, color: AppColors.CHARCOAL);

  static const TextStyle productPrice =
      TextStyle(fontSize: 13, fontWeight: FontWeight.bold);

  static TextStyle productPriceDiscounted = TextStyle(
    fontSize: 12,
    color: AppColors.SOFT_GREY,
    decoration: TextDecoration.lineThrough,
  );

  static TextStyle productSale =
      TextStyle(fontSize: 11, color: AppColors.SOFT_GREY);

  static TextStyle productLocation =
      TextStyle(fontSize: 11, color: AppColors.SOFT_GREY);

  static TextStyle productTotalReview =
      TextStyle(fontSize: 11, color: AppColors.SOFT_GREY);

  // search filter
  static TextStyle filterTitle =
      const TextStyle(fontSize: 16, fontWeight: FontWeight.bold);

  // detail product
  static const TextStyle detailProductPrice =
      TextStyle(fontSize: 20, fontWeight: FontWeight.bold);

  static const TextStyle sectionTitle =
      TextStyle(fontSize: 16, fontWeight: FontWeight.bold);

  static TextStyle viewAll = TextStyle(
      fontSize: 13,
      fontWeight: FontWeight.bold,
      color: AppColors.PRIMARY_COLOR);

  static const TextStyle paymentTotalPrice =
      TextStyle(fontSize: 14, fontWeight: FontWeight.bold);

  // delivery
  static const TextStyle deliveryPrice = TextStyle(fontSize: 15);

  static TextStyle deliveryTotalPrice = TextStyle(
      fontSize: 14, fontWeight: FontWeight.bold, color: AppColors.ASSENT_COLOR);

  static const TextStyle chooseCourier =
      TextStyle(fontSize: 16, fontWeight: FontWeight.bold);

  static const TextStyle courierTitle =
      TextStyle(fontSize: 15, fontWeight: FontWeight.bold);

  // static const TextStyle courierType = TextStyle(fontSize: 15, color: CHARCOAL);

  // shopping cart
  static TextStyle shoppingCartTotalPrice = TextStyle(
      fontSize: 16, fontWeight: FontWeight.bold, color: AppColors.ASSENT_COLOR);

  static const TextStyle shoppingCartOtherProduct = TextStyle(
    fontSize: 12,
  );

  // account information
  static TextStyle accountInformationLabel = TextStyle(
      fontSize: 15, color: AppColors.BLACK_GREY, fontWeight: FontWeight.normal);

  static const TextStyle accountInformationValue =
      TextStyle(fontSize: 16, color: Colors.black);

  static TextStyle accountInformationEdit = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.bold,
    color: AppColors.PRIMARY_COLOR,
  );

  // address
  static const TextStyle addressTitle =
      TextStyle(fontSize: 16, fontWeight: FontWeight.bold);

  static const TextStyle addressContent = TextStyle(fontSize: 12);

  static TextStyle addressAction =
      TextStyle(fontSize: 14, color: AppColors.PRIMARY_COLOR);

  // verification
  static TextStyle chooseVerificationTitle = TextStyle(
      fontSize: 16, fontWeight: FontWeight.bold, color: AppColors.CHARCOAL);

  static TextStyle chooseVerificationMessage =
      TextStyle(fontSize: 13, color: AppColors.BLACK_GREY);

  static TextStyle methodTitle = TextStyle(
      fontSize: 16, fontWeight: FontWeight.bold, color: AppColors.CHARCOAL);

  static TextStyle methodMessage =
      TextStyle(fontSize: 13, color: AppColors.BLACK_GREY);

  static TextStyle notReceiveCode =
      TextStyle(fontSize: 13, color: AppColors.SOFT_GREY);

  static TextStyle resendVerification =
      TextStyle(fontSize: 13, color: AppColors.PRIMARY_COLOR);

  static Icon iconBack =
      Icon(Icons.arrow_back, size: 16, color: AppColors.PRIMARY_COLOR);

  static TextStyle back =
      TextStyle(color: AppColors.PRIMARY_COLOR, fontWeight: FontWeight.w700);

  // authentication
  static TextStyle authTitle = TextStyle(
      fontSize: 14,
      fontWeight: FontWeight.bold,
      color: AppColors.PRIMARY_COLOR);

  static TextStyle authNotes =
      TextStyle(fontSize: 13, color: AppColors.SOFT_GREY);

  static TextStyle authSignWith =
      TextStyle(fontSize: 13, color: AppColors.SOFT_GREY);

  static TextStyle authBottom1 =
      TextStyle(fontSize: 13, color: AppColors.SOFT_GREY);

  static TextStyle authBottom2 =
      TextStyle(fontSize: 13, color: AppColors.PRIMARY_COLOR);

  static TextStyle resetPasswordNotes =
      TextStyle(fontSize: 14, color: AppColors.SOFT_GREY);

  // coupon
  static const TextStyle couponLimitedOffer =
      TextStyle(fontSize: 11, color: Colors.white);

  static const TextStyle couponName =
      TextStyle(fontSize: 15, fontWeight: FontWeight.bold);

  static TextStyle couponExpired =
      TextStyle(fontSize: 13, color: AppColors.SOFT_GREY);

  static Icon iconTime =
      Icon(Icons.access_time, size: 14, color: AppColors.SOFT_GREY);
}
