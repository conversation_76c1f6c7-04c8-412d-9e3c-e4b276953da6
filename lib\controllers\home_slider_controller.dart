import 'package:alderishop/data/api/api.dart';
import 'package:alderishop/data/model/home/<USER>';
import 'package:flutter/material.dart';

class HomeSliderController with ChangeNotifier {
  List<SliderItem> sliderItem = [];

  Future<void> getSliders() async {
    try {
      var result = await Api.getOne(
        useBaseUrl2: false,
        action: "Slider",
      );
      if (result.data != null && result.data['data'] != null) {
        for (var element in result.data['data']) {
          sliderItem.add(SliderItem.fromJson(element));
        }
      }

      notifyListeners();
    } catch (e) {
      // print(e);
    }
  }
}
