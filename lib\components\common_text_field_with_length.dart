// ignore_for_file: public_member_api_docs, sort_constructors_first
//********************************* */

import 'package:alderishop/constants/constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

enum MyInputType { text, number, password, email }

class CommonInputWithMaxLength extends StatefulWidget {
  final String? label;
  final String? hintText;
  final String validationNull;
  final String? initialVal;
  final Color? fillColor;
  final Color? labelColor;
  final Color? cursorColor;
  final Color? borderColor;
  final bool isEnabled;
  final bool isPassword;
  final bool isMoney;
  final double borderRadiusBottomLeft;
  final double borderRadiusBottomRight;
  final double borderRadiusTopRight;
  final double borderRadiusTopLeft;
  final double borderRadiusAll;
  final double contentPaddingVertical;
  final double contentPaddingHorizantal;
  final TextAlign textAlign;
  final TextInputAction? textInputAction;
  final bool isAllowNull;
  final bool asideByAside;
  final Function? onSave;
  final Function? onSubmit;
  final Function? onChange;
  final MyInputType inputType;
  final Function? onEditingComplete;
  final int maxLines;
  final TextEditingController? controller;
  final FloatingLabelBehavior? floatingLabelBehavior;
  const CommonInputWithMaxLength({
    super.key,
    this.label,
    this.hintText,
    this.cursorColor = Colors.black,
    this.fillColor = const Color(0xffD5D7DA),
    this.labelColor = const Color(0xFF11235A),
    this.borderColor = const Color(0xffD5D7DA),
    this.validationNull = 'Required',
    this.initialVal = '',
    this.borderRadiusBottomLeft = 10,
    this.borderRadiusBottomRight = 10,
    this.borderRadiusTopRight = 10,
    this.borderRadiusTopLeft = 10,
    this.borderRadiusAll = 10,
    this.contentPaddingVertical = 5,
    this.contentPaddingHorizantal = 10,
    this.textAlign = TextAlign.start,
    this.isEnabled = true,
    this.isPassword = false,
    this.asideByAside = false,
    this.onSave,
    this.inputType = MyInputType.text,
    this.isAllowNull = false,
    this.onSubmit,
    this.onChange,
    this.maxLines = 1,
    this.controller,
    this.isMoney = false,
    this.textInputAction = TextInputAction.done,
    this.onEditingComplete,
    this.floatingLabelBehavior = FloatingLabelBehavior.auto,
  });

  @override
  State<CommonInputWithMaxLength> createState() =>
      _CommonInputWithMaxLengthState();
}

class _CommonInputWithMaxLengthState extends State<CommonInputWithMaxLength> {
  bool _obscureText = true;
  TextEditingController _passwordController = TextEditingController();

  @override
  void initState() {
    _passwordController =
        widget.controller ?? TextEditingController(text: widget.initialVal);

    super.initState();
  }

  // Toggles the password show status
  void _toggle() {
    setState(() {
      _obscureText = !_obscureText;
    });
  }

  @override
  Widget build(BuildContext context) {
    //final TextEditingController _controller = TextEditingController(text: initialVal);
    TextInputType? keyboardType;
    switch (widget.inputType) {
      case MyInputType.number:
        keyboardType = TextInputType.number;
        break;
      case MyInputType.email:
        keyboardType = TextInputType.emailAddress;
        break;
      default:
        keyboardType = TextInputType.text;
    }
    return Directionality(
      textDirection: TextDirection.rtl,
      child: TextFormField(
        controller: _passwordController,
        textInputAction: widget.textInputAction,
        obscureText: widget.inputType == MyInputType.password && _obscureText,

        keyboardType: keyboardType,
        enabled: widget.isEnabled,
        autocorrect: false,
        maxLines: widget.maxLines,
        onChanged: (value) {
          if (widget.onChange != null) {
            if (widget.isMoney) {
              var amount = value.toString().replaceAll(",", "");
              widget.onChange!(amount);
            } else {
              widget.onChange!(value);
            }
          }
        },
        onFieldSubmitted: (value) {
          if (widget.onEditingComplete != null) {
            widget.onEditingComplete!(value);
          }
        },
        cursorColor: widget.cursorColor,
        textAlign: widget.textAlign,
        textDirection: TextDirection.ltr,

        //onTap: () => _controller.selection = TextSelection(baseOffset: 0, extentOffset: _controller.text.length),
        decoration: InputDecoration(
          floatingLabelBehavior: widget.floatingLabelBehavior,
          suffixIcon: widget.isPassword
              ? IconButton(
                  focusNode: FocusNode(skipTraversal: true),
                  padding: const EdgeInsets.all(0),
                  icon: Icon(
                    size: 14,
                    _obscureText ? Icons.visibility : Icons.visibility_off,
                    color: const Color(0xff42474d),
                  ),
                  onPressed: () {
                    _toggle();
                  },
                )
              : null,
          filled: true, //<-- SEE HERE
          fillColor: Colors.transparent,
          isDense: true,
          // hintStyle: TextStyle(color: Colors.red),
          contentPadding: EdgeInsets.symmetric(
              vertical: widget.contentPaddingVertical,
              horizontal: widget.contentPaddingHorizantal),
          //border: OutlineInputBorder(borderRadius: BorderRadius.circular(10.0)),
          hintText: widget.label == null ? widget.hintText : null,

          hintStyle: TextStyle(
            fontSize: 13,
            color: widget.labelColor,
            height: 1,
          ),
          labelText: widget.label,
          labelStyle: TextStyle(
            fontSize: 15,
            color: widget.labelColor,
            height: 15,
          ),
          enabledBorder: OutlineInputBorder(
            borderSide: BorderSide(
              color: widget.borderColor!,
            ),
            borderRadius: widget.asideByAside
                ? BorderRadius.only(
                    bottomLeft: Radius.circular(widget.borderRadiusBottomLeft),
                    bottomRight:
                        Radius.circular(widget.borderRadiusBottomRight),
                    topRight: Radius.circular(widget.borderRadiusTopRight),
                    topLeft: Radius.circular(widget.borderRadiusTopLeft))
                : BorderRadius.all(Radius.circular(widget.borderRadiusAll)),
          ),
          disabledBorder: OutlineInputBorder(
            borderSide: BorderSide(
              color: AppColors.PRIMARY_COLOR,
            ),
            borderRadius: widget.asideByAside
                ? BorderRadius.only(
                    bottomLeft: Radius.circular(widget.borderRadiusBottomLeft),
                    bottomRight:
                        Radius.circular(widget.borderRadiusBottomRight),
                    topRight: Radius.circular(widget.borderRadiusTopRight),
                    topLeft: Radius.circular(widget.borderRadiusTopLeft))
                : BorderRadius.all(Radius.circular(widget.borderRadiusAll)),
          ),
          focusedBorder: OutlineInputBorder(
            borderSide: BorderSide(
                color: AppColors.PRIMARY_COLOR.withOpacity(0.4), width: 2),
            borderRadius: !widget.asideByAside
                ? BorderRadius.only(
                    bottomLeft: Radius.circular(widget.borderRadiusBottomLeft),
                    bottomRight:
                        Radius.circular(widget.borderRadiusBottomRight),
                    topRight: Radius.circular(widget.borderRadiusTopRight),
                    topLeft: Radius.circular(widget.borderRadiusTopLeft))
                : BorderRadius.all(Radius.circular(widget.borderRadiusAll)),
          ),
        ),
        validator: (value) {
          if (widget.isAllowNull) return null;
          if (value == null || value.trim().isEmpty) {
            return widget.validationNull;
          }
          if (widget.inputType == MyInputType.number) {
            try {
              var amount = value.toString().replaceAll(",", "");
              double.parse(amount);
            } catch (e) {
              return 'Please verify the number';
            }
          }
          return null;
        },
        onSaved: (value) => widget.onSave!(value),
      ),
    );
  }
}
