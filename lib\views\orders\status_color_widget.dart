import 'package:alderishop/constants/constants.dart';
import 'package:alderishop/services/helper.dart';
import 'package:alderishop/views/home/<USER>/CustomText.dart';
import 'package:flutter/material.dart';

class OrderStatusWidget extends StatelessWidget {
  final int? orderStatus;

  const OrderStatusWidget({Key? key, required this.orderStatus})
      : super(key: key);

  Color _getStatusColor() {
    switch (orderStatus) {
      // Extract enum value
      case 0:
        return Colors.grey; // Gray
      case 1:
        return Colors.orange; // Orange
      case 2:
        return Colors.blue; // Blue
      case 3:
        return Colors.purple; // Purple
      case 4:
        return Colors.green; // Green
      case 5:
        return Colors.green[700]!; // Dark Green
      case 6:
        return Colors.red; // Red
      case 7:
        return Colors.brown; // Brown
      case 8:
        return Colors.pink; // Pink
      case 9:
        return Colors.black; // Black
      default:
        return Colors.white;
    }
  }

  String _getStatusText() {
    switch (orderStatus) {
      case 0:
        return T('Payment Pending');
      case 1:
        return T('Pending');
      case 2:
        return T('Confirmed');
      case 3:
        return T('Preparing');
      case 4:
        return T('Out For Delivery');
      case 5:
        return T('Delivered');
      case 6:
        return T('Canceled');
      case 7:
        return T('Returned');
      case 8:
        return T('Refunded');
      case 9:
        return T('Failed');
      default:
        return T('Unknown'); // Default status text
    }
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        CustomText(
                    text: '${T("status")}: ',
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: AppColors.BLACK_COLOR,
                  ),  
        Container(
          decoration: BoxDecoration(color: _getStatusColor() ,
          borderRadius: BorderRadius.circular(5)),
          child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 15,vertical: 5),
            child: Row(
              children: [
                
                CustomText(
                  text: _getStatusText(),
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                  color: AppColors.WHITE_COLOR,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
