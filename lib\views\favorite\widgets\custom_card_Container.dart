// ignore: file_names
import 'package:flutter/material.dart';

// ignore: must_be_immutable
class CustomContainerWidget extends StatefulWidget {
  double? height;
  double? width;
  String text;
  Color color;
  double fontSize;
  CustomContainerWidget(
      {super.key,
      required this.color,
      this.height,
      required this.fontSize,
      this.width,
      required this.text});

  @override
  State<CustomContainerWidget> createState() => _CustomContainerWidgetState();
}

class _CustomContainerWidgetState extends State<CustomContainerWidget> {
  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
          color: widget.color, borderRadius: BorderRadius.circular(5)),
      height: widget.height,
      width: widget.width,
      margin: const EdgeInsets.symmetric(horizontal: 5, vertical: 8),
      child: Center(
        child: Text(
          widget.text,
          style: TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.w600,
            fontSize: widget.fontSize,
          ),
        ),
      ),
    );
  }
}
