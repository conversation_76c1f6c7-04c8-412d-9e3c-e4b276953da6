import 'package:alderishop/components/common_header_deligate_widget.dart';
import 'package:alderishop/components/layout.dart';
import 'package:alderishop/constants/constants.dart';
import 'package:alderishop/controllers/favorites_controller.dart';
import 'package:alderishop/services/helper.dart';

import 'package:alderishop/components/common_header_widget.dart';
import 'package:alderishop/views/favorite/widgets/favorite_list_widget.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

class FavoritePage extends StatefulWidget {
  const FavoritePage({Key? key}) : super(key: key);

  @override
  State<FavoritePage> createState() => _FavoritePageState();
}

class _FavoritePageState extends State<FavoritePage>
    with TickerProviderStateMixin {
  AnimationController? animationController;
  final ScrollController _scrollController = ScrollController();
  final RefreshController _refreshController =
      RefreshController(initialRefresh: false);
  bool _isLoading = false;
  String searchTerm = "";

  @override
  void initState() {
    animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _onRefresh();
    super.initState();
  }

  @override
  void dispose() {
    animationController?.dispose();
    _scrollController.dispose();
    _refreshController.dispose();
    super.dispose();
  }

  void _onRefresh() async {
    setState(() {
      _isLoading = true;
    });
    await Provider.of<FavoritesController>(context, listen: false)
        .getFavorites();

    _refreshController.refreshCompleted();
    setState(() {
      _isLoading = false;
    });
  }

  void _onLoading() async {
    await Provider.of<FavoritesController>(context, listen: false)
        .getFavorites();
    if (mounted) {
      setState(() {});
    }
    _refreshController.loadComplete();
  }

  @override
  Widget build(BuildContext context) {
    final providerData =
        Provider.of<FavoritesController>(context).favoritesList;
    return ApplicationLayout(
      selectedBottomNavbarItem: BottomNavbarItems.none,
      content: Stack(
        children: [
          Column(
            children: <Widget>[
              Expanded(
                child: NestedScrollView(
                  controller: _scrollController,
                  headerSliverBuilder:
                      (BuildContext context, bool innerBoxIsScrolled) {
                    return <Widget>[
                      SliverList(
                        delegate: SliverChildBuilderDelegate(
                          (BuildContext context, int index) {
                            return const Column(
                              children: <Widget>[SizedBox()],
                            );
                          },
                          childCount: 1,
                        ),
                      ),
                      SliverPersistentHeader(
                        pinned: true,
                        floating: true,
                        delegate: CommonHeaderDelegateWidget(
                          CommonCategoriesHeader(text: T('My Favorites')),
                        ),
                      ),
                    ];
                  },
                  body: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 5),
                    color: Colors.white,
                    child: Column(
                      children: [
                        Padding(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 20, vertical: 10),
                          child: Container(
                            padding: const EdgeInsets.symmetric(horizontal: 20),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(10),
                              color: AppColors.BLACK_GREY.withOpacity(0.1),
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                Expanded(
                                  child: TextField(
                                    decoration: InputDecoration(
                                      hintText: T("Search My Favorites"),
                                      hintStyle: TextStyle(
                                        color: AppColors.BLACK_COLOR,
                                        fontSize: 12,
                                      ),
                                      border: InputBorder.none,
                                    ),
                                    style: TextStyle(
                                      color: AppColors.BLACK_COLOR,
                                      fontSize: 12,
                                    ),
                                    onChanged: (value) {
                                      setState(() {
                                        searchTerm = value;
                                      });
                                    },
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                        Expanded(
                          child: SmartRefresher(
                            enablePullDown: true,
                            enablePullUp: true,
                            header: const WaterDropHeader(),
                            footer: CustomFooter(
                              builder: (context, mode) {
                                Widget body;
                                if (mode == LoadStatus.idle) {
                                  body = Text(T("Pull up to load"));
                                } else if (mode == LoadStatus.failed) {
                                  body = Text(T("Load Failed! Click retry!"));
                                } else if (mode == LoadStatus.canLoading) {
                                  body = Text(T("Release to load more"));
                                } else {
                                  body = Text(T("No more data"));
                                }
                                return SizedBox(
                                  height: 55.0,
                                  child: Center(child: body),
                                );
                              },
                            ),
                            controller: _refreshController,
                            onRefresh: _onRefresh,
                            onLoading: _onLoading,
                            child: FavoriteListWidget(
                              searchTerm: searchTerm,
                              favorites: providerData,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
          if (_isLoading)
            const SizedBox(
              width: double.infinity,
              height: double.infinity,
              child: Center(
                child: CircularProgressIndicator(),
              ),
            ),
        ],
      ),
    );
  }
}
