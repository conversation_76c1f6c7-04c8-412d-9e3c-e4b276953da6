import 'package:alderishop/components/common_worning_info_widget.dart';
import 'package:alderishop/constants/constants.dart';
import 'package:alderishop/controllers/app_controller.dart';
import 'package:alderishop/controllers/auth_Controller.dart';
import 'package:alderishop/data/model/Products/productModel/productModel.dart';
import 'package:alderishop/data/model/auth_model.dart';
import 'package:alderishop/services/helper.dart';
import "package:alderishop/views/home/<USER>/CustomText.dart";
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ProductDetailsTitleWidget extends StatelessWidget {
  const ProductDetailsTitleWidget({super.key, required this.item});
  final ProductModel item;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: AppController.W - 30,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CustomText(
            text: item.shortDescription ?? "",
            fontSize: 12.sp,
            fontWeight: FontWeight.w700,
            color: AppColors.PRIMARY_COLOR,
          ),
          const SizedBox(height: 5),
          AppController.isAuth == false
              ? CommonWorningInfoWidget(
                  text: T("This price is not for wholesale customer"),
                )
              : AuthController.getUserType() == UserTypes.businessCustomer
                  ? CommonWorningInfoWidget(
                      text: T("This price is not for wholesale customer"),
                    )
                  : const SizedBox.shrink(),
          const SizedBox(height: 5),
          Row(
            children: [
              CustomText(
                text: "${T("Price")} :",
                fontSize: 16,
                fontWeight: FontWeight.w700,
                color: AppColors.Theard_COLOR,
              ),
              CustomText(
                text: "${item.price} IQD",
                fontSize: 16,
                fontWeight: FontWeight.w700,
                color: AppColors.PRIMARY_COLOR,
              ),
           
             
            ],
          ),
           
          Row(
            children: [
              CustomText(
                text: "${T("profit Point")} :",
                fontSize: 12,
                fontWeight: FontWeight.w700,
                color: AppColors.RED_COLOR,
              ),
              CustomText(
                text: item.profitPointOnBuy.toString(),
                fontSize: 12,
                fontWeight: FontWeight.w700,
                color: AppColors.RED_COLOR,
              ),
            ],
          ),
        ],
      ),
    );
  }
}
