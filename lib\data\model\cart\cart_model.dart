class CartModel {
  int? id;
  int? userId;
  List<CartItems>? cartItems;
  int? totalItems;

  CartModel({this.id, this.userId, this.cartItems, this.totalItems});

  CartModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    userId = json['userId'];
    if (json['cartItems'] != null) {
      cartItems = <CartItems>[];
      json['cartItems'].forEach((v) {
        cartItems!.add(new CartItems.fromJson(v));
      });
    }
    totalItems = json['totalItems'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['userId'] = this.userId;
    if (this.cartItems != null) {
      data['cartItems'] = this.cartItems!.map((v) => v.toJson()).toList();
    }
    data['totalItems'] = this.totalItems;
    return data;
  }
}

class CartItems {
  int? id;
  int? cartId;
  int? itemId;
  String? itemName;
  String? itemImage;
  int? quantity;
  double? itemPrice;
  List<SelectedOptions>? selectedOptions;

  CartItems(
      {this.id,
      this.cartId,
      this.itemId,
      this.itemName,
      this.itemImage,
      this.quantity,
      this.itemPrice,
      this.selectedOptions});

  CartItems.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    cartId = json['cartId'];
    itemId = json['itemId'];
    itemName = json['itemName'];
    itemImage = json['itemImage'];
    quantity = json['quantity'];
    itemPrice = json['itemPrice'];
    if (json['selectedOptions'] != null) {
      selectedOptions = <SelectedOptions>[];
      json['selectedOptions'].forEach((v) {
        selectedOptions!.add(SelectedOptions.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['cartId'] = this.cartId;
    data['itemId'] = this.itemId;
    data['itemName'] = this.itemName;
    data['itemImage'] = this.itemImage;
    data['quantity'] = this.quantity;
    data['itemPrice'] = this.itemPrice;
    if (this.selectedOptions != null) {
      data['selectedOptions'] =
          this.selectedOptions!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class SelectedOptions {
  bool? status;
  int? id;
  int? productAttributeId;
  String? name;
  String? value;
  bool? isSelected;
  double? newPrice;

  SelectedOptions(
      {this.status,
      this.id,
      this.productAttributeId,
      this.name,
      this.value,
      this.isSelected,
      this.newPrice});

  SelectedOptions.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    id = json['id'];
    productAttributeId = json['productAttributeId'];
    name = json['name'];
    value = json['value'];
    isSelected = json['isSelected'];
    newPrice = json['newPrice'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['status'] = this.status;
    data['id'] = this.id;
    data['productAttributeId'] = this.productAttributeId;
    data['name'] = this.name;
    data['value'] = this.value;
    data['isSelected'] = this.isSelected;
    data['newPrice'] = this.newPrice;
    return data;
  }
}

class CartHelperModel {
  int? userId;
  int? productId;
  int? cartItemId;
  int? quantity;
  List<int>? selectedProductAttributeOptionIds;

  CartHelperModel({
    this.userId,
    this.productId,
    this.cartItemId,
    this.quantity,
    this.selectedProductAttributeOptionIds,
  });

  CartHelperModel.fromJson(Map<String, dynamic> json) {
    userId = json['userId'];
    productId = json['productId'];
    cartItemId = json['cartItemId'];
    quantity = json['quantity'];
    if (json['selectedProductAttributeOptionIds'] != null) {
      selectedProductAttributeOptionIds = <int>[];
      json['selectedProductAttributeOptionIds'].forEach((v) {
        // Directly add the integer value.
        selectedProductAttributeOptionIds!.add(v);
        // If needed, you can cast or parse the value:
        // selectedProductAttributeOptionIds!.add(v is int ? v : int.parse(v.toString()));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['userId'] = userId;
    data['productId'] = productId;
    data['cartItemId'] = cartItemId;
    data['quantity'] = quantity;
    if (selectedProductAttributeOptionIds != null) {
      // No need to call toJson() on ints.
      data['selectedProductAttributeOptionIds'] =
          selectedProductAttributeOptionIds;
    }
    return data;
  }
}
