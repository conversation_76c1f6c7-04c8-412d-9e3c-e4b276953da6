import 'package:alderishop/controllers/app_controller.dart';
import 'package:alderishop/controllers/products_controller.dart';

import 'package:alderishop/views/catalog/items_Details.dart';
import 'package:alderishop/views/catalog/widgets/custom_categories_item.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:page_transition/page_transition.dart';
import 'package:provider/provider.dart';

class FeaturedCustomCategoriesList extends StatefulWidget {
  // final List<ItemsListModel> favoriteItems;
  final bool isGrid;

  const FeaturedCustomCategoriesList({
    Key? key,
    // required this.favoriteItems,
    required this.isGrid,
  }) : super(key: key);

  @override
  State<FeaturedCustomCategoriesList> createState() =>
      _FeaturedCustomCategoriesListState();
}

class _FeaturedCustomCategoriesListState
    extends State<FeaturedCustomCategoriesList> {
  @override
  Widget build(BuildContext context) {
    final dataProvider = Provider.of<ProductsController>(context);
    final itemList = dataProvider.featuredProduct;

    // Show max 2 items

    return AlignedGridView.count(
      crossAxisCount: widget.isGrid ? (AppController.W > 700 ? 3 : 2) : 1,
      crossAxisSpacing: 0,
      physics: const NeverScrollableScrollPhysics(),
      scrollDirection: Axis.vertical,
      shrinkWrap: true,
      itemCount: itemList.length > 4 ? 4 : itemList.length,
      itemBuilder: (BuildContext context, int index) {
        return InkWell(
          onTap: () {
            Navigator.push(
                context,
                PageTransition(
                  type: AppController.currentLangId == 2
                      ? PageTransitionType.leftToRight
                      : PageTransitionType.rightToLeftWithFade,
                  child: ItemDetails(
                    item: itemList[index].id,
                  ),
                ));
          },
          child: CustomCategoryItem(
            isGrid: widget.isGrid,
            data: itemList[index],
          ),
        );
      },
    );
  }
}
