
import 'package:alderishop/constants/constants.dart';
import 'package:alderishop/controllers/app_controller.dart';
import 'package:alderishop/views/CartPage/cart_items_screen.dart';
import 'package:alderishop/views/auth/sign_in.dart';
import 'package:alderishop/views/favorite/favorite.dart';
import 'package:alderishop/views/home/<USER>';
import 'package:alderishop/views/home/<USER>/SearchPage.dart';
import 'package:alderishop/views/orders/orders_screen.dart';

import 'package:flutter/material.dart';
class CommonButtomNavBarWidget extends StatefulWidget {
  final int selectedIndex;

  const CommonButtomNavBarWidget({super.key, required this.selectedIndex});

  @override
  State<CommonButtomNavBarWidget> createState() =>
      _CommonButtomNavBarWidgetState();
}

class _CommonButtomNavBarWidgetState extends State<CommonButtomNavBarWidget> {
  void _navigate(int index) {
    if (index == 0) {
      Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (context) => const SearchPage()));
    } else if (index == 1) {
      if (!AppController.isAuth) {
        Navigator.of(context).push(
            MaterialPageRoute(builder: (context) => const SignInPage()));
      } else {
        Navigator.of(context).pushReplacement(MaterialPageRoute(
            builder: (context) => const MyOrderesScreen()));
      }
    } else if (index == 2) {
      if (!AppController.isAuth) {
        Navigator.of(context).push(
            MaterialPageRoute(builder: (context) => const SignInPage()));
      } else {
        Navigator.of(context).pushReplacement(MaterialPageRoute(
            builder: (context) => const FavoritePage()));
      }
    } else if (index == 3) {
      Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (context) => const HomePage()));
    }else if (index == 4) {
      if (AppController.isAuth) {
                Navigator.of(context).pushReplacement(MaterialPageRoute(
                    builder: (context) => const CartItemsScreen()));
              } else {
                Navigator.of(context).pushReplacement(
                  MaterialPageRoute(builder: (context) => const SignInPage()),
                );
              }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      alignment: Alignment.center,
      decoration: BoxDecoration(
    color: Colors.white, // you can keep transparent if you want
    boxShadow: [
      BoxShadow(
        color: Colors.black.withOpacity(0.2), // shadow color
        spreadRadius: 1, // how much the shadow spreads
        blurRadius: 6, // blur effect
        offset: const Offset(0, 3), // position of shadow (x, y)
      ),
    ],
  ),
      height: 48,
      width: AppController.W,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 10),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
                        InkWell(
                          onTap: () => _navigate(3),
                          child: Container(
                            padding: const EdgeInsets.all(6),
                            child: Image.asset(
                              'assets/img/new/home.png',
                              height: 45,
                              width: 45,
                            ),
                          ),
                        ),     const SizedBox(width: 20), 
                       
                        InkWell(
                          onTap: () => _navigate(2),
                          child: Container(
                            padding: const EdgeInsets.all(6),
                            child: Image.asset(
                              'assets/img/new/fav.png',
                            height: 50,
                              width: 50,
                            ),
                          ),
                        ),
           const SizedBox(width: 10,),
             InkWell(
                  onTap: () => _navigate(4),
                  child: Container(
                    padding: const EdgeInsets.all(6),
                    child: Image.asset(
                      'assets/img/new/cart.png',
                      height: 50,
                      width: 50,
                    ),
                  ),
                ),   const SizedBox(width: 10,),              
            InkWell(
              onTap: () => _navigate(1),
              child: Container(
                padding: const EdgeInsets.all(6),
                child: Image.asset(
                  'assets/img/new/order.png',
                  height: 50,
                  width: 50,
                ),
              ),
            ),   const SizedBox(width: 10,),   
             InkWell(
              onTap: () => _navigate(0),
              child: Container(
                padding: const EdgeInsets.all(6),
                child: Image.asset(
                  'assets/img/new/search1.png',
                  color:AppColors.PRIMARY_COLOR,
               height: 50,
                  width: 50,
                ),
              ),
            ),

          ],
        ),
      ),
    );
  }
}
