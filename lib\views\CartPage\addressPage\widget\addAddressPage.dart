import 'package:alderishop/components/common_snckbar.dart';

import 'package:alderishop/components/common_text_form_field.dart';
import "package:alderishop/components/layout.dart";
import 'package:alderishop/constants/constants.dart';
import 'package:alderishop/controllers/addresses_controller.dart';
import 'package:alderishop/controllers/app_controller.dart';
import 'package:alderishop/data/model/addresses_model.dart';
import 'package:alderishop/services/helper.dart';
import 'package:alderishop/views/categories/widgets/Header.dart';

import 'package:alderishop/views/home/<USER>/%C4%B0magebgWidget.dart';
import 'package:alderishop/views/home/<USER>/CustomText.dart';
import 'package:alderishop/views/profile/profile.dart';

import 'package:flutter/material.dart';
import 'package:page_transition/page_transition.dart';
import 'package:provider/provider.dart';

class AddAddressesPage extends StatefulWidget {
  final int? id;

  const AddAddressesPage({super.key, this.id});

  @override
  State<AddAddressesPage> createState() => _AddAddressesPageState();
}

class _AddAddressesPageState extends State<AddAddressesPage> {
  final _formKey = GlobalKey<FormState>();
  var model = AddressesModel();

  @override
  void initState() {
    super.initState();
    if (widget.id != null) {
      model = Provider.of<AddressesController>(context, listen: false)
          .addresses
          .firstWhere((element) => element.id == widget.id);
    }
  }

  @override
  Widget build(BuildContext context) {
    return ApplicationLayout(
      selectedBottomNavbarItem: BottomNavbarItems.profile,
      content: SingleChildScrollView(
        child: Column(
          children: [
            CategoriesHeader(
              text: T(''),
              onTap: () {
                Navigator.push(
                    context,
                    PageTransition(
                        type: AppController.currentLangId == 2
                            ? PageTransitionType.leftToRight
                            : PageTransitionType.rightToLeftWithFade,
                        child: const ProfileScreen()));
              },
            ),
            ImageBgWidget(),
            SizedBox(height: AppController.h * 0.05),
            CustomText(
              text: T('Add Address'),
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: AppColors.BLACK_COLOR,
            ),
            Form(
              key: _formKey,
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    _buildField(
                      initialValue: model.addressName,
                      label: T('address name'),
                      hint: T("home, work"),
                      onChanged: (_) {},
                      validator: _requiredValidator,
                    ),
                    _buildField(
                      initialValue: model.country,
                      label: T('Country'),
                      onChanged: (value) => model.country = value,
                      validator: _requiredValidator,
                    ),
                    _buildField(
                      initialValue: model.city,
                      label: T('City'),
                      onChanged: (value) => model.city = value,
                      validator: _requiredValidator,
                    ),
                    _buildField(
                      initialValue: model.district,
                      label: T('District'),
                      onChanged: (value) => model.district = value,
                      validator: _requiredValidator,
                    ),
                    _buildField(
                      initialValue: model.street,
                      label: T('Street'),
                      onChanged: (value) => model.street = value,
                      validator: _requiredValidator,
                    ),
                    _buildField(
                      initialValue: model.buildingNumber,
                      label: T('Building Number'),
                      onChanged: (value) => model.buildingNumber = value,
                      validator: _requiredValidator,
                      keyboardType: TextInputType.number,
                    ),
                    _buildField(
                      initialValue: model.apartmentNumber,
                      label: T('Apartment Number'),
                      onChanged: (value) => model.apartmentNumber = value,
                      validator: _requiredValidator,
                      keyboardType: TextInputType.number,
                    ),
                    _buildField(
                      initialValue: model.postalCode?.toString() ?? '',
                      label: T('Postal Code'),
                      onChanged: (value) =>
                          model.postalCode = int.tryParse(value) ?? 0,
                      validator: (val) {
                        if (val == null || val.trim().isEmpty) {
                          return T("This field is required");
                        }
                        if (int.tryParse(val) == null) {
                          return T("Postal code must be a number");
                        }
                        return null;
                      },
                      keyboardType: TextInputType.number,
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 20),
            InkWell(
              onTap: () async {
                if (!_formKey.currentState!.validate()) return;

                pleaseWaitDialog(context: context, isShown: true);
                bool result;
                if (model.id != null) {
                  result = await Provider.of<AddressesController>(context,
                          listen: false)
                      .updateAddress(addresses: model);
                } else {
                  result = await Provider.of<AddressesController>(context,
                          listen: false)
                      .addAddress(model: model);
                }

                // ignore: use_build_context_synchronously
                pleaseWaitDialog(context: context, isShown: false);
                if (result) {
                  // ignore: use_build_context_synchronously
                  Navigator.push(
                      context,
                      PageTransition(
                          type: AppController.currentLangId == 2
                              ? PageTransitionType.leftToRight
                              : PageTransitionType.rightToLeftWithFade,
                          child: const ProfileScreen()));
                  // ignore: use_build_context_synchronously
                  successMsg(
                      context: context, msg: T("Operation done Successfully"));
                } else {
                  // ignore: use_build_context_synchronously
                  errorMsg(context: context, title: T('Error'));
                }
              },
              child: Container(
                width: AppController.W / 1.12,
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10),
                  color: AppColors.brown,
                ),
                child: Center(
                  child: CustomText(
                    text: T('Save'),
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppColors.WHITE_COLOR,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  String? _requiredValidator(String? value) {
    if (value == null || value.trim().isEmpty) {
      return T("This field is required");
    }
    return null;
  }

  Widget _buildField({
    required String? initialValue,
    required String label,
    String? hint,
    required Function(String) onChanged,
    String? Function(String?)? validator,
    TextInputType? keyboardType,
  }) {
    return Container(
      width: AppController.W / 1.15,
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
      child: CommonTextField(
        initialValue: initialValue,
        label: label,
        hint: hint,
        onChanged: onChanged,
        keyboardType: keyboardType,
        validator: validator,
      ),
    );
  }
}
