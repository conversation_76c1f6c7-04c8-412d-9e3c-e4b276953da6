import 'package:alderishop/constants/constants.dart';
import 'package:alderishop/controllers/addresses_controller.dart';
import 'package:alderishop/controllers/app_controller.dart';
import 'package:alderishop/controllers/auth_Controller.dart';
import 'package:alderishop/controllers/categores_controller.dart';
import 'package:alderishop/controllers/configration_controller.dart';
import 'package:alderishop/controllers/coupon_controller.dart';
import 'package:alderishop/controllers/favorites_controller.dart';
import 'package:alderishop/controllers/home_slider_controller.dart';
import 'package:alderishop/controllers/offer_controller.dart';
import 'package:alderishop/controllers/order_controller.dart';
import 'package:alderishop/controllers/products_controller.dart';
import 'package:alderishop/data/model/search/search_model.dart';
import 'package:alderishop/views/home/<USER>/%C4%B0magebgWidget.dart';
import 'package:alderishop/views/home/<USER>';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:provider/provider.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({Key? key}) : super(key: key);

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with SingleTickerProviderStateMixin {
  late final AnimationController _controller;
  late final Animation<Offset> _imageSlideAnimation;
  late final Animation<Offset> _textSlideAnimation;

  @override
  void initState() {
    //getToken();
    super.initState();

    // Configure animation controller with 1.5 second duration
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    );

    // Create image animation (slides from top)
    _imageSlideAnimation = Tween<Offset>(
      begin: const Offset(0, -1.0),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeOut,
    ));

    // Create text animation (slides from bottom)
    _textSlideAnimation = Tween<Offset>(
      begin: const Offset(0, 1.0),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeOut,
    ));

    // add post frame callback
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // Start the animation
      _controller.forward();
      init();
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void init() async {
    AuthController.tryAutoLogin();
    Provider.of<HomeSliderController>(context, listen: false).getSliders();

    Provider.of<ProductsController>(context, listen: false).getFeaturedProducts(
        resetAndRefresh: true,
        seachModel: ProductSearchFilter(
            pageNumber: 1, pageSize: 10, sort: SortEnum.popularProduct));
    Provider.of<ProductsController>(context, listen: false).getNewProducts(
        resetAndRefresh: true,
        seachModel: ProductSearchFilter(
            pageNumber: 1, pageSize: 10, sort: SortEnum.newest));

    Provider.of<CategoryController>(context, listen: false).getCategory();

    Provider.of<ConfigrationController>(context, listen: false)
        .getDeliveryType();

    Provider.of<ConfigrationController>(context, listen: false).getLanguages();
    Provider.of<ConfigrationController>(context, listen: false)
        .getPrivacyPolicy();
    Provider.of<AddressesController>(context, listen: false).getAddresses();
    Provider.of<ConfigrationController>(context, listen: false).getTermsToUse();
    Provider.of<CouponController>(context, listen: false).getCouponsForAll();
    Provider.of<OffersController>(context, listen: false).getOffers();
    Provider.of<CouponController>(context, listen: false).getMyCoupons();
    Provider.of<OrdersController>(context, listen: false).getOrders();
    if (AppController.isAuth) {
      FavoritesController.getFavoritesItems();
      Provider.of<FavoritesController>(context, listen: false).getFavorites();
    }
    if (AppController.isAuth) {}

    Future.delayed(const Duration(milliseconds: 4120), () async {
      Navigator.of(context).pushReplacement(MaterialPageRoute(
        builder: (context) => const HomePage(),
      ));
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.WHITE_COLOR,
      body: Column(
        children: [
          // Image with slide-in animation from top without bouncing below its target position
          SlideTransition(
            position: _imageSlideAnimation,
            child: ImageBgWidget(
              height: 240,
            ),
          ),
          Expanded(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
              
                SlideTransition(
                  position: _textSlideAnimation,
                  child: Column(
                    children: [
                       SvgPicture.asset(
        'assets/img/new/logo.svg',
       
      ),
                     
                      const SizedBox(height: 60),
                      Text(
                        "اهلا بكم في تطبيق البارودي",
                        style: TextStyle(
                            color: AppColors.PRIMARY_COLOR,
                            fontWeight: FontWeight.w600),
                      ),
                      const SizedBox(height: 10),
                      Text(
                        "المتجر الافضل لتسوق الملابس في العراق",
                        style: TextStyle(
                            color: AppColors.PRIMARY_COLOR,
                            fontWeight: FontWeight.w600),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
