import 'package:alderishop/components/common_snckbar.dart';
import 'package:alderishop/components/layout.dart';
import 'package:alderishop/constants/constants.dart';
import 'package:alderishop/controllers/app_controller.dart';
import 'package:alderishop/controllers/categores_controller.dart';
import 'package:alderishop/controllers/products_controller.dart';
import 'package:alderishop/data/model/Products/productModel/productListModel.dart';
import 'package:alderishop/data/model/categories/categroy_model.dart';
import 'package:alderishop/services/helper.dart';
import 'package:alderishop/views/categories/items/category_item.dart';
import 'package:alderishop/views/categories/items/items_Details.dart';
import 'package:alderishop/views/categories/items/widgets/filterWidget.dart';
import 'package:alderishop/views/home/<USER>/SearchPage.dart';
import 'package:alderishop/views/home/<USER>/SubcategoriesPage.dart';
import 'package:alderishop/views/home/<USER>/custom_categories_item.dart';
import 'package:alderishop/views/home/<USER>/subCategoriesitem.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:page_transition/page_transition.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

class SubcategoryAndProductPage extends StatefulWidget {
  final CategoryModel category;

  const SubcategoryAndProductPage({super.key, required this.category});

  @override
  State<SubcategoryAndProductPage> createState() =>
      _SubcategoryAndProductPageState();
}

class _SubcategoryAndProductPageState extends State<SubcategoryAndProductPage>
    with TickerProviderStateMixin {
  AnimationController? animationController;
  final RefreshController _refreshController =
      RefreshController(initialRefresh: false);
  bool isGrid = true;
  String _sortBy = 'none';

  final sortOptions = [
    SortOption(
      value: 'price',
      label: 'السعر من الأقل إلى الأعلى',
      icon: Icons.arrow_upward,
      iconColor: AppColors.PRIMARY_COLOR,
    ),
    SortOption(
      value: 'price_desc',
      label: 'السعر من الأعلى إلى الأقل',
      icon: Icons.arrow_downward,
      iconColor: AppColors.PRIMARY_COLOR,
    ),
    SortOption(
      value: 'rating',
      label: "الترتيب حسب التقييم",
      icon: Icons.star,
      iconColor: AppColors.brown,
    ),
    //     SortOption(
    //   value: 'az',
    //   label: 'الترتيب من أ إلى ي',
    //   icon: Icons.sort_by_alpha,
    //   iconColor: AppColors.PRIMARY_COLOR,
    // ),
    // SortOption(
    //   value: 'za',
    //   label: 'الترتيب من ي إلى أ',
    //   icon: Icons.sort_by_alpha,
    //   iconColor: AppColors.PRIMARY_COLOR,
    // ),
  ];
  @override
  void initState() {
    animationController = AnimationController(
        duration: const Duration(milliseconds: 1000), vsync: this);
    _onRefresh();
    super.initState();
  }

  void _applySort(List<ProductListModel> products) {
    if (_sortBy == 'price') {
      products.sort((a, b) => (a.price ?? 0).compareTo(b.price ?? 0));
    } else if (_sortBy == 'price_desc') {
      products.sort((a, b) => (b.price ?? 0).compareTo(a.price ?? 0));
    }
    //  else if (_sortBy == 'az') {
    //   products.sort((a, b) => (a.name ?? '').compareTo(b.name ?? ''));
    // } else if (_sortBy == 'za') {
    //   products.sort((a, b) => (b.name ?? '').compareTo(a.name ?? ''));
    // }
  }

  void _onRefresh() async {
    setState(() {});
    await Provider.of<ProductsController>(context, listen: false)
        .getProductCategories(
      categoryId: widget.category.id ?? 0,
    );

    _applySort(
        // ignore: use_build_context_synchronously
        Provider.of<ProductsController>(context, listen: false).productData);

    _refreshController.refreshCompleted();
    setState(() {});
  }

  final List<String> categories = ['صنف 1', 'صنف 2'];
  final List<String> products =
      List.generate(30, (index) => 'منتج رقم ${index + 1}');
  @override
  Widget build(BuildContext context) {
    final subcategories =
        Provider.of<CategoryController>(context).subcategories;
    final products = Provider.of<ProductsController>(context);
    final itemList = products.productData;
    final isLoading = products.isLoading;
    return ApplicationLayout(
      selectedBottomNavbarItem: BottomNavbarItems.none,
      content: SmartRefresher(
        controller: _refreshController,
        onRefresh: _onRefresh,
        enablePullDown: true,
        child: CustomScrollView(
          primary: true,
          slivers: [
            // ✅ Title
            SliverToBoxAdapter(
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Text(
                  T("تصنيفات فرعية"),
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
            ),

            // ✅ Subcategories Grid
            SliverToBoxAdapter(
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: AlignedGridView.count(
                  crossAxisCount: 2,
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: subcategories.length,
                  itemBuilder: (context, index) {
                    final category = subcategories[index];
                    return InkWell(
                      // onTap: () => _handleCategoryTap(category),
                      child: Subcategoriesitem(
                        category: category,
                        isGrid: false,
                      ),
                    );
                  },
                ),
              ),
            ),

            // ✅ Product Title
            SliverToBoxAdapter(
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Text(
                  T("المنتجات"),
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
            ),

            // ✅ Filter Bar
            SliverToBoxAdapter(
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: TopFilterBar(
                  isGrid: isGrid,
                  onToggleView: () => setState(() => isGrid = !isGrid),
                  onSort: (value) => setState(() {
                    _sortBy = value;
                    _applySort(itemList);
                  }),
                  onFilter: () => Navigator.push(
                    context,
                    MaterialPageRoute(builder: (_) => const SearchPage()),
                  ),
                  sortOptions: sortOptions,
                ),
              ),
            ),

            // ✅ Products Grid/List
            SliverToBoxAdapter(
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: products.productData.isEmpty && isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : AlignedGridView.count(
                        crossAxisCount:
                            isGrid ? (AppController.W > 700 ? 3 : 2) : 1,
                        mainAxisSpacing: 12,
                        crossAxisSpacing: 12,
                        physics: const NeverScrollableScrollPhysics(),
                        shrinkWrap: true,
                        itemCount: products.productData.length,
                        itemBuilder: (context, index) {
                          final product = products.productData[index];
                          return InkWell(
                            onTap: () => Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (_) => ItemDetails(item: product.id),
                              ),
                            ),
                            child: CustomCategoryItem(data: product),
                          );
                        },
                      ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// ignore: unused_element
class _SliverCategoryHeader extends SliverPersistentHeaderDelegate {
  final Widget child;

  _SliverCategoryHeader({required this.child});

  @override
  Widget build(
      BuildContext context, double shrinkOffset, bool overlapsContent) {
    return Material(
      elevation: 0,
      child: child,
    );
  }

  @override
  double get maxExtent => 200;

  @override
  double get minExtent => 0; // يختفي عند السكرول

  @override
  bool shouldRebuild(covariant _SliverCategoryHeader oldDelegate) {
    return true;
  }
}
