import 'package:alderishop/components/common_header_widget.dart';
import 'package:alderishop/components/layout.dart';
import 'package:alderishop/constants/constants.dart';
import 'package:alderishop/controllers/app_controller.dart';
import 'package:alderishop/data/model/chat_model.dart';

import 'package:chat_bubbles/bubbles/bubble_special_three.dart';
import 'package:emoji_picker_flutter/emoji_picker_flutter.dart';
import 'package:flutter/material.dart';

class ChatPage extends StatefulWidget {
  const ChatPage({super.key});

  @override
  State<ChatPage> createState() => _ChatPageState();
}

class _ChatPageState extends State<ChatPage>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  List<ModelChat> chat = [];
  final TextEditingController textController = TextEditingController();
  bool _showEmojiPicker = false; // Track the emoji picker visibility

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(vsync: this);
  }

  @override
  void dispose() {
    _controller.dispose();
    textController.dispose();
    super.dispose();
  }

  String myName = "chat1";

  @override
  Widget build(BuildContext context) {
    return ApplicationLayout(
      content: Column(
        children: [
          const CommonCategoriesHeader(text: ""),
          _buildHeader(),
          SizedBox(
            height: 10,
          ),
          SizedBox(
            height: 520,
            child: Column(
              children: [
                _buildChatList(),
                _buildMessageInput(),
                // Show the emoji picker when _showEmojiPicker is true
                if (_showEmojiPicker)
                  SizedBox(
                    height: 250,
                    child: EmojiPicker(
                      onEmojiSelected: (Category? category, Emoji emoji) {
                        setState(() {
                          // Insert the selected emoji into the text field
                          textController.text += emoji.emoji;
                        });
                      },
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
      selectedBottomNavbarItem: BottomNavbarItems.none,
    );
  }

  Widget _buildHeader() {
    return Container(
      width: AppController.W - 40,
      decoration: BoxDecoration(
        border: Border(bottom: BorderSide(color: AppColors.BLACK_GREY)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Image.asset("assets/img/new/chats.png"),
          Column(
            children: [
              Text(
                "محمد الاحمد",
                style: TextStyle(
                  color: AppColors.BLACK_COLOR,
                  fontWeight: FontWeight.bold,
                  fontSize: 18,
                ),
              ),
              const SizedBox(
                height: 2,
              ),
              Text(
                "00/00/0000",
                style: TextStyle(
                  color: AppColors.BLACK_COLOR,
                  fontWeight: FontWeight.bold,
                  fontSize: 15,
                ),
              ),
              const SizedBox(
                height: 10,
              ),
            ],
          ),
          Image.asset("assets/img/new/chat_bubble.png"),
        ],
      ),
    );
  }

  Widget _buildChatList() {
    return Expanded(
      child: ListView.builder(
        itemCount: chat.length,
        itemBuilder: (_, index) => BubbleSpecialThree(
          isSender: chat[index].sender == myName,
          text: chat[index].message,
          color: chat[index].sender == myName
              ? AppColors.SOFT_BLUE
              : Colors.grey.shade500,
          tail: false,
          textStyle: const TextStyle(color: Colors.black, fontSize: 14),
        ),
      ),
    );
  }

  Widget _buildMessageInput() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: textController,
              decoration: InputDecoration(
                prefixIcon: InkWell(
                    onTap: () {
                      setState(() {
                        _showEmojiPicker = !_showEmojiPicker;
                      });
                    },
                    child: Icon(Icons.emoji_emotions)),
                hintText: 'write your message',
                hintStyle: const TextStyle(
                  color: Colors.black54,
                  fontWeight: FontWeight.bold,
                ),
                filled: true,
                fillColor: AppColors.BLACK_COLOR.withOpacity(0.1),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(15),
                  borderSide: BorderSide.none,
                ),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 10,
                  vertical: 0,
                ),
              ),
            ),
          ),
          // IconButton(
          //   onPressed: () {
          //     setState(() {
          //       // Toggle the emoji picker visibility
          //       _showEmojiPicker = !_showEmojiPicker;
          //     });
          //   },
          //   icon: const Icon(Icons.emoji_emotions),
          // ),
          IconButton(
            onPressed: _sendMessage,
            icon: const Icon(Icons.send),
          ),
        ],
      ),
    );
  }

  void _sendMessage() {
    if (textController.text.trim().isNotEmpty) {
      setState(() {
        chat.add(ModelChat(textController.text.trim(), myName));
        textController.clear();
        _showEmojiPicker =
            false; // Close the emoji picker after sending a message
      });
    }
  }
}
