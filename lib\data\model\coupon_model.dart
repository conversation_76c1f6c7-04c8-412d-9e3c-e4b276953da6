class CouponModel {
  String? shortDescription;
  String? name;
  int? userId;
  int? priceByPoints;
  double? discount;
  int? discountType;
  String? code;
  bool? isUsed;
  int? id;
  String? createdBy;
  String? updatedBy;
  String? createdAt;
  DateTime? updatedAt;
  bool? isActive;
  bool? isDeleted;
  CouponStatus? couponStatus;
  CouponModel(
      {this.shortDescription,
      this.name,
      this.userId,
      this.priceByPoints,
      this.discount,
      this.discountType,
      this.code,
      this.isUsed,
      this.id,
      this.createdBy,
      this.updatedBy,
      this.createdAt,
      this.updatedAt,
      this.couponStatus,
      this.isActive,
      this.isDeleted});

  CouponModel.fromJson(Map<String, dynamic> json) {
    shortDescription = json['shortDescription'];
    name = json['name'];
    userId = json['userId'];
    priceByPoints = json['priceByPoints'];
    discount = json['discount'];
    discountType = json['discountType'];
    code = json['code'];
    isUsed = json['isUsed'];
    id = json['id'];
    createdBy = json['createdBy'];
    updatedBy = json['updatedBy'];
    createdAt = json['createdAt'];
    updatedAt = json['updatedAt'];
    isActive = json['isActive'];
    isDeleted = json['isDeleted'];
    couponStatus = json['couponStatus'] != null
        ? CouponStatus.values[json['couponStatus']]
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['shortDescription'] = this.shortDescription;
    data['name'] = this.name;
    data['userId'] = this.userId;
    data['priceByPoints'] = this.priceByPoints;
    data['discount'] = this.discount;
    data['discountType'] = this.discountType;
    data['code'] = this.code;
    data['isUsed'] = this.isUsed;
    data['id'] = this.id;
    data['createdBy'] = this.createdBy;
    data['updatedBy'] = this.updatedBy;
    data['createdAt'] = this.createdAt;
    data['updatedAt'] = this.updatedAt;
    data['isActive'] = this.isActive;
    data['isDeleted'] = this.isDeleted;
    data['couponStatus'] = couponStatus?.index;
    return data;
  }
}

enum DiscountType { Percentage, Amount }

enum CouponStatus {
  // ignore: constant_identifier_names
  Private,
  // ignore: constant_identifier_names
  Public,
  // ignore: constant_identifier_names
  ForBuy,
}
