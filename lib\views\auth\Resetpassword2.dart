import 'package:alderishop/components/CustomInputField.dart';
import 'package:alderishop/constants/constants.dart';
import 'package:alderishop/controllers/app_controller.dart';
import 'package:alderishop/services/helper.dart';
import 'package:alderishop/views/profile/widgets/customContanier.dart';
import 'package:alderishop/views/home/<USER>/%C4%B0magebgWidget.dart';
import 'package:alderishop/views/home/<USER>';
import 'package:alderishop/views/home/<USER>/CustomText.dart';
import 'package:flutter/material.dart';

class RestPasswordScreen extends StatefulWidget {
  const RestPasswordScreen({super.key});

  @override
  State<RestPasswordScreen> createState() => _RestPasswordScreenState();
}

class _RestPasswordScreenState extends State<RestPasswordScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          ImageBgWidget(
            height: 230,
          ),
          <PERSON><PERSON><PERSON><PERSON>(
            height: AppController.h * 0.07,
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Column(
              children: [
                Center(
                  child: CustomText(
                      text: T('Reset Password'),
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: AppColors.BLACK_COLOR),
                ),
                SizedBox(
                  height: AppController.h * 0.05,
                ),
                CustomInputField(
                  hintText: "password",
                  backgroundColor: AppColors.SOFT_BLUE.withOpacity(0.5),
                  isPassword: true,
                  keyboardType: TextInputType.visiblePassword,
                  onChanged: (value) {
                    setState(() {});
                  },
                ),
                CustomInputField(
                  hintText: "confirm password",
                  backgroundColor: AppColors.SOFT_BLUE.withOpacity(0.5),
                  isPassword: true,
                  keyboardType: TextInputType.visiblePassword,
                  onChanged: (value) {
                    setState(() {});
                  },
                ),
                InkWell(
                  onTap: () {
                    Navigator.of(context).pushReplacement(MaterialPageRoute(
                        builder: (context) => const HomePage()));
                  },
                  child: CustomContainer(
                    width: AppController.W,
                    text: T("ok"),
                    color: AppColors.brown,
                  ),
                )
              ],
            ),
          )
        ],
      ),
    );
  }
}
