import 'package:alderishop/components/common_cache_image.dart';
import 'package:alderishop/constants/constants.dart';
import 'package:alderishop/controllers/app_controller.dart';
import 'package:alderishop/data/api/api.dart';
import 'package:alderishop/data/model/order/order_model.dart';
import 'package:alderishop/views/catalog/items_Details.dart';
import 'package:alderishop/views/favorite/widgets/custom_card_Container.dart';
import 'package:alderishop/views/home/<USER>/CustomText.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

// ignore: must_be_immutable
class OrderDetailsContainer extends StatefulWidget {
  final OrderItem? myOrderModel;
  final String? text;

  const OrderDetailsContainer(
      {super.key, required this.myOrderModel, this.text});

  @override
  State<OrderDetailsContainer> createState() => _OrderDetailsContainerState();
}

class _OrderDetailsContainerState extends State<OrderDetailsContainer> {
  @override
  Widget build(BuildContext context) {
    // List<String> imageUrls = widget.myOrderModel?.product?.images
    //         ?.map((url) => '$baseUrl1$url')
    //         .toList() ??
    //     [];
    print(widget.myOrderModel?.id);

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 5),
      width: 320.w,
      decoration: BoxDecoration(
          color: AppColors.WHITE_COLOR,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.2),
              spreadRadius: 2,
              blurRadius: 5,
              offset: Offset(0, 3),
            ),
          ],
          borderRadius: BorderRadius.circular(15)),
      margin: const EdgeInsets.only(top: 10, bottom: 0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 10),
            child: InkWell(
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => ItemDetails(
                      item: widget.myOrderModel?.productId,
                    ),
                  ),
                );
              },
              child: Container(
                  height: 70,
                  width: 70,
                  decoration: BoxDecoration(
                      color: AppColors.WHITE_COLOR,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.2),
                          spreadRadius: 2,
                          blurRadius: 5,
                          offset: Offset(0, 3),
                        ),
                      ],
                      borderRadius: BorderRadius.circular(5)),
                  child: Padding(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 4, vertical: 4),
                    child: CachedImage(
                      fit: BoxFit.cover,
                      imageUrl:
                          "$baseUrl1/${widget.myOrderModel?.productImage}",
                    ),
                  )),
            ),
          ),
          Column(
            children: [
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 5),
                child: Column(
                  children: [
                    const SizedBox(
                      height: 10,
                    ),
                    SizedBox(
                      width: AppController.W / 2.1,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          SizedBox(
                            width: AppController.W / 2 - 20,
                            child: CustomText(
                              text: widget.myOrderModel?.productName ?? "",
                              fontSize: 12.sp,
                              fontWeight: FontWeight.bold,
                              color: AppColors.PRIMARY_COLOR,
                            ),
                          ),
                        ],
                      ),
                    ),
                    SizedBox(
                      width: AppController.W / 2.1,
                      child: SizedBox(
                        width: AppController.W / 2 - 20,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            CustomText(
                              text:
                                  "${widget.myOrderModel?.productPrice.toString() ?? ""} IQD",
                              fontSize: 12,
                              fontWeight: FontWeight.normal,
                              color: AppColors.PRIMARY_COLOR,
                            ),
                            CustomContainerWidget(
                                color: AppColors.PRIMARY_COLOR,
                                height: 18,
                                width: 80,
                                fontSize: 12,
                                text: widget.myOrderModel!.quantity.toString()),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              )
            ],
          ),
        ],
      ),
    );
  }
}
