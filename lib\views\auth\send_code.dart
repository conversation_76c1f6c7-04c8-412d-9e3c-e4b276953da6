import 'package:alderishop/constants/constants.dart';
import 'package:alderishop/controllers/app_controller.dart';
import 'package:alderishop/main.dart';
import 'package:alderishop/services/helper.dart';
import 'package:alderishop/views/auth/Resetpassword2.dart';
import 'package:alderishop/views/home/<USER>/%C4%B0magebgWidget.dart';
import 'package:alderishop/views/home/<USER>/CustomText.dart';
import 'package:flutter/material.dart';

class SendCodePage extends StatefulWidget {
  const SendCodePage({super.key});

  @override
  State<SendCodePage> createState() => _SendCodePageState();
}

class _SendCodePageState extends State<SendCodePage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          ImageBgWidget(
            height: 230,
          ),
          SizedBox(
            height: AppController.h * 0.07,
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Column(
              children: [
                Center(
                  child: CustomText(
                      text: T('Reset Password'),
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: AppColors.BLACK_COLOR),
                ),
                SizedBox(
                  height: AppController.h * 0.05,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    CustomText(
                        text: T('Code'),
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: AppColors.BLACK_COLOR),
                    CustomText(
                        text: T('send code'),
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: AppColors.brown),
                  ],
                ),
                SizedBox(
                  height: AppController.h * 0.02,
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 15),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      Container(
                        height: 50,
                        width: 70,
                        decoration: const BoxDecoration(
                          border: Border(
                            bottom: BorderSide(color: Colors.black),
                          ),
                        ),
                        child: const TextField(
                          textAlign: TextAlign.center,
                          decoration: InputDecoration(
                            border: InputBorder
                                .none, // no inner border, container provides border
                            contentPadding: EdgeInsets.symmetric(
                                vertical: 12), // to center text vertically
                          ),
                        ),
                      ),
                      Container(
                        height: 50,
                        decoration:const BoxDecoration(
                          border: Border(
                            bottom: BorderSide(color: Colors.black),
                          ),
                        ),
                        width: 70,
                        child: const TextField(
                          textAlign: TextAlign.center,
                          decoration: InputDecoration(border: InputBorder.none),
                        ),
                      ),
                      Container(
                        height: 50,
                        decoration:const BoxDecoration(
                          border: Border(
                            bottom: BorderSide(color: Colors.black),
                          ),
                        ),
                        width: 70,
                        child: const TextField(
                          textAlign: TextAlign.center,
                          decoration: InputDecoration(border: InputBorder.none),
                        ),
                      ),
                      Container(
                        height: 50,
                        decoration:const BoxDecoration(
                          border: Border(
                            bottom: BorderSide(color: Colors.black),
                          ),
                        ),
                        width: 70,
                        child: const TextField(
                          textAlign: TextAlign.center,
                          decoration: InputDecoration(border: InputBorder.none),
                        ),
                      ),
                    ],
                  ),
                ),
                SizedBox(
                  height: AppController.h * 0.03,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    CustomText(
                        text: T('03.18'),
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: AppColors.BLACK_COLOR),
                    InkWell(
                      onTap: () {
                        showresetPassword(confirmText: "Reset Password");
                      },
                      child: CustomText(
                          text: T('الرمز صالح'),
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: AppColors.BLACK_COLOR.withOpacity(0.4)),
                    ),
                  ],
                ),
              ],
            ),
          )
        ],
      ),
    );
  }
}

Future<bool?> showresetPassword({
  String? title,
  int? orderId,
  String? content,
  String? backText,
  String? confirmText,
}) async {
  title ??= T('Code accepted \nPlease enter a new password');
  content ??= T('');

  return showDialog<bool>(
    context: navigatorKey.currentContext!,
    builder: (BuildContext context) {
      return AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        contentPadding: EdgeInsets.zero,
        backgroundColor: AppColors.PRIMARY_COLOR,
        title: Text(
          title!,
          textAlign: TextAlign.center,
          style: TextStyle(
              color: AppColors.WHITE_COLOR,
              fontWeight: FontWeight.normal,
              fontSize: 14),
        ),
        content: SingleChildScrollView(
          child: ListBody(
            children: [
              Text(content!),
            ],
          ),
        ),
        actions: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                decoration: BoxDecoration(
                    color: AppColors.brown,
                    borderRadius: BorderRadius.circular(12)),
                width: AppController.W / 1.5,
                child: TextButton(
                  child: Text(
                    confirmText ?? T('Confirm'),
                    style: TextStyle(
                        color: AppColors.WHITE_COLOR,
                        fontWeight: FontWeight.bold),
                  ),
                  onPressed: () {
                    Navigator.of(context).pushReplacement(MaterialPageRoute(
                        builder: (context) => const RestPasswordScreen()));
                  },
                ),
              ),
            ],
          ),
        ],
      );
    },
  );
}
