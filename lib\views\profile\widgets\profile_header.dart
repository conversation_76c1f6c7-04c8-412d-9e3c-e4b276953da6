import 'package:alderishop/views/profile/profile.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../../../constants/constants.dart';
import '../../../controllers/app_controller.dart';
import '../../home/<USER>/CustomText.dart';

class ProfileHeader extends StatelessWidget {
  final String text;
  const ProfileHeader({super.key, required this.text});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 40.h,
      color: AppColors.WHITE_COLOR,
      child: Padding(
        padding: EdgeInsets.only(right: 25.w, left: 25.w),
        child: Row(
          children: [
            InkWell(
              onTap: () {
                Navigator.of(context).pushReplacement(
                  MaterialPageRoute(
                      builder: (context) => const ProfileScreen()),
                );
              },
              child: FaIcon(
                FontAwesomeIcons.arrowLeft,
                color: AppColors.PRIMARY_COLOR,
                size: 18,
              ),
            ),
            SizedBox(
              width: AppController.W * 0.05,
            ),
            CustomText(
                text: text,
                fontSize: 12.sp,
                fontWeight: FontWeight.w700,
                color: AppColors.PRIMARY_COLOR)
          ],
        ),
      ),
    );
  }
}

// widget.itemsList.title,