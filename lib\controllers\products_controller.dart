import 'package:alderishop/data/api/api.dart';
import 'package:alderishop/data/model/Products/attributesDto.dart';
import 'package:alderishop/data/model/Products/productModel/productListModel.dart';
import 'package:alderishop/data/model/Products/productModel/productModel.dart';
import 'package:alderishop/data/model/response.dart';
import 'package:alderishop/data/model/review_model.dart';
import 'package:alderishop/data/model/search/search_model.dart';
import 'package:flutter/material.dart';

class ProductsController extends ChangeNotifier {
  List<ProductListModel> productData = [];
  List<ProductListModel> newProducts = [];
  List<ProductListModel> relatedProduct = [];
  List<ProductListModel> featuredProduct = [];
  List<ReviewModel> reviews = [];
  ProductListModel? productReviews;
  ProductModel? productDetails;
  List<ProductAttribute>? productDetailsAttribute;
  List<int> favoritesProductsIds = [];
  List<ProductListModel> searchProducts = [];
  ProductSearchFilter searchFilter = ProductSearchFilter(attrFilter: []);
  int? selectedCategoryId;
  bool isLoading = false;
  final int _pageSize = 10;
  bool hasMore = true;
  List<AttributesDTO> attributes = [];
  int _getCurrentPage() {
    var currentPage = ((productData.length) / _pageSize).ceil() + 1;
    if (productData.isEmpty) currentPage = 1;
    return currentPage;
  }

  void setSelectedCategory(int id) {
    selectedCategoryId = id;
    notifyListeners();
  }

  int? _activeCategoryId;
  int _currentPage = 1;
  Future<List<ProductListModel>> getProductCategories({
    required int categoryId,
    bool refresh = false,
     bool resetFilters = false, 
  }) async {
    if (isLoading) return productData;

    if (_activeCategoryId != categoryId) {
      refresh = true;
      _activeCategoryId = categoryId;
    }

    if (refresh) {
      _currentPage = 1;
      hasMore = true;
      productData.clear();
    
    }if (resetFilters) {
      searchFilter.attrFilter?.clear(); 
    }

    if (!hasMore) return productData;

    isLoading = true;
    searchFilter
      ..pageSize = _pageSize
      ..pageNumber = _currentPage
      ..categoryId = categoryId;

    try {
      final result = await Api.post(
        action: "Catalog/Products",
        body: searchFilter.toJson(),
      );

      final raw = (result?.data['data'] as List?) ?? [];

      final fetched = raw.map((e) => ProductListModel.fromJson(e)).toList();
      productData.addAll(fetched);

      if (fetched.length < _pageSize) {
        hasMore = false;
      } else {
        _currentPage++;
      }

      return productData;
    } catch (e) {
      return productData;
    } finally {
      isLoading = false;
      notifyListeners();
    }
  }

  //----------------------------------------------------------------------------

  Future<List<ProductListModel>> getProducts({
    bool? resetAndRefresh = false,
    ProductSearchFilter? seachModel,
  }) async {
    isLoading = true;
    try {
      if (resetAndRefresh!) productData.clear();
      if (resetAndRefresh) seachModel?.attrFilter?.clear();
      var url = "Catalog/Products";
      searchFilter.search ??= "";
      searchFilter.pageSize = _pageSize;
      searchFilter.pageNumber = (-productData.length ~/ _pageSize) + 1;
      searchFilter.categoryId = seachModel?.categoryId;

      productData.clear();
      var result = await Api.post(action: url, body: searchFilter.toJson());

      if (result != null && result.data['data'] != null) {
        for (var element in result.data['data']) {
          var data = ProductListModel.fromJson(element);
          productData.add(data);
        }
      }

      notifyListeners();
      return productData;
    } catch (e) {
      return [];
    } finally {
      isLoading = false;
      notifyListeners();
    }
  }
  //----------------------------------------------------------------------------

  Future<List<ProductListModel>> getFeaturedProducts({
    bool? resetAndRefresh = false,
    ProductSearchFilter? seachModel,
  }) async {
    isLoading = true;
    try {
      if (resetAndRefresh!) featuredProduct.clear();

      var url = "Catalog/Products";
      seachModel?.search ??= "";
      seachModel?.pageSize = _pageSize;
      seachModel?.pageNumber = (-featuredProduct.length ~/ _pageSize) + 1;

      var result = await Api.post(action: url, body: seachModel?.toJson());

      if (result != null && result.data['data'] != null) {
        for (var element in result.data['data']) {
          var data = ProductListModel.fromJson(element);
          featuredProduct.add(data);
        }
      }

      notifyListeners();
      return productData;
    } catch (e) {
      return [];
    } finally {
      isLoading = false;
      notifyListeners();
    }
  }
  //----------------------------------------------------------------------------

  Future<List<ProductListModel>> getNewProducts({
    bool? resetAndRefresh = false,
    ProductSearchFilter? seachModel,
  }) async {
    isLoading = true;
    try {
      if (resetAndRefresh!) newProducts.clear();

      var url = "Catalog/Products";
      seachModel?.search ??= "";
      seachModel?.pageSize = _pageSize;
      seachModel?.pageNumber = (-newProducts.length ~/ _pageSize) + 1;

      var result = await Api.post(action: url, body: seachModel?.toJson());

      if (result != null && result.data['data'] != null) {
        for (var element in result.data['data']) {
          var data = ProductListModel.fromJson(element);
          newProducts.add(data);
        }
      }

      notifyListeners();
      return productData;
    } catch (e) {
      return [];
    } finally {
      isLoading = false;
      notifyListeners();
    }
  }

  //----------------------------------------------------------------------------
  Future<void> getProductById({
    required int itemId,
  }) async {
    try {
      productDetails = ProductModel();

      var url = 'Catalog/item/$itemId';

      var result = await Api.getOne(
        useBaseUrl2: false,
        action: url,
      );

      if (result != null && result.data != null) {
        var value = result.data['data'];

        if (value is Map<String, dynamic>) {
          var item = ProductModel.fromJson(value);
          productDetails = item;
          if (productDetails?.productAttribute == null) return;

          for (var attribute in productDetails!.productAttribute!) {
            for (var option in attribute.options ?? []) {
              if (option.image != null) {
                productDetails?.images?.add(Images(base64File: option.image));
              }
            }
          }

          productDetailsAttribute = item.productAttribute;
        } else if (value is List &&
            value.isNotEmpty &&
            value[0] is Map<String, dynamic>) {
          var item = ProductModel.fromJson(value[0]);
          productDetailsAttribute = item.productAttribute;
          relatedProduct = item.relatedProducts ?? [];
          productDetails = item;
        } else {
          print("Unexpected data format or empty list: $value");
        }
      } else {
        print("API response or data is null.");
      }

      notifyListeners();
    } catch (e) {
      // Handle errors
      print("Error fetching product details: $e");
    }
  }

  //----------------------------------------------------------------------------
  Future<void> getFavoritesItems() async {
    try {
      favoritesProductsIds.clear();
      var url = 'odata/v1/GetCustomerWishlistedProductIdList';
      var result = await Api.getOne(
        action: url,
      );
      if (result.data != null) {
        for (var element in result.data) {
          favoritesProductsIds.add(element);
        }
      }
      // ignore: empty_catches
    } catch (e) {}
  }

//----------------------------------------------------------------------------
  Future<void> getAttributesWithOptionsCategoryById({
    required int categoryId,
    bool resetAndRefresh = false,
  }) async {
    try {
      var url = 'Catalog/AttributesWithOptions/$categoryId';
      var result = await Api.getOne(action: url);

      attributes.clear();
      if (result != null && result.data != null) {
        if (result.data.containsKey('data') && result.data['data'] is List) {
          List<dynamic> items = result.data['data'];

          attributes.clear();

          for (var element in items) {
            attributes.add(AttributesDTO.fromJson(element));
          }

          notifyListeners();
        } else {
          print("Error: 'data' key is missing or not a list.");
        }
      } else {
        print("Error: API response is null or does not contain valid data.");
      }
    } catch (e) {
      print("Error: $e");
    }
  }

  //----------------------------------------------------------------------------
  Future<void> getProductReviews({
    required int itemId,
  }) async {
    try {
      reviews = [];
      var url = 'Review/GetReviewsByProductId?productId=$itemId';

      var result = await Api.getOne(
        useBaseUrl2: false,
        action: url,
      );

      if (result != null && result.data != null) {
        var value = result.data['data'];

        if (value is List && value.isNotEmpty) {
          reviews = value.map((e) => ReviewModel.fromJson(e)).toList();
        } else {
          print("Unexpected data format or empty list: $value");
        }
      } else {
        print("API response or data is null.");
      }

      notifyListeners();
    } catch (e) {
      print("Error fetching product details: $e");
    }
  }

  //----------------------------------------------------------------------------
  Future<void> getProductAttributeBySelectedValues(int itemId,
      List<int> selectedIds, int order, List<int?> availableIdsSameType) async {
    try {
      if (selectedIds.isEmpty) return;

      var url = 'Catalog/GetProductAttributeBySelectedOption?productId=$itemId';

      for (var element in selectedIds) {
        url = "$url&selectedIds=$element";
      }

      url = "$url&SelectedOrderId=$order";

      for (var element in availableIdsSameType) {
        url = "$url&availableIdsSameType=$element";
      }
      var result = await Api.getOne(
        action: url,
      );
      productDetailsAttribute?.clear();
      if (result.data != null) {
        for (var element in result.data["data"]) {
          try {
            var item = ProductAttribute.fromJson(element);
            productDetailsAttribute?.add(item);
          } catch (e) {
            print('Error parsing product data: $e');
            print('Error data: $element');
          }
        }
      }

      notifyListeners();
    } catch (e) {
      print('Error fetching product categories: $e');
    }
  }

  //---------------------------------------------------------------------------
  Future<ResponseResultModel> createProductReview(CreateReview model) async {
    try {
      var url = 'Review/CreateReview';
      var result = await Api.post(
        action: url,
        body: model.toJson(),
      );
      if (result != null) {
        if (result.isSuccess == true) {
          return result;
        } else {
          return result;
        }
      }
      notifyListeners();
      return ResponseResultModel(isSuccess: false);
    } catch (e) {
      return ResponseResultModel(isSuccess: false);
    }
  }
}
