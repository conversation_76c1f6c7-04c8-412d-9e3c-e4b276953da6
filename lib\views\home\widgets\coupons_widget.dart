import 'package:alderishop/controllers/app_controller.dart';
import 'package:alderishop/controllers/coupon_controller.dart';
import 'package:alderishop/views/home/<USER>/couponCard.dart';

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class CouponsWidget extends StatefulWidget {
  const CouponsWidget({super.key});

  @override
  State<CouponsWidget> createState() => _CouponsWidgetState();
}

class _CouponsWidgetState extends State<CouponsWidget> {
  @override
  Widget build(BuildContext context) {
    var data = Provider.of<CouponController>(context).couponsForAll;

    return Padding(
      padding: EdgeInsets.only(
          left: AppController.currentLangId == 2 ? 7 : 18,
          right: AppController.currentLangId != 2 ? 7 : 18),
      child: SizedBox(
        height: 160,
        child: ListView.builder(
          shrinkWrap: true,
          scrollDirection: Axis.horizontal,
          itemCount: data.length > 4 ? 4 : data.length,
          itemBuilder: (context, index) {
            return Padding(
              padding: const EdgeInsets.symmetric(horizontal: 10),
              child: CouponCard(
                coupons: data[index],
              ),
            );
          },
        ),
      ),
    );
  }
}
