import 'package:alderishop/data/api/api.dart';
import 'package:alderishop/data/model/home/<USER>';
import 'package:flutter/material.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';

import 'package:alderishop/constants/constants.dart';
import 'package:alderishop/components/common_cache_image.dart';


class ReusableBannerSlider extends StatefulWidget {
  final List<SliderItem> items; // Your slider items
  final bool autoPlay;
  final double height;
  final Function(SliderItem)? onTap;

  const ReusableBannerSlider({
    Key? key,
    required this.items,
    this.autoPlay = true,
    this.height = 400,
    this.onTap,
  }) : super(key: key);

  @override
  State<ReusableBannerSlider> createState() => _ReusableBannerSliderState();
}

class _ReusableBannerSliderState extends State<ReusableBannerSlider> {
  int _currentIndex = 0;

  @override
  Widget build(BuildContext context) {
    final items = widget.items;

    if (items.isEmpty) {
      return const Center(child: CircularProgressIndicator());
    }

    return Column(
      children: [
        CarouselSlider.builder(
          itemCount: items.length,
          itemBuilder: (context, index, _) {
            final item = items[index];
            return GestureDetector(
              onTap: () {
                if (widget.onTap != null) widget.onTap!(item);
              },
              child: Stack(
                children: [
                  CachedImage(
                    imageUrl: "$baseUrl1/${item.imageUrl ?? ""}",
                    fit: BoxFit.cover,
                    width: double.infinity,
                    height: widget.height,
                  ),
                  Positioned(
                    bottom: 20,
                    left: 16,
                    right: 16,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          item.title ?? '',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                        Text(
                          item.title ?? '',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            );
          },
          options: CarouselOptions(
            height: widget.height,
            autoPlay: widget.autoPlay,
            viewportFraction: 1,
            onPageChanged: (index, reason) {
              setState(() {
                _currentIndex = index;
              });
            },
          ),
        ),
        const SizedBox(height: 10),
        AnimatedSmoothIndicator(
          activeIndex: _currentIndex,
          count: items.length,
          effect:  ScrollingDotsEffect(
            activeDotColor: AppColors.PRIMARY_COLOR,
            dotColor: Colors.grey,
            dotHeight: 1,
            dotWidth: 15,
          ),
        ),
      ],
    );
  }
}
