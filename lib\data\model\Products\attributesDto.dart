class AttributesDTO {
  int? attributeId;
  String? attributeName;
  int? controlType;
  List<OptionsDTo>? options;

  AttributesDTO(
      {this.attributeId, this.attributeName, this.controlType, this.options});

  AttributesDTO.fromJson(Map<String, dynamic> json) {
    attributeId = json['attributeId'];
    attributeName = json['attributeName'];
    controlType = json['controlType'];
    if (json['options'] != null) {
      options = <OptionsDTo>[];
      json['options'].forEach((v) {
        options!.add(new OptionsDTo.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['attributeId'] = this.attributeId;
    data['attributeName'] = this.attributeName;
    data['controlType'] = this.controlType;
    if (this.options != null) {
      data['options'] = this.options!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class OptionsDTo {
  int? optionId;
  String? optionName;
  String? optionValue;

  OptionsDTo({this.optionId, this.optionName, this.optionValue});

  OptionsDTo.fromJson(Map<String, dynamic> json) {
    optionId = json['optionId'];
    optionName = json['optionName'];
    optionValue = json['optionValue'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['optionId'] = this.optionId;
    data['optionName'] = this.optionName;
    data['optionValue'] = this.optionValue;
    return data;
  }
}
