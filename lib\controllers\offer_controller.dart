import 'package:alderishop/data/api/api.dart';
import 'package:alderishop/data/model/Offers_model.dart';
import 'package:flutter/widgets.dart';

class OffersController with ChangeNotifier {
  List<OffersModel> offers = [];

  Future<void> getOffers() async {
    try {
      var result = await Api.getOne(
        useBaseUrl2: false,
        action: "Offers",
      );
      if (result.data != null && result.data['data'] != null) {
        for (var element in result.data['data']) {
          offers.add(OffersModel.fromJson(element));
        }
      }

      notifyListeners();
    } catch (e) {
      // print(e);
    }
  }
}
