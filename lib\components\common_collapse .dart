import 'package:flutter/material.dart';

class CommonCollapseWidget extends StatefulWidget {
  const CommonCollapseWidget({
    Key? key,
    required this.header,
    required this.body,
    this.isOpen = false,
    this.bottomBorderColor = Colors.grey,
    this.headerMargin,
    this.headerColor = Colors.white,
    this.color = Colors.white,
  }) : super(key: key);

  final Widget header;
  final Widget body;
  final bool isOpen;
  final Color bottomBorderColor;
  final EdgeInsetsGeometry? headerMargin;
  final Color headerColor;
  final Color color;

  @override
  State<CommonCollapseWidget> createState() => _CommonCollapseWidgetState();
}

class _CommonCollapseWidgetState extends State<CommonCollapseWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;
  bool _isOpen = false;

  @override
  void initState() {
    super.initState();
    _isOpen = widget.isOpen;
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    _animation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut,
      ),
    );
    if (widget.isOpen) {
      _openCollapse();
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _toggle() {
    if (_animationController.isAnimating) return;
    setState(() {
      _isOpen = !_isOpen;
    });
    if (_isOpen) {
      _animationController.forward();
    } else {
      _animationController.reverse();
    }
  }

  void _openCollapse() async {
    await Future.delayed(const Duration(milliseconds: 200));
    _animationController.forward();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        InkWell(
          onTap: _toggle,
          child: Container(
            margin: widget.headerMargin,
            padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 10),
            decoration: BoxDecoration(
              color: widget.headerColor,
              border: Border(
                bottom: BorderSide(
                  color: widget.bottomBorderColor,
                  width: 1,
                ),
              ),
              boxShadow: [],
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                widget.header,
                Icon(
                  _isOpen ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
                  color: Colors.grey,
                ),
              ],
            ),
          ),
        ),
        const SizedBox(height: 5),
        AnimatedBuilder(
          animation: _animation,
          builder: (context, child) {
            return ClipRect(
              child: Align(
                heightFactor: _animation.value,
                alignment: Alignment.topCenter,
                child: Container(
                  color: widget.color,
                  padding: const EdgeInsets.all(15),
                  child: widget.body,
                ),
              ),
            );
          },
        ),
      ],
    );
  }
}
