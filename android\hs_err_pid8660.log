#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 27262976 bytes for G1 virtual space
# Possible reasons:
#   The system is out of physical RAM or swap space
#   The process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (os_windows.cpp:3771), pid=8660, tid=33832
#
# JRE version: OpenJDK Runtime Environment (17.0.7) (build 17.0.7+0-b2043.56-10550314)
# Java VM: OpenJDK 64-Bit Server VM (17.0.7+0-b2043.56-10550314, mixed mode, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED -Xmx4G -Dfile.encoding=windows-1252 -Duser.country=US -Duser.language=en -Duser.variant org.gradle.launcher.daemon.bootstrap.GradleDaemon 7.5

Host: Intel(R) Core(TM) i7-10700T CPU @ 2.00GHz, 16 cores, 15G,  Windows 11 , 64 bit Build 26100 (10.0.26100.3775)
Time: Thu Apr 24 16:28:27 2025 Turkey Standard Time elapsed time: 20.154006 seconds (0d 0h 0m 20s)

---------------  T H R E A D  ---------------

Current thread (0x000001aa57c5ec50):  VMThread "VM Thread" [stack: 0x0000002aa8300000,0x0000002aa8400000] [id=33832]

Stack: [0x0000002aa8300000,0x0000002aa8400000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x683bfa]
V  [jvm.dll+0x8430a4]
V  [jvm.dll+0x8449be]
V  [jvm.dll+0x845023]
V  [jvm.dll+0x24ad2f]
V  [jvm.dll+0x680ac9]
V  [jvm.dll+0x67519a]
V  [jvm.dll+0x30b3cb]
V  [jvm.dll+0x312876]
V  [jvm.dll+0x36221e]
V  [jvm.dll+0x36244f]
V  [jvm.dll+0x2e14a8]
V  [jvm.dll+0x2df684]
V  [jvm.dll+0x2dec8c]
V  [jvm.dll+0x32382b]
V  [jvm.dll+0x8496eb]
V  [jvm.dll+0x84a424]
V  [jvm.dll+0x84a93d]
V  [jvm.dll+0x84ad14]
V  [jvm.dll+0x84ade0]
V  [jvm.dll+0x7f2aea]
V  [jvm.dll+0x682a35]
C  [ucrtbase.dll+0x37b0]
C  [KERNEL32.DLL+0x2e8d7]
C  [ntdll.dll+0xb14fc]

VM_Operation (0x0000002aaa0f0ef0): G1CollectForAllocation, mode: safepoint, requested by thread 0x000001aa5cceb3a0


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000001aa628013f0, length=237, elements={
0x000001aa3872ff80, 0x000001aa5c084020, 0x000001aa5c087c70, 0x000001aa5c0d1040,
0x000001aa5c0d1900, 0x000001aa5c0d21c0, 0x000001aa5c0d2a80, 0x000001aa5c0cb3e0,
0x000001aa5c0cc630, 0x000001aa5c0ccb40, 0x000001aa5c24e200, 0x000001aa5c250cf0,
0x000001aa5e61a6a0, 0x000001aa5d678ec0, 0x000001aa5e1b26c0, 0x000001aa5db3b2c0,
0x000001aa5c79b7e0, 0x000001aa5ccec210, 0x000001aa5cceb3a0, 0x000001aa5cceaed0,
0x000001aa5ccea060, 0x000001aa5ccea530, 0x000001aa5cceb870, 0x000001aa5ccebd40,
0x000001aa5cced550, 0x000001aa5ccec6e0, 0x000001aa5cced080, 0x000001aa5ccecbb0,
0x000001aa5cceda20, 0x000001aa5cceaa00, 0x000001aa60c916e0, 0x000001aa60c8fed0,
0x000001aa60c92a20, 0x000001aa60c90d40, 0x000001aa60c8f060, 0x000001aa60c91210,
0x000001aa60c92ef0, 0x000001aa60c933c0, 0x000001aa60c8fa00, 0x000001aa60c93890,
0x000001aa60c93d60, 0x000001aa60c94bd0, 0x000001aa60c94230, 0x000001aa60c94700,
0x000001aa60c95f10, 0x000001aa60c963e0, 0x000001aa60c950a0, 0x000001aa60c968b0,
0x000001aa60c95570, 0x000001aa60c95a40, 0x000001aa61b44010, 0x000001aa61b47500,
0x000001aa61b46b60, 0x000001aa61b479d0, 0x000001aa61b47030, 0x000001aa61b45350,
0x000001aa61b449b0, 0x000001aa61b44e80, 0x000001aa61b46690, 0x000001aa61b45820,
0x000001aa61b47ea0, 0x000001aa61b45cf0, 0x000001aa61b461c0, 0x000001aa61b444e0,
0x000001aa61b496b0, 0x000001aa61b491e0, 0x000001aa61b49b80, 0x000001aa61b4b860,
0x000001aa61b48370, 0x000001aa61b4a050, 0x000001aa61b48d10, 0x000001aa61b48840,
0x000001aa61b4a520, 0x000001aa61b4a9f0, 0x000001aa61b4b390, 0x000001aa5c7dd130,
0x000001aa5c7df2e0, 0x000001aa5c7dee10, 0x000001aa5c7dc2c0, 0x000001aa5c7dd600,
0x000001aa5c7dcc60, 0x000001aa5c7dbdf0, 0x000001aa5c7de940, 0x000001aa5c7ddad0,
0x000001aa5c7ddfa0, 0x000001aa5c7de470, 0x000001aa5c7db920, 0x000001aa5c7df7b0,
0x000001aa5c7e0150, 0x000001aa5c7dfc80, 0x000001aa5c7dc790, 0x000001aa5c7e1e30,
0x000001aa5c7e0fc0, 0x000001aa5c7e3170, 0x000001aa5c7e2300, 0x000001aa5c7e0620,
0x000001aa5c7e0af0, 0x000001aa5c7e27d0, 0x000001aa5c7e1490, 0x000001aa5c7e1960,
0x000001aa5c7e2ca0, 0x000001aa5f0a58a0, 0x000001aa5f0a7580, 0x000001aa5f0a53d0,
0x000001aa5f0a4560, 0x000001aa5f0a4a30, 0x000001aa5f0a6710, 0x000001aa5f0a4f00,
0x000001aa5f0a5d70, 0x000001aa5f0a6240, 0x000001aa5f0a3bc0, 0x000001aa5f0a6be0,
0x000001aa5f0a7a50, 0x000001aa5f0a70b0, 0x000001aa5f0a4090, 0x000001aa5f0aaa70,
0x000001aa5f0a7f20, 0x000001aa5f0a9260, 0x000001aa5f0a9730, 0x000001aa5f0aa5a0,
0x000001aa5f0a88c0, 0x000001aa5f0aa0d0, 0x000001aa5f0aaf40, 0x000001aa5f0ab410,
0x000001aa5f0a9c00, 0x000001aa5f0a83f0, 0x000001aa5f0a8d90, 0x000001aa60bd9260,
0x000001aa60bdaa70, 0x000001aa60bd7a50, 0x000001aa60bdb410, 0x000001aa60bdb8e0,
0x000001aa60bd7f20, 0x000001aa60bd83f0, 0x000001aa60bda0d0, 0x000001aa60bda5a0,
0x000001aa60bd9c00, 0x000001aa60bdbdb0, 0x000001aa60bd88c0, 0x000001aa60bdc280,
0x000001aa60bdc750, 0x000001aa60bdaf40, 0x000001aa60bd8d90, 0x000001aa60bdcc20,
0x000001aa60bdd0f0, 0x000001aa60bd9730, 0x000001aa60bdda90, 0x000001aa60bdf2a0,
0x000001aa60bdd5c0, 0x000001aa60bddf60, 0x000001aa60bde900, 0x000001aa60bde430,
0x000001aa5e3e9c90, 0x000001aa5e3e7ae0, 0x000001aa5e3e8480, 0x000001aa5e3e8e20,
0x000001aa5e3eab00, 0x000001aa5e3ea160, 0x000001aa5e3e7fb0, 0x000001aa5e3eb4a0,
0x000001aa5e3e92f0, 0x000001aa5e3e97c0, 0x000001aa5e3eb970, 0x000001aa5e3ea630,
0x000001aa5e3eafd0, 0x000001aa5e3ebe40, 0x000001aa5e3ec310, 0x000001aa5e3e8950,
0x000001aa5e3ec7e0, 0x000001aa5e3ed650, 0x000001aa5e3ef330, 0x000001aa5e3eee60,
0x000001aa5e3ee4c0, 0x000001aa5e3edff0, 0x000001aa5e3ed180, 0x000001aa5e3eccb0,
0x000001aa5e3edb20, 0x000001aa5e3ee990, 0x000001aa60bdedd0, 0x000001aa61192190,
0x000001aa61194ce0, 0x000001aa611934d0, 0x000001aa61193e70, 0x000001aa61194810,
0x000001aa61192660, 0x000001aa61191cc0, 0x000001aa611917f0, 0x000001aa61192b30,
0x000001aa61193000, 0x000001aa611939a0, 0x000001aa61194340, 0x000001aa61191320,
0x000001aa61217ce0, 0x000001aa612147f0, 0x000001aa61214cc0, 0x000001aa61215190,
0x000001aa61217340, 0x000001aa61216000, 0x000001aa612164d0, 0x000001aa61219020,
0x000001aa612169a0, 0x000001aa612181b0, 0x000001aa61216e70, 0x000001aa6076ca90,
0x000001aa6076f820, 0x000001aa6076cfa0, 0x000001aa6076d4b0, 0x000001aa61217810,
0x000001aa6076d9c0, 0x000001aa61218b50, 0x000001aa61215660, 0x000001aa612194f0,
0x000001aa61215b30, 0x000001aa6121b6a0, 0x000001aa6121bb70, 0x000001aa612199c0,
0x000001aa6121ad00, 0x000001aa6121c040, 0x000001aa61219e90, 0x000001aa6121a360,
0x000001aa6121a830, 0x000001aa6121b1d0, 0x000001aa61dc46e0, 0x000001aa61dc5080,
0x000001aa61dc3870, 0x000001aa61dc5550, 0x000001aa61dc33a0, 0x000001aa61dc1b90,
0x000001aa61dc5a20, 0x000001aa61dc2060, 0x000001aa61dc4210, 0x000001aa61dc2ed0,
0x000001aa61dc3d40, 0x000001aa61dc5ef0, 0x000001aa61dc4bb0, 0x000001aa61dc2530,
0x000001aa6076e3e0
}

Java Threads: ( => current thread )
  0x000001aa3872ff80 JavaThread "main" [_thread_blocked, id=11320, stack(0x0000002aa7d00000,0x0000002aa7e00000)]
  0x000001aa5c084020 JavaThread "Reference Handler" daemon [_thread_blocked, id=17940, stack(0x0000002aa8400000,0x0000002aa8500000)]
  0x000001aa5c087c70 JavaThread "Finalizer" daemon [_thread_blocked, id=8288, stack(0x0000002aa8500000,0x0000002aa8600000)]
  0x000001aa5c0d1040 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=5628, stack(0x0000002aa8600000,0x0000002aa8700000)]
  0x000001aa5c0d1900 JavaThread "Attach Listener" daemon [_thread_blocked, id=33552, stack(0x0000002aa8700000,0x0000002aa8800000)]
  0x000001aa5c0d21c0 JavaThread "Service Thread" daemon [_thread_blocked, id=16708, stack(0x0000002aa8800000,0x0000002aa8900000)]
  0x000001aa5c0d2a80 JavaThread "Monitor Deflation Thread" daemon [_thread_blocked, id=16604, stack(0x0000002aa8900000,0x0000002aa8a00000)]
  0x000001aa5c0cb3e0 JavaThread "C2 CompilerThread0" daemon [_thread_blocked, id=31148, stack(0x0000002aa8a00000,0x0000002aa8b00000)]
  0x000001aa5c0cc630 JavaThread "C1 CompilerThread0" daemon [_thread_blocked, id=39664, stack(0x0000002aa8b00000,0x0000002aa8c00000)]
  0x000001aa5c0ccb40 JavaThread "Sweeper thread" daemon [_thread_blocked, id=22700, stack(0x0000002aa8c00000,0x0000002aa8d00000)]
  0x000001aa5c24e200 JavaThread "Common-Cleaner" daemon [_thread_blocked, id=36932, stack(0x0000002aa8e00000,0x0000002aa8f00000)]
  0x000001aa5c250cf0 JavaThread "Notification Thread" daemon [_thread_blocked, id=32980, stack(0x0000002aa8f00000,0x0000002aa9000000)]
  0x000001aa5e61a6a0 JavaThread "Daemon health stats" [_thread_blocked, id=19004, stack(0x0000002aa9a00000,0x0000002aa9b00000)]
  0x000001aa5d678ec0 JavaThread "Incoming local TCP Connector on port 61835" [_thread_in_native, id=20396, stack(0x0000002aa9b00000,0x0000002aa9c00000)]
  0x000001aa5e1b26c0 JavaThread "Daemon periodic checks" [_thread_blocked, id=41924, stack(0x0000002aa9c00000,0x0000002aa9d00000)]
  0x000001aa5db3b2c0 JavaThread "Daemon" [_thread_blocked, id=36620, stack(0x0000002aa9d00000,0x0000002aa9e00000)]
  0x000001aa5c79b7e0 JavaThread "Handler for socket connection from /127.0.0.1:61835 to /127.0.0.1:61836" [_thread_in_native, id=17056, stack(0x0000002aa9e00000,0x0000002aa9f00000)]
  0x000001aa5ccec210 JavaThread "Cancel handler" [_thread_blocked, id=7016, stack(0x0000002aa9f00000,0x0000002aaa000000)]
  0x000001aa5cceb3a0 JavaThread "Daemon worker" [_thread_blocked, id=37856, stack(0x0000002aaa000000,0x0000002aaa100000)]
  0x000001aa5cceaed0 JavaThread "Asynchronous log dispatcher for DefaultDaemonConnection: socket connection from /127.0.0.1:61835 to /127.0.0.1:61836" [_thread_blocked, id=26244, stack(0x0000002aaa100000,0x0000002aaa200000)]
  0x000001aa5ccea060 JavaThread "Stdin handler" [_thread_blocked, id=13060, stack(0x0000002aaa200000,0x0000002aaa300000)]
  0x000001aa5ccea530 JavaThread "Daemon client event forwarder" [_thread_blocked, id=3204, stack(0x0000002aaa300000,0x0000002aaa400000)]
  0x000001aa5cceb870 JavaThread "Cache worker for journal cache (C:\Users\<USER>\.gradle\caches\journal-1)" [_thread_blocked, id=31884, stack(0x0000002aaa400000,0x0000002aaa500000)]
  0x000001aa5ccebd40 JavaThread "File lock request listener" [_thread_in_native, id=33308, stack(0x0000002aaa500000,0x0000002aaa600000)]
  0x000001aa5cced550 JavaThread "Cache worker for file hash cache (C:\Users\<USER>\.gradle\caches\7.5\fileHashes)" [_thread_blocked, id=37536, stack(0x0000002aaaf00000,0x0000002aab000000)]
  0x000001aa5ccec6e0 JavaThread "File watcher server" daemon [_thread_in_native, id=41604, stack(0x0000002aab000000,0x0000002aab100000)]
  0x000001aa5cced080 JavaThread "File watcher consumer" daemon [_thread_blocked, id=23712, stack(0x0000002aab100000,0x0000002aab200000)]
  0x000001aa5ccecbb0 JavaThread "Cache worker for checksums cache (D:\Ecommerce-App\android\.gradle\7.5\checksums)" [_thread_blocked, id=25000, stack(0x0000002aab200000,0x0000002aab300000)]
  0x000001aa5cceda20 JavaThread "Cache worker for cache directory md-rule (C:\Users\<USER>\.gradle\caches\7.5\md-rule)" [_thread_blocked, id=30560, stack(0x0000002aab300000,0x0000002aab400000)]
  0x000001aa5cceaa00 JavaThread "Cache worker for file content cache (C:\Users\<USER>\.gradle\caches\7.5\fileContent)" [_thread_blocked, id=36852, stack(0x0000002aab400000,0x0000002aab500000)]
  0x000001aa60c916e0 JavaThread "Cache worker for file hash cache (D:\Ecommerce-App\android\.gradle\7.5\fileHashes)" [_thread_blocked, id=29480, stack(0x0000002aab500000,0x0000002aab600000)]
  0x000001aa60c8fed0 JavaThread "Cache worker for cache directory md-supplier (C:\Users\<USER>\.gradle\caches\7.5\md-supplier)" [_thread_blocked, id=11056, stack(0x0000002aab600000,0x0000002aab700000)]
  0x000001aa60c92a20 JavaThread "Cache worker for execution history cache (C:\Users\<USER>\.gradle\caches\7.5\executionHistory)" [_thread_blocked, id=5940, stack(0x0000002aab700000,0x0000002aab800000)]
  0x000001aa60c90d40 JavaThread "jar transforms" [_thread_blocked, id=16712, stack(0x0000002aaba00000,0x0000002aabb00000)]
  0x000001aa60c8f060 JavaThread "Cache worker for kotlin-dsl (C:\Users\<USER>\.gradle\caches\7.5\kotlin-dsl)" [_thread_blocked, id=29420, stack(0x0000002aabb00000,0x0000002aabc00000)]
  0x000001aa60c91210 JavaThread "jar transforms Thread 2" [_thread_blocked, id=1396, stack(0x0000002aabc00000,0x0000002aabd00000)]
  0x000001aa60c92ef0 JavaThread "Cache worker for dependencies-accessors (D:\Ecommerce-App\android\.gradle\7.5\dependencies-accessors)" [_thread_blocked, id=29076, stack(0x0000002aabd00000,0x0000002aabe00000)]
  0x000001aa60c933c0 JavaThread "Cache worker for Build Output Cleanup Cache (C:\flutter\packages\flutter_tools\gradle\.gradle\buildOutputCleanup)" [_thread_blocked, id=41752, stack(0x0000002aabe00000,0x0000002aabf00000)]
  0x000001aa60c8fa00 JavaThread "jar transforms Thread 3" [_thread_blocked, id=25896, stack(0x0000002aabf00000,0x0000002aac000000)]
  0x000001aa60c93890 JavaThread "Memory manager" [_thread_blocked, id=28124, stack(0x0000002aac000000,0x0000002aac100000)]
  0x000001aa60c93d60 JavaThread "jar transforms Thread 4" [_thread_blocked, id=28320, stack(0x0000002aa9200000,0x0000002aa9300000)]
  0x000001aa60c94bd0 JavaThread "included builds" [_thread_blocked, id=6908, stack(0x0000002aab900000,0x0000002aaba00000)]
  0x000001aa60c94230 JavaThread "Execution worker" [_thread_blocked, id=8316, stack(0x0000002aac100000,0x0000002aac200000)]
  0x000001aa60c94700 JavaThread "Execution worker Thread 2" [_thread_blocked, id=29240, stack(0x0000002aac200000,0x0000002aac300000)]
  0x000001aa60c95f10 JavaThread "Execution worker Thread 3" [_thread_blocked, id=22516, stack(0x0000002aac300000,0x0000002aac400000)]
  0x000001aa60c963e0 JavaThread "Execution worker Thread 4" [_thread_blocked, id=41484, stack(0x0000002aac400000,0x0000002aac500000)]
  0x000001aa60c950a0 JavaThread "Execution worker Thread 5" [_thread_blocked, id=19936, stack(0x0000002aac500000,0x0000002aac600000)]
  0x000001aa60c968b0 JavaThread "Execution worker Thread 6" [_thread_blocked, id=15840, stack(0x0000002aac600000,0x0000002aac700000)]
  0x000001aa60c95570 JavaThread "Execution worker Thread 7" [_thread_blocked, id=24152, stack(0x0000002aac700000,0x0000002aac800000)]
  0x000001aa60c95a40 JavaThread "Execution worker Thread 8" [_thread_blocked, id=29652, stack(0x0000002aac800000,0x0000002aac900000)]
  0x000001aa61b44010 JavaThread "Execution worker Thread 9" [_thread_blocked, id=24360, stack(0x0000002aac900000,0x0000002aaca00000)]
  0x000001aa61b47500 JavaThread "Execution worker Thread 10" [_thread_blocked, id=41772, stack(0x0000002aaca00000,0x0000002aacb00000)]
  0x000001aa61b46b60 JavaThread "Execution worker Thread 11" [_thread_blocked, id=25212, stack(0x0000002aacb00000,0x0000002aacc00000)]
  0x000001aa61b479d0 JavaThread "Execution worker Thread 12" [_thread_blocked, id=15600, stack(0x0000002aacc00000,0x0000002aacd00000)]
  0x000001aa61b47030 JavaThread "Execution worker Thread 13" [_thread_blocked, id=41928, stack(0x0000002aacd00000,0x0000002aace00000)]
  0x000001aa61b45350 JavaThread "Execution worker Thread 14" [_thread_blocked, id=28408, stack(0x0000002aace00000,0x0000002aacf00000)]
  0x000001aa61b449b0 JavaThread "Execution worker Thread 15" [_thread_blocked, id=31284, stack(0x0000002aacf00000,0x0000002aad000000)]
  0x000001aa61b44e80 JavaThread "Cache worker for execution history cache (C:\flutter\packages\flutter_tools\gradle\.gradle\7.5\executionHistory)" [_thread_blocked, id=36412, stack(0x0000002aad000000,0x0000002aad100000)]
  0x000001aa61b46690 JavaThread "Unconstrained build operations" [_thread_blocked, id=33852, stack(0x0000002aad200000,0x0000002aad300000)]
  0x000001aa61b45820 JavaThread "Unconstrained build operations Thread 2" [_thread_blocked, id=28280, stack(0x0000002aad300000,0x0000002aad400000)]
  0x000001aa61b47ea0 JavaThread "Unconstrained build operations Thread 3" [_thread_blocked, id=16404, stack(0x0000002aad400000,0x0000002aad500000)]
  0x000001aa61b45cf0 JavaThread "Unconstrained build operations Thread 4" [_thread_blocked, id=22628, stack(0x0000002aad500000,0x0000002aad600000)]
  0x000001aa61b461c0 JavaThread "Unconstrained build operations Thread 5" [_thread_blocked, id=29220, stack(0x0000002aad600000,0x0000002aad700000)]
  0x000001aa61b444e0 JavaThread "Unconstrained build operations Thread 6" [_thread_blocked, id=4196, stack(0x0000002aad700000,0x0000002aad800000)]
  0x000001aa61b496b0 JavaThread "Unconstrained build operations Thread 7" [_thread_blocked, id=5584, stack(0x0000002aad800000,0x0000002aad900000)]
  0x000001aa61b491e0 JavaThread "Unconstrained build operations Thread 8" [_thread_blocked, id=40472, stack(0x0000002aad900000,0x0000002aada00000)]
  0x000001aa61b49b80 JavaThread "Unconstrained build operations Thread 9" [_thread_blocked, id=28896, stack(0x0000002aada00000,0x0000002aadb00000)]
  0x000001aa61b4b860 JavaThread "Unconstrained build operations Thread 10" [_thread_blocked, id=19072, stack(0x0000002aadb00000,0x0000002aadc00000)]
  0x000001aa61b48370 JavaThread "Unconstrained build operations Thread 11" [_thread_blocked, id=27696, stack(0x0000002aa9900000,0x0000002aa9a00000)]
  0x000001aa61b4a050 JavaThread "Unconstrained build operations Thread 12" [_thread_blocked, id=29360, stack(0x0000002aab800000,0x0000002aab900000)]
  0x000001aa61b48d10 JavaThread "Unconstrained build operations Thread 13" [_thread_blocked, id=19932, stack(0x0000002aad100000,0x0000002aad200000)]
  0x000001aa61b48840 JavaThread "Unconstrained build operations Thread 14" [_thread_blocked, id=28424, stack(0x0000002aadc00000,0x0000002aadd00000)]
  0x000001aa61b4a520 JavaThread "Unconstrained build operations Thread 15" [_thread_blocked, id=41340, stack(0x0000002aadd00000,0x0000002aade00000)]
  0x000001aa61b4a9f0 JavaThread "Unconstrained build operations Thread 16" [_thread_blocked, id=5800, stack(0x0000002aade00000,0x0000002aadf00000)]
  0x000001aa61b4b390 JavaThread "Unconstrained build operations Thread 17" [_thread_blocked, id=7676, stack(0x0000002aadf00000,0x0000002aae000000)]
  0x000001aa5c7dd130 JavaThread "Unconstrained build operations Thread 18" [_thread_blocked, id=24584, stack(0x0000002aae000000,0x0000002aae100000)]
  0x000001aa5c7df2e0 JavaThread "Unconstrained build operations Thread 19" [_thread_blocked, id=39048, stack(0x0000002aae100000,0x0000002aae200000)]
  0x000001aa5c7dee10 JavaThread "Unconstrained build operations Thread 20" [_thread_blocked, id=25468, stack(0x0000002aae200000,0x0000002aae300000)]
  0x000001aa5c7dc2c0 JavaThread "Unconstrained build operations Thread 21" [_thread_blocked, id=7100, stack(0x0000002aae300000,0x0000002aae400000)]
  0x000001aa5c7dd600 JavaThread "Unconstrained build operations Thread 22" [_thread_blocked, id=26564, stack(0x0000002aae400000,0x0000002aae500000)]
  0x000001aa5c7dcc60 JavaThread "Unconstrained build operations Thread 23" [_thread_blocked, id=9380, stack(0x0000002aae500000,0x0000002aae600000)]
  0x000001aa5c7dbdf0 JavaThread "Unconstrained build operations Thread 24" [_thread_blocked, id=30760, stack(0x0000002aae600000,0x0000002aae700000)]
  0x000001aa5c7de940 JavaThread "Unconstrained build operations Thread 25" [_thread_blocked, id=15580, stack(0x0000002aae700000,0x0000002aae800000)]
  0x000001aa5c7ddad0 JavaThread "Unconstrained build operations Thread 26" [_thread_blocked, id=33732, stack(0x0000002aae800000,0x0000002aae900000)]
  0x000001aa5c7ddfa0 JavaThread "Unconstrained build operations Thread 27" [_thread_blocked, id=35560, stack(0x0000002aae900000,0x0000002aaea00000)]
  0x000001aa5c7de470 JavaThread "Unconstrained build operations Thread 28" [_thread_blocked, id=26316, stack(0x0000002aaea00000,0x0000002aaeb00000)]
  0x000001aa5c7db920 JavaThread "Unconstrained build operations Thread 29" [_thread_blocked, id=16696, stack(0x0000002aaeb00000,0x0000002aaec00000)]
  0x000001aa5c7df7b0 JavaThread "Unconstrained build operations Thread 30" [_thread_blocked, id=38380, stack(0x0000002aaec00000,0x0000002aaed00000)]
  0x000001aa5c7e0150 JavaThread "Unconstrained build operations Thread 31" [_thread_blocked, id=33900, stack(0x0000002aaed00000,0x0000002aaee00000)]
  0x000001aa5c7dfc80 JavaThread "Unconstrained build operations Thread 32" [_thread_blocked, id=25872, stack(0x0000002aaee00000,0x0000002aaef00000)]
  0x000001aa5c7dc790 JavaThread "Unconstrained build operations Thread 33" [_thread_blocked, id=41800, stack(0x0000002aaef00000,0x0000002aaf000000)]
  0x000001aa5c7e1e30 JavaThread "Unconstrained build operations Thread 34" [_thread_blocked, id=18324, stack(0x0000002aaf000000,0x0000002aaf100000)]
  0x000001aa5c7e0fc0 JavaThread "Unconstrained build operations Thread 35" [_thread_blocked, id=36472, stack(0x0000002aaf100000,0x0000002aaf200000)]
  0x000001aa5c7e3170 JavaThread "Unconstrained build operations Thread 36" [_thread_blocked, id=27216, stack(0x0000002aaf200000,0x0000002aaf300000)]
  0x000001aa5c7e2300 JavaThread "Unconstrained build operations Thread 37" [_thread_blocked, id=27860, stack(0x0000002aaf300000,0x0000002aaf400000)]
  0x000001aa5c7e0620 JavaThread "Unconstrained build operations Thread 38" [_thread_blocked, id=1804, stack(0x0000002aaf400000,0x0000002aaf500000)]
  0x000001aa5c7e0af0 JavaThread "Unconstrained build operations Thread 39" [_thread_blocked, id=30904, stack(0x0000002aaf500000,0x0000002aaf600000)]
  0x000001aa5c7e27d0 JavaThread "Unconstrained build operations Thread 40" [_thread_blocked, id=27632, stack(0x0000002aaf600000,0x0000002aaf700000)]
  0x000001aa5c7e1490 JavaThread "Unconstrained build operations Thread 41" [_thread_blocked, id=34404, stack(0x0000002aaf700000,0x0000002aaf800000)]
  0x000001aa5c7e1960 JavaThread "Unconstrained build operations Thread 42" [_thread_blocked, id=10616, stack(0x0000002aaf800000,0x0000002aaf900000)]
  0x000001aa5c7e2ca0 JavaThread "Unconstrained build operations Thread 43" [_thread_blocked, id=2876, stack(0x0000002aaf900000,0x0000002aafa00000)]
  0x000001aa5f0a58a0 JavaThread "Unconstrained build operations Thread 44" [_thread_blocked, id=38176, stack(0x0000002aafa00000,0x0000002aafb00000)]
  0x000001aa5f0a7580 JavaThread "Unconstrained build operations Thread 45" [_thread_blocked, id=21124, stack(0x0000002aafb00000,0x0000002aafc00000)]
  0x000001aa5f0a53d0 JavaThread "Unconstrained build operations Thread 46" [_thread_blocked, id=21508, stack(0x0000002aafc00000,0x0000002aafd00000)]
  0x000001aa5f0a4560 JavaThread "Unconstrained build operations Thread 47" [_thread_blocked, id=17096, stack(0x0000002aafd00000,0x0000002aafe00000)]
  0x000001aa5f0a4a30 JavaThread "Unconstrained build operations Thread 48" [_thread_blocked, id=16116, stack(0x0000002aafe00000,0x0000002aaff00000)]
  0x000001aa5f0a6710 JavaThread "Unconstrained build operations Thread 49" [_thread_blocked, id=41860, stack(0x0000002aaff00000,0x0000002ab0000000)]
  0x000001aa5f0a4f00 JavaThread "Unconstrained build operations Thread 50" [_thread_blocked, id=32748, stack(0x0000002aa9300000,0x0000002aa9400000)]
  0x000001aa5f0a5d70 JavaThread "Unconstrained build operations Thread 51" [_thread_blocked, id=19236, stack(0x0000002ab0000000,0x0000002ab0100000)]
  0x000001aa5f0a6240 JavaThread "Unconstrained build operations Thread 52" [_thread_blocked, id=36724, stack(0x0000002ab0100000,0x0000002ab0200000)]
  0x000001aa5f0a3bc0 JavaThread "Unconstrained build operations Thread 53" [_thread_blocked, id=2296, stack(0x0000002ab0200000,0x0000002ab0300000)]
  0x000001aa5f0a6be0 JavaThread "Unconstrained build operations Thread 54" [_thread_blocked, id=5412, stack(0x0000002ab0300000,0x0000002ab0400000)]
  0x000001aa5f0a7a50 JavaThread "Unconstrained build operations Thread 55" [_thread_blocked, id=11872, stack(0x0000002ab0400000,0x0000002ab0500000)]
  0x000001aa5f0a70b0 JavaThread "Unconstrained build operations Thread 56" [_thread_blocked, id=41840, stack(0x0000002ab0500000,0x0000002ab0600000)]
  0x000001aa5f0a4090 JavaThread "Unconstrained build operations Thread 57" [_thread_blocked, id=28912, stack(0x0000002ab0600000,0x0000002ab0700000)]
  0x000001aa5f0aaa70 JavaThread "Unconstrained build operations Thread 58" [_thread_blocked, id=32412, stack(0x0000002ab0700000,0x0000002ab0800000)]
  0x000001aa5f0a7f20 JavaThread "Unconstrained build operations Thread 59" [_thread_blocked, id=23272, stack(0x0000002aa8d00000,0x0000002aa8e00000)]
  0x000001aa5f0a9260 JavaThread "Unconstrained build operations Thread 60" [_thread_blocked, id=30544, stack(0x0000002aa9100000,0x0000002aa9200000)]
  0x000001aa5f0a9730 JavaThread "Unconstrained build operations Thread 61" [_thread_blocked, id=29720, stack(0x0000002ab0800000,0x0000002ab0900000)]
  0x000001aa5f0aa5a0 JavaThread "Unconstrained build operations Thread 62" [_thread_blocked, id=41292, stack(0x0000002ab0900000,0x0000002ab0a00000)]
  0x000001aa5f0a88c0 JavaThread "Unconstrained build operations Thread 63" [_thread_blocked, id=32744, stack(0x0000002ab0a00000,0x0000002ab0b00000)]
  0x000001aa5f0aa0d0 JavaThread "Unconstrained build operations Thread 64" [_thread_blocked, id=23120, stack(0x0000002ab0b00000,0x0000002ab0c00000)]
  0x000001aa5f0aaf40 JavaThread "Unconstrained build operations Thread 65" [_thread_blocked, id=16784, stack(0x0000002ab0c00000,0x0000002ab0d00000)]
  0x000001aa5f0ab410 JavaThread "Unconstrained build operations Thread 66" [_thread_blocked, id=18096, stack(0x0000002ab0d00000,0x0000002ab0e00000)]
  0x000001aa5f0a9c00 JavaThread "Unconstrained build operations Thread 67" [_thread_blocked, id=25240, stack(0x0000002ab0e00000,0x0000002ab0f00000)]
  0x000001aa5f0a83f0 JavaThread "Unconstrained build operations Thread 68" [_thread_blocked, id=16676, stack(0x0000002ab0f00000,0x0000002ab1000000)]
  0x000001aa5f0a8d90 JavaThread "Unconstrained build operations Thread 69" [_thread_blocked, id=26168, stack(0x0000002ab1000000,0x0000002ab1100000)]
  0x000001aa60bd9260 JavaThread "Unconstrained build operations Thread 70" [_thread_blocked, id=36364, stack(0x0000002ab1100000,0x0000002ab1200000)]
  0x000001aa60bdaa70 JavaThread "Unconstrained build operations Thread 71" [_thread_blocked, id=22084, stack(0x0000002ab1200000,0x0000002ab1300000)]
  0x000001aa60bd7a50 JavaThread "Unconstrained build operations Thread 72" [_thread_blocked, id=40352, stack(0x0000002ab1300000,0x0000002ab1400000)]
  0x000001aa60bdb410 JavaThread "Unconstrained build operations Thread 73" [_thread_blocked, id=2484, stack(0x0000002ab1400000,0x0000002ab1500000)]
  0x000001aa60bdb8e0 JavaThread "Unconstrained build operations Thread 74" [_thread_blocked, id=35468, stack(0x0000002ab1500000,0x0000002ab1600000)]
  0x000001aa60bd7f20 JavaThread "Unconstrained build operations Thread 75" [_thread_blocked, id=39332, stack(0x0000002ab1600000,0x0000002ab1700000)]
  0x000001aa60bd83f0 JavaThread "Unconstrained build operations Thread 76" [_thread_blocked, id=23564, stack(0x0000002ab1700000,0x0000002ab1800000)]
  0x000001aa60bda0d0 JavaThread "Unconstrained build operations Thread 77" [_thread_blocked, id=31764, stack(0x0000002ab1800000,0x0000002ab1900000)]
  0x000001aa60bda5a0 JavaThread "Unconstrained build operations Thread 78" [_thread_blocked, id=30020, stack(0x0000002ab1a00000,0x0000002ab1b00000)]
  0x000001aa60bd9c00 JavaThread "Unconstrained build operations Thread 79" [_thread_blocked, id=27968, stack(0x0000002ab1b00000,0x0000002ab1c00000)]
  0x000001aa60bdbdb0 JavaThread "Unconstrained build operations Thread 80" [_thread_blocked, id=17428, stack(0x0000002ab1c00000,0x0000002ab1d00000)]
  0x000001aa60bd88c0 JavaThread "Unconstrained build operations Thread 81" [_thread_blocked, id=25700, stack(0x0000002ab1d00000,0x0000002ab1e00000)]
  0x000001aa60bdc280 JavaThread "Unconstrained build operations Thread 82" [_thread_blocked, id=34272, stack(0x0000002ab1e00000,0x0000002ab1f00000)]
  0x000001aa60bdc750 JavaThread "Unconstrained build operations Thread 83" [_thread_blocked, id=9248, stack(0x0000002ab1f00000,0x0000002ab2000000)]
  0x000001aa60bdaf40 JavaThread "Unconstrained build operations Thread 84" [_thread_blocked, id=39280, stack(0x0000002ab2000000,0x0000002ab2100000)]
  0x000001aa60bd8d90 JavaThread "Unconstrained build operations Thread 85" [_thread_blocked, id=8432, stack(0x0000002ab2100000,0x0000002ab2200000)]
  0x000001aa60bdcc20 JavaThread "Unconstrained build operations Thread 86" [_thread_blocked, id=34680, stack(0x0000002ab2200000,0x0000002ab2300000)]
  0x000001aa60bdd0f0 JavaThread "Unconstrained build operations Thread 87" [_thread_blocked, id=31620, stack(0x0000002ab2300000,0x0000002ab2400000)]
  0x000001aa60bd9730 JavaThread "Unconstrained build operations Thread 88" [_thread_blocked, id=32816, stack(0x0000002ab2400000,0x0000002ab2500000)]
  0x000001aa60bdda90 JavaThread "Unconstrained build operations Thread 89" [_thread_blocked, id=15928, stack(0x0000002ab2500000,0x0000002ab2600000)]
  0x000001aa60bdf2a0 JavaThread "Unconstrained build operations Thread 90" [_thread_blocked, id=33068, stack(0x0000002ab2600000,0x0000002ab2700000)]
  0x000001aa60bdd5c0 JavaThread "Unconstrained build operations Thread 91" [_thread_blocked, id=24184, stack(0x0000002ab2700000,0x0000002ab2800000)]
  0x000001aa60bddf60 JavaThread "Unconstrained build operations Thread 92" [_thread_blocked, id=16592, stack(0x0000002ab2800000,0x0000002ab2900000)]
  0x000001aa60bde900 JavaThread "Unconstrained build operations Thread 93" [_thread_blocked, id=23904, stack(0x0000002ab2900000,0x0000002ab2a00000)]
  0x000001aa60bde430 JavaThread "Unconstrained build operations Thread 94" [_thread_blocked, id=25396, stack(0x0000002ab2a00000,0x0000002ab2b00000)]
  0x000001aa5e3e9c90 JavaThread "Unconstrained build operations Thread 95" [_thread_blocked, id=20980, stack(0x0000002ab2b00000,0x0000002ab2c00000)]
  0x000001aa5e3e7ae0 JavaThread "Unconstrained build operations Thread 96" [_thread_blocked, id=20276, stack(0x0000002ab2c00000,0x0000002ab2d00000)]
  0x000001aa5e3e8480 JavaThread "Unconstrained build operations Thread 97" [_thread_blocked, id=29296, stack(0x0000002ab2d00000,0x0000002ab2e00000)]
  0x000001aa5e3e8e20 JavaThread "Unconstrained build operations Thread 98" [_thread_blocked, id=32300, stack(0x0000002ab2e00000,0x0000002ab2f00000)]
  0x000001aa5e3eab00 JavaThread "Unconstrained build operations Thread 99" [_thread_blocked, id=22536, stack(0x0000002ab2f00000,0x0000002ab3000000)]
  0x000001aa5e3ea160 JavaThread "Unconstrained build operations Thread 100" [_thread_blocked, id=23676, stack(0x0000002ab3000000,0x0000002ab3100000)]
  0x000001aa5e3e7fb0 JavaThread "Unconstrained build operations Thread 101" [_thread_blocked, id=36888, stack(0x0000002ab3100000,0x0000002ab3200000)]
  0x000001aa5e3eb4a0 JavaThread "Unconstrained build operations Thread 102" [_thread_blocked, id=41184, stack(0x0000002ab3200000,0x0000002ab3300000)]
  0x000001aa5e3e92f0 JavaThread "Unconstrained build operations Thread 103" [_thread_blocked, id=15752, stack(0x0000002ab3300000,0x0000002ab3400000)]
  0x000001aa5e3e97c0 JavaThread "Unconstrained build operations Thread 104" [_thread_blocked, id=28716, stack(0x0000002ab3400000,0x0000002ab3500000)]
  0x000001aa5e3eb970 JavaThread "Unconstrained build operations Thread 105" [_thread_blocked, id=33540, stack(0x0000002ab3500000,0x0000002ab3600000)]
  0x000001aa5e3ea630 JavaThread "Unconstrained build operations Thread 106" [_thread_blocked, id=12012, stack(0x0000002ab3600000,0x0000002ab3700000)]
  0x000001aa5e3eafd0 JavaThread "Unconstrained build operations Thread 107" [_thread_blocked, id=30108, stack(0x0000002ab3700000,0x0000002ab3800000)]
  0x000001aa5e3ebe40 JavaThread "Unconstrained build operations Thread 108" [_thread_blocked, id=33244, stack(0x0000002ab3800000,0x0000002ab3900000)]
  0x000001aa5e3ec310 JavaThread "Unconstrained build operations Thread 109" [_thread_blocked, id=30404, stack(0x0000002ab3900000,0x0000002ab3a00000)]
  0x000001aa5e3e8950 JavaThread "Unconstrained build operations Thread 110" [_thread_blocked, id=4740, stack(0x0000002ab3a00000,0x0000002ab3b00000)]
  0x000001aa5e3ec7e0 JavaThread "Unconstrained build operations Thread 111" [_thread_blocked, id=8716, stack(0x0000002ab3b00000,0x0000002ab3c00000)]
  0x000001aa5e3ed650 JavaThread "Unconstrained build operations Thread 112" [_thread_blocked, id=12592, stack(0x0000002ab3c00000,0x0000002ab3d00000)]
  0x000001aa5e3ef330 JavaThread "Unconstrained build operations Thread 113" [_thread_blocked, id=28328, stack(0x0000002ab3d00000,0x0000002ab3e00000)]
  0x000001aa5e3eee60 JavaThread "Unconstrained build operations Thread 114" [_thread_blocked, id=27244, stack(0x0000002ab3e00000,0x0000002ab3f00000)]
  0x000001aa5e3ee4c0 JavaThread "Unconstrained build operations Thread 115" [_thread_blocked, id=28472, stack(0x0000002ab3f00000,0x0000002ab4000000)]
  0x000001aa5e3edff0 JavaThread "Unconstrained build operations Thread 116" [_thread_blocked, id=5144, stack(0x0000002ab4000000,0x0000002ab4100000)]
  0x000001aa5e3ed180 JavaThread "Unconstrained build operations Thread 117" [_thread_blocked, id=21280, stack(0x0000002ab4100000,0x0000002ab4200000)]
  0x000001aa5e3eccb0 JavaThread "Unconstrained build operations Thread 118" [_thread_blocked, id=39060, stack(0x0000002ab4200000,0x0000002ab4300000)]
  0x000001aa5e3edb20 JavaThread "Unconstrained build operations Thread 119" [_thread_blocked, id=25604, stack(0x0000002ab4300000,0x0000002ab4400000)]
  0x000001aa5e3ee990 JavaThread "Unconstrained build operations Thread 120" [_thread_blocked, id=24804, stack(0x0000002ab4400000,0x0000002ab4500000)]
  0x000001aa60bdedd0 JavaThread "Unconstrained build operations Thread 121" [_thread_blocked, id=15864, stack(0x0000002ab4500000,0x0000002ab4600000)]
  0x000001aa61192190 JavaThread "Unconstrained build operations Thread 122" [_thread_blocked, id=41000, stack(0x0000002ab4600000,0x0000002ab4700000)]
  0x000001aa61194ce0 JavaThread "Unconstrained build operations Thread 123" [_thread_blocked, id=6788, stack(0x0000002ab4700000,0x0000002ab4800000)]
  0x000001aa611934d0 JavaThread "Unconstrained build operations Thread 124" [_thread_blocked, id=10888, stack(0x0000002ab4800000,0x0000002ab4900000)]
  0x000001aa61193e70 JavaThread "Unconstrained build operations Thread 125" [_thread_blocked, id=35912, stack(0x0000002ab4900000,0x0000002ab4a00000)]
  0x000001aa61194810 JavaThread "Unconstrained build operations Thread 126" [_thread_blocked, id=11560, stack(0x0000002ab4a00000,0x0000002ab4b00000)]
  0x000001aa61192660 JavaThread "Unconstrained build operations Thread 127" [_thread_blocked, id=36204, stack(0x0000002ab4b00000,0x0000002ab4c00000)]
  0x000001aa61191cc0 JavaThread "Unconstrained build operations Thread 128" [_thread_blocked, id=6028, stack(0x0000002ab4c00000,0x0000002ab4d00000)]
  0x000001aa611917f0 JavaThread "Unconstrained build operations Thread 129" [_thread_blocked, id=31652, stack(0x0000002ab4d00000,0x0000002ab4e00000)]
  0x000001aa61192b30 JavaThread "Unconstrained build operations Thread 130" [_thread_blocked, id=14848, stack(0x0000002ab4e00000,0x0000002ab4f00000)]
  0x000001aa61193000 JavaThread "Unconstrained build operations Thread 131" [_thread_blocked, id=32548, stack(0x0000002ab4f00000,0x0000002ab5000000)]
  0x000001aa611939a0 JavaThread "Unconstrained build operations Thread 132" [_thread_blocked, id=20764, stack(0x0000002ab5000000,0x0000002ab5100000)]
  0x000001aa61194340 JavaThread "Unconstrained build operations Thread 133" [_thread_blocked, id=12808, stack(0x0000002ab5100000,0x0000002ab5200000)]
  0x000001aa61191320 JavaThread "jar transforms Thread 5" [_thread_blocked, id=5960, stack(0x0000002ab5200000,0x0000002ab5300000)]
  0x000001aa61217ce0 JavaThread "jar transforms Thread 6" [_thread_blocked, id=37764, stack(0x0000002ab5300000,0x0000002ab5400000)]
  0x000001aa612147f0 JavaThread "jar transforms Thread 7" [_thread_blocked, id=38600, stack(0x0000002ab5400000,0x0000002ab5500000)]
  0x000001aa61214cc0 JavaThread "jar transforms Thread 8" [_thread_blocked, id=32544, stack(0x0000002ab5500000,0x0000002ab5600000)]
  0x000001aa61215190 JavaThread "jar transforms Thread 9" [_thread_blocked, id=40484, stack(0x0000002ab5600000,0x0000002ab5700000)]
  0x000001aa61217340 JavaThread "jar transforms Thread 10" [_thread_blocked, id=11164, stack(0x0000002ab5700000,0x0000002ab5800000)]
  0x000001aa61216000 JavaThread "jar transforms Thread 11" [_thread_blocked, id=34984, stack(0x0000002ab5800000,0x0000002ab5900000)]
  0x000001aa612164d0 JavaThread "jar transforms Thread 12" [_thread_blocked, id=36368, stack(0x0000002ab5900000,0x0000002ab5a00000)]
  0x000001aa61219020 JavaThread "jar transforms Thread 13" [_thread_blocked, id=34044, stack(0x0000002ab5a00000,0x0000002ab5b00000)]
  0x000001aa612169a0 JavaThread "jar transforms Thread 14" [_thread_blocked, id=40380, stack(0x0000002ab5b00000,0x0000002ab5c00000)]
  0x000001aa612181b0 JavaThread "jar transforms Thread 15" [_thread_blocked, id=15428, stack(0x0000002ab5c00000,0x0000002ab5d00000)]
  0x000001aa61216e70 JavaThread "jar transforms Thread 16" [_thread_blocked, id=31112, stack(0x0000002ab5d00000,0x0000002ab5e00000)]
  0x000001aa6076ca90 JavaThread "C2 CompilerThread1" daemon [_thread_blocked, id=25272, stack(0x0000002ab1900000,0x0000002ab1a00000)]
  0x000001aa6076f820 JavaThread "C1 CompilerThread1" daemon [_thread_blocked, id=12512, stack(0x0000002ab5e00000,0x0000002ab5f00000)]
  0x000001aa6076cfa0 JavaThread "C1 CompilerThread2" daemon [_thread_blocked, id=22116, stack(0x0000002ab5f00000,0x0000002ab6000000)]
  0x000001aa6076d4b0 JavaThread "C1 CompilerThread3" daemon [_thread_blocked, id=36772, stack(0x0000002ab6000000,0x0000002ab6100000)]
  0x000001aa61217810 JavaThread "Cache worker for Build Output Cleanup Cache (D:\Ecommerce-App\android\.gradle\buildOutputCleanup)" [_thread_blocked, id=3436, stack(0x0000002ab6100000,0x0000002ab6200000)]
  0x000001aa6076d9c0 JavaThread "C2 CompilerThread2" daemon [_thread_blocked, id=40132, stack(0x0000002ab6200000,0x0000002ab6300000)]
  0x000001aa61218b50 JavaThread "Unconstrained build operations Thread 134" [_thread_blocked, id=5372, stack(0x0000002ab6300000,0x0000002ab6400000)]
  0x000001aa61215660 JavaThread "Unconstrained build operations Thread 135" [_thread_blocked, id=40228, stack(0x0000002ab6400000,0x0000002ab6500000)]
  0x000001aa612194f0 JavaThread "Unconstrained build operations Thread 136" [_thread_blocked, id=40328, stack(0x0000002ab6500000,0x0000002ab6600000)]
  0x000001aa61215b30 JavaThread "Unconstrained build operations Thread 137" [_thread_blocked, id=35644, stack(0x0000002ab6600000,0x0000002ab6700000)]
  0x000001aa6121b6a0 JavaThread "Unconstrained build operations Thread 138" [_thread_blocked, id=38968, stack(0x0000002ab6700000,0x0000002ab6800000)]
  0x000001aa6121bb70 JavaThread "Unconstrained build operations Thread 139" [_thread_blocked, id=28448, stack(0x0000002ab6800000,0x0000002ab6900000)]
  0x000001aa612199c0 JavaThread "Unconstrained build operations Thread 140" [_thread_blocked, id=37476, stack(0x0000002ab6900000,0x0000002ab6a00000)]
  0x000001aa6121ad00 JavaThread "Unconstrained build operations Thread 141" [_thread_blocked, id=18780, stack(0x0000002ab6a00000,0x0000002ab6b00000)]
  0x000001aa6121c040 JavaThread "Unconstrained build operations Thread 142" [_thread_blocked, id=18112, stack(0x0000002ab6b00000,0x0000002ab6c00000)]
  0x000001aa61219e90 JavaThread "Unconstrained build operations Thread 143" [_thread_blocked, id=29284, stack(0x0000002ab6c00000,0x0000002ab6d00000)]
  0x000001aa6121a360 JavaThread "Unconstrained build operations Thread 144" [_thread_blocked, id=26032, stack(0x0000002ab6d00000,0x0000002ab6e00000)]
  0x000001aa6121a830 JavaThread "Unconstrained build operations Thread 145" [_thread_blocked, id=17612, stack(0x0000002ab6e00000,0x0000002ab6f00000)]
  0x000001aa6121b1d0 JavaThread "Unconstrained build operations Thread 146" [_thread_blocked, id=22356, stack(0x0000002ab6f00000,0x0000002ab7000000)]
  0x000001aa61dc46e0 JavaThread "Unconstrained build operations Thread 147" [_thread_blocked, id=29668, stack(0x0000002ab7000000,0x0000002ab7100000)]
  0x000001aa61dc5080 JavaThread "Unconstrained build operations Thread 148" [_thread_blocked, id=20228, stack(0x0000002ab7100000,0x0000002ab7200000)]
  0x000001aa61dc3870 JavaThread "Unconstrained build operations Thread 149" [_thread_blocked, id=37604, stack(0x0000002ab7200000,0x0000002ab7300000)]
  0x000001aa61dc5550 JavaThread "Unconstrained build operations Thread 150" [_thread_blocked, id=21340, stack(0x0000002ab7300000,0x0000002ab7400000)]
  0x000001aa61dc33a0 JavaThread "Unconstrained build operations Thread 151" [_thread_blocked, id=4788, stack(0x0000002ab7400000,0x0000002ab7500000)]
  0x000001aa61dc1b90 JavaThread "Unconstrained build operations Thread 152" [_thread_blocked, id=27824, stack(0x0000002ab7500000,0x0000002ab7600000)]
  0x000001aa61dc5a20 JavaThread "Unconstrained build operations Thread 153" [_thread_blocked, id=5540, stack(0x0000002ab7600000,0x0000002ab7700000)]
  0x000001aa61dc2060 JavaThread "Unconstrained build operations Thread 154" [_thread_blocked, id=33636, stack(0x0000002ab7700000,0x0000002ab7800000)]
  0x000001aa61dc4210 JavaThread "Unconstrained build operations Thread 155" [_thread_blocked, id=34584, stack(0x0000002ab7800000,0x0000002ab7900000)]
  0x000001aa61dc2ed0 JavaThread "Unconstrained build operations Thread 156" [_thread_blocked, id=1776, stack(0x0000002ab7900000,0x0000002ab7a00000)]
  0x000001aa61dc3d40 JavaThread "Unconstrained build operations Thread 157" [_thread_blocked, id=8692, stack(0x0000002ab7c00000,0x0000002ab7d00000)]
  0x000001aa61dc5ef0 JavaThread "Unconstrained build operations Thread 158" [_thread_blocked, id=30328, stack(0x0000002ab7d00000,0x0000002ab7e00000)]
  0x000001aa61dc4bb0 JavaThread "Unconstrained build operations Thread 159" [_thread_blocked, id=12248, stack(0x0000002ab7e00000,0x0000002ab7f00000)]
  0x000001aa61dc2530 JavaThread "Unconstrained build operations Thread 160" [_thread_blocked, id=41828, stack(0x0000002ab7f00000,0x0000002ab8000000)]
  0x000001aa6076e3e0 JavaThread "C2 CompilerThread3" daemon [_thread_blocked, id=4020, stack(0x0000002ab8000000,0x0000002ab8100000)]

Other Threads:
=>0x000001aa57c5ec50 VMThread "VM Thread" [stack: 0x0000002aa8300000,0x0000002aa8400000] [id=33832]
  0x000001aa5c254f70 WatcherThread [stack: 0x0000002aa9000000,0x0000002aa9100000] [id=33024]
  0x000001aa387a1530 GCTaskThread "GC Thread#0" [stack: 0x0000002aa7e00000,0x0000002aa7f00000] [id=19764]
  0x000001aa5c450590 GCTaskThread "GC Thread#1" [stack: 0x0000002aa9400000,0x0000002aa9500000] [id=25172]
  0x000001aa5c450840 GCTaskThread "GC Thread#2" [stack: 0x0000002aa9500000,0x0000002aa9600000] [id=35880]
  0x000001aa5c4502e0 GCTaskThread "GC Thread#3" [stack: 0x0000002aa9600000,0x0000002aa9700000] [id=21188]
  0x000001aa5c450af0 GCTaskThread "GC Thread#4" [stack: 0x0000002aa9700000,0x0000002aa9800000] [id=6088]
  0x000001aa5c451050 GCTaskThread "GC Thread#5" [stack: 0x0000002aa9800000,0x0000002aa9900000] [id=27356]
  0x000001aa5da6d370 GCTaskThread "GC Thread#6" [stack: 0x0000002aaa600000,0x0000002aaa700000] [id=33912]
  0x000001aa5da70120 GCTaskThread "GC Thread#7" [stack: 0x0000002aaa700000,0x0000002aaa800000] [id=17960]
  0x000001aa5da6db80 GCTaskThread "GC Thread#8" [stack: 0x0000002aaa800000,0x0000002aaa900000] [id=29004]
  0x000001aa5da6f910 GCTaskThread "GC Thread#9" [stack: 0x0000002aaa900000,0x0000002aaaa00000] [id=25448]
  0x000001aa5da6eba0 GCTaskThread "GC Thread#10" [stack: 0x0000002aaaa00000,0x0000002aaab00000] [id=5312]
  0x000001aa5da6f660 GCTaskThread "GC Thread#11" [stack: 0x0000002aaab00000,0x0000002aaac00000] [id=35064]
  0x000001aa5da6fe70 GCTaskThread "GC Thread#12" [stack: 0x0000002aaac00000,0x0000002aaad00000] [id=31188]
  0x000001aa387b3050 ConcurrentGCThread "G1 Main Marker" [stack: 0x0000002aa7f00000,0x0000002aa8000000] [id=28524]
  0x000001aa387b4110 ConcurrentGCThread "G1 Conc#0" [stack: 0x0000002aa8000000,0x0000002aa8100000] [id=30176]
  0x000001aa5da6fbc0 ConcurrentGCThread "G1 Conc#1" [stack: 0x0000002aaad00000,0x0000002aaae00000] [id=34336]
  0x000001aa5da6e0e0 ConcurrentGCThread "G1 Conc#2" [stack: 0x0000002aaae00000,0x0000002aaaf00000] [id=29632]
  0x000001aa57b12c80 ConcurrentGCThread "G1 Refine#0" [stack: 0x0000002aa8100000,0x0000002aa8200000] [id=40844]
  0x000001aa57b135b0 ConcurrentGCThread "G1 Service" [stack: 0x0000002aa8200000,0x0000002aa8300000] [id=27912]

Threads with active compile tasks:

VM state: at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x000001aa36389cd0] Threads_lock - owner thread: 0x000001aa57c5ec50
[0x000001aa3872e560] Heap_lock - owner thread: 0x000001aa5cceb3a0

Heap address: 0x0000000700000000, size: 4096 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) not mapped
Compressed class space mapped at: 0x0000000800000000-0x0000000840000000, reserved size: 1073741824
Narrow klass base: 0x0000000800000000, Narrow klass shift: 0, Narrow klass range: 0x40000000

GC Precious Log:
 CPUs: 16 total, 16 available
 Memory: 16195M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (Zero based)
 Heap Region Size: 2M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 254M
 Heap Max Capacity: 4G
 Pre-touch: Disabled
 Parallel Workers: 13
 Concurrent Workers: 3
 Concurrent Refinement Workers: 13
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 126976K, used 73180K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 2 young (4096K), 2 survivors (4096K)
 Metaspace       used 76414K, committed 76992K, reserved 1179648K
  class space    used 10312K, committed 10560K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, OA=open archive, CA=closed archive, TAMS=top-at-mark-start (previous, next)
|   0|0x0000000700000000, 0x0000000700200000, 0x0000000700200000|100%| O|  |TAMS 0x0000000700200000, 0x0000000700200000| Untracked 
|   1|0x0000000700200000, 0x0000000700400000, 0x0000000700400000|100%| O|  |TAMS 0x0000000700400000, 0x0000000700400000| Untracked 
|   2|0x0000000700400000, 0x0000000700600000, 0x0000000700600000|100%|HS|  |TAMS 0x0000000700600000, 0x0000000700600000| Complete 
|   3|0x0000000700600000, 0x0000000700800000, 0x0000000700800000|100%|HC|  |TAMS 0x0000000700800000, 0x0000000700800000| Complete 
|   4|0x0000000700800000, 0x0000000700a00000, 0x0000000700a00000|100%| O|  |TAMS 0x0000000700a00000, 0x0000000700a00000| Untracked 
|   5|0x0000000700a00000, 0x0000000700c00000, 0x0000000700c00000|100%| O|  |TAMS 0x0000000700c00000, 0x0000000700c00000| Untracked 
|   6|0x0000000700c00000, 0x0000000700e00000, 0x0000000700e00000|100%| O|  |TAMS 0x0000000700e00000, 0x0000000700e00000| Untracked 
|   7|0x0000000700e00000, 0x0000000701000000, 0x0000000701000000|100%| O|  |TAMS 0x0000000701000000, 0x0000000701000000| Untracked 
|   8|0x0000000701000000, 0x0000000701200000, 0x0000000701200000|100%| O|  |TAMS 0x0000000701200000, 0x0000000701200000| Untracked 
|   9|0x0000000701200000, 0x0000000701400000, 0x0000000701400000|100%| O|  |TAMS 0x0000000701400000, 0x0000000701400000| Untracked 
|  10|0x0000000701400000, 0x0000000701600000, 0x0000000701600000|100%| O|  |TAMS 0x0000000701600000, 0x0000000701600000| Untracked 
|  11|0x0000000701600000, 0x0000000701800000, 0x0000000701800000|100%| O|  |TAMS 0x0000000701800000, 0x0000000701800000| Untracked 
|  12|0x0000000701800000, 0x0000000701a00000, 0x0000000701a00000|100%| O|  |TAMS 0x0000000701a00000, 0x0000000701a00000| Untracked 
|  13|0x0000000701a00000, 0x0000000701c00000, 0x0000000701c00000|100%| O|  |TAMS 0x0000000701c00000, 0x0000000701c00000| Untracked 
|  14|0x0000000701c00000, 0x0000000701e00000, 0x0000000701e00000|100%| O|  |TAMS 0x0000000701e00000, 0x0000000701e00000| Untracked 
|  15|0x0000000701e00000, 0x0000000702000000, 0x0000000702000000|100%| O|  |TAMS 0x0000000702000000, 0x0000000702000000| Untracked 
|  16|0x0000000702000000, 0x0000000702200000, 0x0000000702200000|100%| O|  |TAMS 0x0000000702200000, 0x0000000702200000| Untracked 
|  17|0x0000000702200000, 0x0000000702400000, 0x0000000702400000|100%| O|  |TAMS 0x0000000702400000, 0x0000000702400000| Untracked 
|  18|0x0000000702400000, 0x0000000702600000, 0x0000000702600000|100%| O|  |TAMS 0x0000000702600000, 0x0000000702600000| Untracked 
|  19|0x0000000702600000, 0x0000000702800000, 0x0000000702800000|100%| O|  |TAMS 0x0000000702800000, 0x0000000702800000| Untracked 
|  20|0x0000000702800000, 0x0000000702a00000, 0x0000000702a00000|100%| O|  |TAMS 0x0000000702a00000, 0x0000000702a00000| Untracked 
|  21|0x0000000702a00000, 0x0000000702c00000, 0x0000000702c00000|100%| O|  |TAMS 0x0000000702c00000, 0x0000000702c00000| Untracked 
|  22|0x0000000702c00000, 0x0000000702e00000, 0x0000000702e00000|100%| O|  |TAMS 0x0000000702e00000, 0x0000000702e00000| Untracked 
|  23|0x0000000702e00000, 0x0000000703000000, 0x0000000703000000|100%| O|  |TAMS 0x0000000703000000, 0x0000000703000000| Untracked 
|  24|0x0000000703000000, 0x0000000703200000, 0x0000000703200000|100%| O|  |TAMS 0x0000000703200000, 0x0000000703200000| Untracked 
|  25|0x0000000703200000, 0x0000000703400000, 0x0000000703400000|100%|HS|  |TAMS 0x0000000703400000, 0x0000000703400000| Complete 
|  26|0x0000000703400000, 0x0000000703600000, 0x0000000703600000|100%| O|  |TAMS 0x0000000703600000, 0x0000000703600000| Untracked 
|  27|0x0000000703600000, 0x0000000703800000, 0x0000000703800000|100%| O|  |TAMS 0x0000000703800000, 0x0000000703800000| Untracked 
|  28|0x0000000703800000, 0x0000000703a00000, 0x0000000703a00000|100%|HS|  |TAMS 0x0000000703a00000, 0x0000000703a00000| Complete 
|  29|0x0000000703a00000, 0x0000000703c00000, 0x0000000703c00000|100%|HC|  |TAMS 0x0000000703c00000, 0x0000000703c00000| Complete 
|  30|0x0000000703c00000, 0x0000000703e00000, 0x0000000703e00000|100%| O|  |TAMS 0x0000000703e00000, 0x0000000703e00000| Untracked 
|  31|0x0000000703e00000, 0x0000000704000000, 0x0000000704000000|100%| O|  |TAMS 0x0000000703f09000, 0x0000000704000000| Untracked 
|  32|0x0000000704000000, 0x0000000704200000, 0x0000000704200000|100%| O|  |TAMS 0x0000000704000000, 0x0000000704200000| Untracked 
|  33|0x0000000704200000, 0x0000000704400000, 0x0000000704400000|100%| O|  |TAMS 0x0000000704200000, 0x0000000704400000| Untracked 
|  34|0x0000000704400000, 0x00000007044e6a00, 0x0000000704600000| 45%| O|  |TAMS 0x0000000704400000, 0x00000007044e6a00| Untracked 
|  35|0x0000000704600000, 0x0000000704600000, 0x0000000704800000|  0%| F|  |TAMS 0x0000000704600000, 0x0000000704600000| Untracked 
|  36|0x0000000704800000, 0x0000000704800000, 0x0000000704a00000|  0%| F|  |TAMS 0x0000000704800000, 0x0000000704800000| Untracked 
|  37|0x0000000704a00000, 0x0000000704a00000, 0x0000000704c00000|  0%| F|  |TAMS 0x0000000704a00000, 0x0000000704a00000| Untracked 
|  38|0x0000000704c00000, 0x0000000704c00000, 0x0000000704e00000|  0%| F|  |TAMS 0x0000000704c00000, 0x0000000704c00000| Untracked 
|  39|0x0000000704e00000, 0x0000000704e00000, 0x0000000705000000|  0%| F|  |TAMS 0x0000000704e00000, 0x0000000704e00000| Untracked 
|  40|0x0000000705000000, 0x0000000705000000, 0x0000000705200000|  0%| F|  |TAMS 0x0000000705000000, 0x0000000705000000| Untracked 
|  41|0x0000000705200000, 0x0000000705200000, 0x0000000705400000|  0%| F|  |TAMS 0x0000000705200000, 0x0000000705200000| Untracked 
|  42|0x0000000705400000, 0x0000000705400000, 0x0000000705600000|  0%| F|  |TAMS 0x0000000705400000, 0x0000000705400000| Untracked 
|  43|0x0000000705600000, 0x0000000705600000, 0x0000000705800000|  0%| F|  |TAMS 0x0000000705600000, 0x0000000705600000| Untracked 
|  44|0x0000000705800000, 0x0000000705800000, 0x0000000705a00000|  0%| F|  |TAMS 0x0000000705800000, 0x0000000705800000| Untracked 
|  45|0x0000000705a00000, 0x0000000705a00000, 0x0000000705c00000|  0%| F|  |TAMS 0x0000000705a00000, 0x0000000705a00000| Untracked 
|  46|0x0000000705c00000, 0x0000000705c00000, 0x0000000705e00000|  0%| F|  |TAMS 0x0000000705c00000, 0x0000000705c00000| Untracked 
|  47|0x0000000705e00000, 0x0000000705e00000, 0x0000000706000000|  0%| F|  |TAMS 0x0000000705e00000, 0x0000000705e00000| Untracked 
|  48|0x0000000706000000, 0x0000000706090630, 0x0000000706200000| 28%| S|CS|TAMS 0x0000000706000000, 0x0000000706000000| Complete 
|  49|0x0000000706200000, 0x0000000706400000, 0x0000000706400000|100%| S|CS|TAMS 0x0000000706200000, 0x0000000706200000| Complete 
|  50|0x0000000706400000, 0x0000000706400000, 0x0000000706600000|  0%| F|  |TAMS 0x0000000706400000, 0x0000000706400000| Untracked 
|  51|0x0000000706600000, 0x0000000706600000, 0x0000000706800000|  0%| F|  |TAMS 0x0000000706600000, 0x0000000706600000| Untracked 
|  52|0x0000000706800000, 0x0000000706800000, 0x0000000706a00000|  0%| F|  |TAMS 0x0000000706800000, 0x0000000706800000| Untracked 
|  53|0x0000000706a00000, 0x0000000706a00000, 0x0000000706c00000|  0%| F|  |TAMS 0x0000000706a00000, 0x0000000706a00000| Untracked 
|  54|0x0000000706c00000, 0x0000000706c00000, 0x0000000706e00000|  0%| F|  |TAMS 0x0000000706c00000, 0x0000000706c00000| Untracked 
|  55|0x0000000706e00000, 0x0000000706e00000, 0x0000000707000000|  0%| F|  |TAMS 0x0000000706e00000, 0x0000000706e00000| Untracked 
|  96|0x000000070c000000, 0x000000070c000000, 0x000000070c200000|  0%| F|  |TAMS 0x000000070c000000, 0x000000070c000000| Untracked 
|  97|0x000000070c200000, 0x000000070c200000, 0x000000070c400000|  0%| F|  |TAMS 0x000000070c200000, 0x000000070c200000| Untracked 
|  98|0x000000070c400000, 0x000000070c400000, 0x000000070c600000|  0%| F|  |TAMS 0x000000070c400000, 0x000000070c400000| Untracked 
|  99|0x000000070c600000, 0x000000070c600000, 0x000000070c800000|  0%| F|  |TAMS 0x000000070c600000, 0x000000070c600000| Untracked 
| 100|0x000000070c800000, 0x000000070c800000, 0x000000070ca00000|  0%| F|  |TAMS 0x000000070c800000, 0x000000070c800000| Untracked 
| 126|0x000000070fc00000, 0x000000070fc00000, 0x000000070fe00000|  0%| F|  |TAMS 0x000000070fc00000, 0x000000070fc00000| Untracked 

Card table byte_map: [0x000001aa4bc90000,0x000001aa4c490000] _byte_map_base: 0x000001aa48490000

Marking Bits (Prev, Next): (CMBitMap*) 0x000001aa387a1a90, (CMBitMap*) 0x000001aa387a1a50
 Prev Bits: [0x000001aa50c90000, 0x000001aa54c90000)
 Next Bits: [0x000001aa4cc90000, 0x000001aa50c90000)

Polling page: 0x000001aa365e0000

Metaspace:

Usage:
  Non-class:     64.55 MB used.
      Class:     10.07 MB used.
       Both:     74.62 MB used.

Virtual space:
  Non-class space:      128.00 MB reserved,      64.88 MB ( 51%) committed,  2 nodes.
      Class space:        1.00 GB reserved,      10.31 MB (  1%) committed,  1 nodes.
             Both:        1.12 GB reserved,      75.19 MB (  7%) committed. 

Chunk freelists:
   Non-Class:  14.58 MB
       Class:  5.62 MB
        Both:  20.20 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 120.88 MB
CDS: off
MetaspaceReclaimPolicy: balanced
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - new_chunks_are_fully_committed: 0.
 - uncommit_free_chunks: 1.
 - use_allocation_guard: 0.
 - handle_deallocations: 1.


Internal statistics:

num_allocs_failed_limit: 8.
num_arena_births: 778.
num_arena_deaths: 0.
num_vsnodes_births: 3.
num_vsnodes_deaths: 0.
num_space_committed: 1203.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 8.
num_chunks_taken_from_freelist: 3390.
num_chunk_merges: 3.
num_chunk_splits: 2271.
num_chunks_enlarged: 1577.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=119168Kb used=4790Kb max_used=4790Kb free=114378Kb
 bounds [0x000001aa437a0000, 0x000001aa43c50000, 0x000001aa4ac00000]
CodeHeap 'profiled nmethods': size=119104Kb used=16095Kb max_used=16095Kb free=103009Kb
 bounds [0x000001aa3bc00000, 0x000001aa3cbc0000, 0x000001aa43050000]
CodeHeap 'non-nmethods': size=7488Kb used=4060Kb max_used=4144Kb free=3427Kb
 bounds [0x000001aa43050000, 0x000001aa43480000, 0x000001aa437a0000]
 total_blobs=8932 nmethods=8032 adapters=809
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 18.070 Thread 0x000001aa6076cfa0 8784       3       java.util.regex.Pattern::compile (500 bytes)
Event: 18.071 Thread 0x000001aa6076d9c0 nmethod 8783 0x000001aa43c47390 code [0x000001aa43c47520, 0x000001aa43c475d8]
Event: 18.071 Thread 0x000001aa6076ca90 8785       4       org.objectweb.asm.Type::getOpcode (284 bytes)
Event: 18.071 Thread 0x000001aa6076d4b0 8786       3       org.gradle.internal.instantiation.generator.AsmBackedClassGenerator$ClassBuilderImpl$LocalMethodVisitorScope::putServiceRegistryOnStack (56 bytes)
Event: 18.072 Thread 0x000001aa6076d4b0 nmethod 8786 0x000001aa3cbb0390 code [0x000001aa3cbb0600, 0x000001aa3cbb1258]
Event: 18.072 Thread 0x000001aa6076ca90 nmethod 8785 0x000001aa43c47690 code [0x000001aa43c47820, 0x000001aa43c47998]
Event: 18.072 Thread 0x000001aa6076cfa0 nmethod 8784 0x000001aa3cbb1710 code [0x000001aa3cbb1bc0, 0x000001aa3cbb4a68]
Event: 18.083 Thread 0x000001aa6076ca90 8787       4       sun.reflect.generics.tree.ClassTypeSignature::accept (8 bytes)
Event: 18.084 Thread 0x000001aa6076ca90 nmethod 8787 0x000001aa43c47b10 code [0x000001aa43c47ca0, 0x000001aa43c47d88]
Event: 18.085 Thread 0x000001aa6076ca90 8788       4       java.lang.AbstractStringBuilder::<init> (63 bytes)
Event: 18.090 Thread 0x000001aa5c0cc630 8789       3       org.gradle.internal.instantiation.generator.AbstractClassGenerator$DslMixInPropertyType::addSetMethods (180 bytes)
Event: 18.091 Thread 0x000001aa5c0cc630 nmethod 8789 0x000001aa3cbb5490 code [0x000001aa3cbb57e0, 0x000001aa3cbb70c8]
Event: 18.092 Thread 0x000001aa6076d9c0 8790       4       java.util.concurrent.locks.ReentrantLock$Sync::tryLock (63 bytes)
Event: 18.093 Thread 0x000001aa5c0cb3e0 8791       4       org.gradle.internal.instantiation.generator.ParamsMatchingConstructorSelector::vetoParameters (1 bytes)
Event: 18.093 Thread 0x000001aa5c0cb3e0 nmethod 8791 0x000001aa43c47e90 code [0x000001aa43c48000, 0x000001aa43c48078]
Event: 18.093 Thread 0x000001aa5c0cc630 8792       3       org.gradle.api.internal.AbstractMutationGuard::withMutationDisabled (7 bytes)
Event: 18.093 Thread 0x000001aa5c0cc630 nmethod 8792 0x000001aa3cbb7810 code [0x000001aa3cbb79c0, 0x000001aa3cbb7b08]
Event: 18.094 Thread 0x000001aa6076d9c0 nmethod 8790 0x000001aa43c48190 code [0x000001aa43c48300, 0x000001aa43c484f8]
Event: 18.095 Thread 0x000001aa6076ca90 nmethod 8788 0x000001aa43c48610 code [0x000001aa43c487c0, 0x000001aa43c492a8]
Event: 18.096 Thread 0x000001aa6076e3e0 nmethod 8778 0x000001aa43c49810 code [0x000001aa43c49c20, 0x000001aa43c4be30]

GC Heap History (20 events):
Event: 15.168 GC heap after
{Heap after GC invocations=22 (full 0):
 garbage-first heap   total 75776K, used 44549K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 1 young (2048K), 1 survivors (2048K)
 Metaspace       used 63950K, committed 64512K, reserved 1114112K
  class space    used 8761K, committed 9024K, reserved 1048576K
}
Event: 15.604 GC heap before
{Heap before GC invocations=23 (full 0):
 garbage-first heap   total 79872K, used 58885K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 9 young (18432K), 1 survivors (2048K)
 Metaspace       used 64052K, committed 64576K, reserved 1114112K
  class space    used 8763K, committed 9024K, reserved 1048576K
}
Event: 15.606 GC heap after
{Heap after GC invocations=24 (full 0):
 garbage-first heap   total 79872K, used 50523K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 2 young (4096K), 2 survivors (4096K)
 Metaspace       used 64052K, committed 64576K, reserved 1114112K
  class space    used 8763K, committed 9024K, reserved 1048576K
}
Event: 15.645 GC heap before
{Heap before GC invocations=24 (full 0):
 garbage-first heap   total 79872K, used 52571K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 4 young (8192K), 2 survivors (4096K)
 Metaspace       used 64054K, committed 64576K, reserved 1114112K
  class space    used 8763K, committed 9024K, reserved 1048576K
}
Event: 15.647 GC heap after
{Heap after GC invocations=25 (full 0):
 garbage-first heap   total 79872K, used 51948K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 1 young (2048K), 1 survivors (2048K)
 Metaspace       used 64054K, committed 64576K, reserved 1114112K
  class space    used 8763K, committed 9024K, reserved 1048576K
}
Event: 15.807 GC heap before
{Heap before GC invocations=26 (full 0):
 garbage-first heap   total 96256K, used 62188K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 5 young (10240K), 1 survivors (2048K)
 Metaspace       used 64478K, committed 65088K, reserved 1114112K
  class space    used 8820K, committed 9088K, reserved 1048576K
}
Event: 15.809 GC heap after
{Heap after GC invocations=27 (full 0):
 garbage-first heap   total 96256K, used 55367K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 1 young (2048K), 1 survivors (2048K)
 Metaspace       used 64478K, committed 65088K, reserved 1114112K
  class space    used 8820K, committed 9088K, reserved 1048576K
}
Event: 16.111 GC heap before
{Heap before GC invocations=27 (full 0):
 garbage-first heap   total 96256K, used 67655K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 8 young (16384K), 1 survivors (2048K)
 Metaspace       used 64942K, committed 65600K, reserved 1114112K
  class space    used 8884K, committed 9152K, reserved 1048576K
}
Event: 16.116 GC heap after
{Heap after GC invocations=28 (full 0):
 garbage-first heap   total 96256K, used 58454K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 1 young (2048K), 1 survivors (2048K)
 Metaspace       used 64942K, committed 65600K, reserved 1114112K
  class space    used 8884K, committed 9152K, reserved 1048576K
}
Event: 16.393 GC heap before
{Heap before GC invocations=29 (full 0):
 garbage-first heap   total 110592K, used 74838K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 7 young (14336K), 1 survivors (2048K)
 Metaspace       used 66238K, committed 66880K, reserved 1114112K
  class space    used 9051K, committed 9344K, reserved 1048576K
}
Event: 16.396 GC heap after
{Heap after GC invocations=30 (full 0):
 garbage-first heap   total 110592K, used 64489K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 1 young (2048K), 1 survivors (2048K)
 Metaspace       used 66238K, committed 66880K, reserved 1114112K
  class space    used 9051K, committed 9344K, reserved 1048576K
}
Event: 16.779 GC heap before
{Heap before GC invocations=30 (full 0):
 garbage-first heap   total 110592K, used 78825K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 9 young (18432K), 1 survivors (2048K)
 Metaspace       used 68368K, committed 68992K, reserved 1114112K
  class space    used 9333K, committed 9600K, reserved 1048576K
}
Event: 16.783 GC heap after
{Heap after GC invocations=31 (full 0):
 garbage-first heap   total 110592K, used 65115K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 2 young (4096K), 2 survivors (4096K)
 Metaspace       used 68368K, committed 68992K, reserved 1114112K
  class space    used 9333K, committed 9600K, reserved 1048576K
}
Event: 17.158 GC heap before
{Heap before GC invocations=32 (full 0):
 garbage-first heap   total 116736K, used 81499K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 10 young (20480K), 2 survivors (4096K)
 Metaspace       used 71057K, committed 71680K, reserved 1114112K
  class space    used 9638K, committed 9920K, reserved 1048576K
}
Event: 17.160 GC heap after
{Heap after GC invocations=33 (full 0):
 garbage-first heap   total 116736K, used 65766K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 1 young (2048K), 1 survivors (2048K)
 Metaspace       used 71057K, committed 71680K, reserved 1114112K
  class space    used 9638K, committed 9920K, reserved 1048576K
}
Event: 17.484 GC heap before
{Heap before GC invocations=33 (full 0):
 garbage-first heap   total 116736K, used 86246K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 12 young (24576K), 1 survivors (2048K)
 Metaspace       used 73506K, committed 74240K, reserved 1114112K
  class space    used 9927K, committed 10240K, reserved 1048576K
}
Event: 17.488 GC heap after
{Heap after GC invocations=34 (full 0):
 garbage-first heap   total 116736K, used 71911K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 2 young (4096K), 2 survivors (4096K)
 Metaspace       used 73506K, committed 74240K, reserved 1114112K
  class space    used 9927K, committed 10240K, reserved 1048576K
}
Event: 17.817 GC heap before
{Heap before GC invocations=35 (full 0):
 garbage-first heap   total 126976K, used 88295K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 10 young (20480K), 2 survivors (4096K)
 Metaspace       used 74565K, committed 75200K, reserved 1114112K
  class space    used 10079K, committed 10368K, reserved 1048576K
}
Event: 17.820 GC heap after
{Heap after GC invocations=36 (full 0):
 garbage-first heap   total 126976K, used 72137K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 1 young (2048K), 1 survivors (2048K)
 Metaspace       used 74565K, committed 75200K, reserved 1114112K
  class space    used 10079K, committed 10368K, reserved 1048576K
}
Event: 18.117 GC heap before
{Heap before GC invocations=36 (full 0):
 garbage-first heap   total 126976K, used 94665K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 12 young (24576K), 1 survivors (2048K)
 Metaspace       used 76414K, committed 76992K, reserved 1179648K
  class space    used 10312K, committed 10560K, reserved 1048576K
}

Dll operation events (2 events):
Event: 0.009 Loaded shared library C:\Program Files\Android\Android Studio\jbr\bin\java.dll
Event: 0.413 Loaded shared library C:\Program Files\Android\Android Studio\jbr\bin\verify.dll

Deoptimization events (20 events):
Event: 17.824 Thread 0x000001aa5cceb3a0 DEOPT PACKING pc=0x000001aa43ab620c sp=0x0000002aaa0f1410
Event: 17.824 Thread 0x000001aa5cceb3a0 DEOPT UNPACKING pc=0x000001aa430a69a3 sp=0x0000002aaa0f11b8 mode 2
Event: 17.824 Thread 0x000001aa5cceb3a0 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000001aa438cdac4 relative=0x00000000000006e4
Event: 17.824 Thread 0x000001aa5cceb3a0 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000001aa438cdac4 method=java.lang.CharacterDataLatin1.isUpperCase(I)Z @ 7 c2
Event: 17.824 Thread 0x000001aa5cceb3a0 DEOPT PACKING pc=0x000001aa438cdac4 sp=0x0000002aaa0f1330
Event: 17.824 Thread 0x000001aa5cceb3a0 DEOPT UNPACKING pc=0x000001aa430a69a3 sp=0x0000002aaa0f11e8 mode 2
Event: 17.824 Thread 0x000001aa5cceb3a0 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000001aa438ce300 relative=0x0000000000000080
Event: 17.824 Thread 0x000001aa5cceb3a0 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000001aa438ce300 method=java.lang.CharacterDataLatin1.isUpperCase(I)Z @ 7 c2
Event: 17.824 Thread 0x000001aa5cceb3a0 DEOPT PACKING pc=0x000001aa438ce300 sp=0x0000002aaa0f12c0
Event: 17.824 Thread 0x000001aa5cceb3a0 DEOPT UNPACKING pc=0x000001aa430a69a3 sp=0x0000002aaa0f1240 mode 2
Event: 17.867 Thread 0x000001aa5cceb3a0 Uncommon trap: trap_request=0xffffffc6 fr.pc=0x000001aa43b1e520 relative=0x0000000000000060
Event: 17.867 Thread 0x000001aa5cceb3a0 Uncommon trap: reason=bimorphic_or_optimized_type_check action=maybe_recompile pc=0x000001aa43b1e520 method=java.util.AbstractQueue.add(Ljava/lang/Object;)Z @ 2 c2
Event: 17.867 Thread 0x000001aa5cceb3a0 DEOPT PACKING pc=0x000001aa43b1e520 sp=0x0000002aaa0f1920
Event: 17.867 Thread 0x000001aa5cceb3a0 DEOPT UNPACKING pc=0x000001aa430a69a3 sp=0x0000002aaa0f18e8 mode 2
Event: 17.867 Thread 0x000001aa5cceb3a0 Uncommon trap: trap_request=0xffffffc6 fr.pc=0x000001aa43b1e520 relative=0x0000000000000060
Event: 17.867 Thread 0x000001aa5cceb3a0 Uncommon trap: reason=bimorphic_or_optimized_type_check action=maybe_recompile pc=0x000001aa43b1e520 method=java.util.AbstractQueue.add(Ljava/lang/Object;)Z @ 2 c2
Event: 17.867 Thread 0x000001aa5cceb3a0 DEOPT PACKING pc=0x000001aa43b1e520 sp=0x0000002aaa0f1920
Event: 17.867 Thread 0x000001aa5cceb3a0 DEOPT UNPACKING pc=0x000001aa430a69a3 sp=0x0000002aaa0f18e8 mode 2
Event: 18.020 Thread 0x000001aa5cceb3a0 DEOPT PACKING pc=0x000001aa3cb04961 sp=0x0000002aaa0f0950
Event: 18.020 Thread 0x000001aa5cceb3a0 DEOPT UNPACKING pc=0x000001aa430a7143 sp=0x0000002aaa0efef0 mode 0

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 15.810 Thread 0x000001aa5cceb3a0 Exception <a 'java/lang/ClassNotFoundException'{0x000000070fc563e8}: java/lang/StringCustomizer> (0x000000070fc563e8) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 15.826 Thread 0x000001aa5cceb3a0 Exception <a 'java/lang/ClassNotFoundException'{0x000000070fd04cb8}: settings_2gruv7bjn7ke34ukd7sgbaihaBeanInfo> (0x000000070fd04cb8) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 15.826 Thread 0x000001aa5cceb3a0 Exception <a 'java/lang/ClassNotFoundException'{0x000000070fd0a7a8}: settings_2gruv7bjn7ke34ukd7sgbaihaCustomizer> (0x000000070fd0a7a8) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 15.874 Thread 0x000001aa5cceb3a0 Exception <a 'java/lang/ClassNotFoundException'{0x000000070c93e7c8}: build_cbgkpd0obcy601vdg3ozkkrdeBeanInfo> (0x000000070c93e7c8) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 15.874 Thread 0x000001aa5cceb3a0 Exception <a 'java/lang/ClassNotFoundException'{0x000000070c948da8}: org/gradle/api/internal/project/ProjectScriptBeanInfo> (0x000000070c948da8) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 15.874 Thread 0x000001aa5cceb3a0 Exception <a 'java/lang/ClassNotFoundException'{0x000000070c953630}: org/gradle/api/internal/project/ProjectScriptCustomizer> (0x000000070c953630) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 15.875 Thread 0x000001aa5cceb3a0 Exception <a 'java/lang/ClassNotFoundException'{0x000000070c96ab50}: build_cbgkpd0obcy601vdg3ozkkrdeCustomizer> (0x000000070c96ab50) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 15.880 Thread 0x000001aa5cceb3a0 Exception <a 'java/lang/ClassNotFoundException'{0x000000070c9b6548}: org/gradle/api/internal/initialization/DefaultScriptHandlerBeanInfo> (0x000000070c9b6548) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 15.881 Thread 0x000001aa5cceb3a0 Exception <a 'java/lang/ClassNotFoundException'{0x000000070c9c8b20}: org/gradle/api/internal/initialization/DefaultScriptHandlerCustomizer> (0x000000070c9c8b20) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 15.885 Thread 0x000001aa5cceb3a0 Implicit null exception at 0x000001aa43ae5090 to 0x000001aa43ae5788
Event: 15.888 Thread 0x000001aa5cceb3a0 Exception <a 'java/lang/ClassNotFoundException'{0x000000070c636610}: org/gradle/api/internal/artifacts/dsl/dependencies/DefaultDependencyHandler_DecoratedBeanInfo> (0x000000070c636610) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 15.889 Thread 0x000001aa5cceb3a0 Exception <a 'java/lang/ClassNotFoundException'{0x000000070c64d8e8}: org/gradle/api/internal/artifacts/dsl/dependencies/DefaultDependencyHandlerBeanInfo> (0x000000070c64d8e8) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 15.889 Thread 0x000001aa5cceb3a0 Exception <a 'java/lang/ClassNotFoundException'{0x000000070c664e60}: org/gradle/api/internal/artifacts/dsl/dependencies/DefaultDependencyHandlerCustomizer> (0x000000070c664e60) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 15.891 Thread 0x000001aa5cceb3a0 Exception <a 'java/lang/ClassNotFoundException'{0x000000070c6a5190}: org/gradle/api/internal/artifacts/dsl/dependencies/DefaultDependencyHandler_DecoratedCustomizer> (0x000000070c6a5190) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 16.028 Thread 0x000001aa5cceb3a0 Exception <a 'java/lang/ClassNotFoundException'{0x000000070513c170}: build_cbgkpd0obcy601vdg3ozkkrdeBeanInfo> (0x000000070513c170) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 16.029 Thread 0x000001aa5cceb3a0 Exception <a 'java/lang/ClassNotFoundException'{0x0000000705142990}: build_cbgkpd0obcy601vdg3ozkkrdeCustomizer> (0x0000000705142990) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 16.170 Thread 0x000001aa5cceb3a0 Exception <a 'java/lang/ClassNotFoundException'{0x000000070fd4fc50}: build_f4lyfob7og778nmfmhgwc9617BeanInfo> (0x000000070fd4fc50) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 16.171 Thread 0x000001aa5cceb3a0 Exception <a 'java/lang/ClassNotFoundException'{0x000000070fd72b48}: build_f4lyfob7og778nmfmhgwc9617Customizer> (0x000000070fd72b48) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 17.377 Thread 0x000001aa5cceb3a0 Implicit null exception at 0x000001aa4396858c to 0x000001aa439686c2
Event: 17.627 Thread 0x000001aa5cceb3a0 Exception <a 'sun/nio/fs/WindowsException'{0x000000070c5b1458}> (0x000000070c5b1458) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 517]

VM Operations (20 events):
Event: 16.826 Executing VM operation: G1Concurrent done
Event: 16.842 Executing VM operation: G1Concurrent
Event: 16.842 Executing VM operation: G1Concurrent done
Event: 16.972 Executing VM operation: HandshakeAllThreads
Event: 16.973 Executing VM operation: HandshakeAllThreads done
Event: 17.158 Executing VM operation: G1CollectForAllocation
Event: 17.160 Executing VM operation: G1CollectForAllocation done
Event: 17.346 Executing VM operation: HandshakeAllThreads
Event: 17.347 Executing VM operation: HandshakeAllThreads done
Event: 17.387 Executing VM operation: HandshakeAllThreads
Event: 17.387 Executing VM operation: HandshakeAllThreads done
Event: 17.484 Executing VM operation: G1CollectForAllocation
Event: 17.488 Executing VM operation: G1CollectForAllocation done
Event: 17.523 Executing VM operation: G1Concurrent
Event: 17.528 Executing VM operation: G1Concurrent done
Event: 17.546 Executing VM operation: G1Concurrent
Event: 17.546 Executing VM operation: G1Concurrent done
Event: 17.817 Executing VM operation: G1CollectForAllocation
Event: 17.820 Executing VM operation: G1CollectForAllocation done
Event: 18.117 Executing VM operation: G1CollectForAllocation

Events (20 events):
Event: 17.426 loading class sun/security/rsa/RSASignature$SHA256withRSA done
Event: 17.427 loading class sun/security/provider/DSA$SHA256withDSA
Event: 17.427 loading class sun/security/provider/DSA$SHA256withDSA done
Event: 17.443 loading class java/util/Base64
Event: 17.443 loading class java/util/Base64 done
Event: 17.443 loading class java/util/Base64$Decoder
Event: 17.449 loading class java/util/Base64$Decoder done
Event: 17.449 loading class java/util/Base64$Encoder
Event: 17.450 loading class java/util/Base64$Encoder done
Event: 17.455 loading class java/util/jar/JarVerifier$VerifierStream
Event: 17.456 loading class java/util/jar/JarVerifier$VerifierStream done
Event: 17.456 loading class sun/security/util/ManifestEntryVerifier$SunProviderHolder
Event: 17.456 loading class sun/security/util/ManifestEntryVerifier$SunProviderHolder done
Event: 17.466 Thread 0x000001aa6076e3e0 Thread added: 0x000001aa6076e3e0
Event: 17.627 loading class sun/nio/fs/WindowsException
Event: 17.627 loading class sun/nio/fs/WindowsException done
Event: 18.033 loading class java/security/KeyStore
Event: 18.033 loading class java/security/KeyStore done
Event: 18.033 loading class java/security/KeyStore$1
Event: 18.033 loading class java/security/KeyStore$1 done


Dynamic libraries:
0x00007ff706710000 - 0x00007ff70671a000 	C:\Program Files\Android\Android Studio\jbr\bin\java.exe
0x00007ffb87780000 - 0x00007ffb879e0000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ffb863b0000 - 0x00007ffb86477000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ffb84bf0000 - 0x00007ffb84fba000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ffb84fc0000 - 0x00007ffb8510b000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ffb59540000 - 0x00007ffb59557000 	C:\Program Files\Android\Android Studio\jbr\bin\jli.dll
0x00007ffb59520000 - 0x00007ffb5953b000 	C:\Program Files\Android\Android Studio\jbr\bin\VCRUNTIME140.dll
0x00007ffb86480000 - 0x00007ffb8664c000 	C:\WINDOWS\System32\USER32.dll
0x00007ffb85550000 - 0x00007ffb85577000 	C:\WINDOWS\System32\win32u.dll
0x00007ffb47d40000 - 0x00007ffb47fd7000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.3624_none_3e086962e3345f24\COMCTL32.dll
0x00007ffb85580000 - 0x00007ffb855aa000 	C:\WINDOWS\System32\GDI32.dll
0x00007ffb86870000 - 0x00007ffb86919000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ffb84a00000 - 0x00007ffb84b31000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ffb84b40000 - 0x00007ffb84be3000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffb85b70000 - 0x00007ffb85b9f000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ffb6c850000 - 0x00007ffb6c85c000 	C:\Program Files\Android\Android Studio\jbr\bin\vcruntime140_1.dll
0x00007ffa69320000 - 0x00007ffa693ad000 	C:\Program Files\Android\Android Studio\jbr\bin\msvcp140.dll
0x00007ffa645d0000 - 0x00007ffa65250000 	C:\Program Files\Android\Android Studio\jbr\bin\server\jvm.dll
0x00007ffb86970000 - 0x00007ffb86a22000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ffb86660000 - 0x00007ffb86706000 	C:\WINDOWS\System32\sechost.dll
0x00007ffb86710000 - 0x00007ffb86826000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffb6cfa0000 - 0x00007ffb6cfaa000 	C:\WINDOWS\SYSTEM32\WSOCK32.dll
0x00007ffb73320000 - 0x00007ffb73356000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ffb78740000 - 0x00007ffb7874b000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ffb861d0000 - 0x00007ffb86244000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ffb83870000 - 0x00007ffb8388a000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ffb0e950000 - 0x00007ffb0e95a000 	C:\Program Files\Android\Android Studio\jbr\bin\jimage.dll
0x00007ffb82a40000 - 0x00007ffb82c81000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ffb87240000 - 0x00007ffb875c2000 	C:\WINDOWS\System32\combase.dll
0x00007ffb87160000 - 0x00007ffb87236000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ffb73250000 - 0x00007ffb73289000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ffb84960000 - 0x00007ffb849f9000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ffb0c5d0000 - 0x00007ffb0c5f5000 	C:\Program Files\Android\Android Studio\jbr\bin\java.dll
0x00007ffb0c5b0000 - 0x00007ffb0c5c8000 	C:\Program Files\Android\Android Studio\jbr\bin\zip.dll
0x00007ffb86a30000 - 0x00007ffb87159000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ffb85290000 - 0x00007ffb853f8000 	C:\WINDOWS\System32\wintypes.dll
0x00007ffb82110000 - 0x00007ffb82962000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ffb86250000 - 0x00007ffb8633d000 	C:\WINDOWS\System32\SHCORE.dll
0x00007ffb876d0000 - 0x00007ffb87734000 	C:\WINDOWS\System32\shlwapi.dll
0x00007ffb84870000 - 0x00007ffb8489f000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ffb0c590000 - 0x00007ffb0c5a9000 	C:\Program Files\Android\Android Studio\jbr\bin\net.dll
0x00007ffb7e270000 - 0x00007ffb7e38d000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007ffb83db0000 - 0x00007ffb83e1a000 	C:\WINDOWS\system32\mswsock.dll
0x00007ffa932f0000 - 0x00007ffa93306000 	C:\Program Files\Android\Android Studio\jbr\bin\nio.dll
0x00007ffb0e8e0000 - 0x00007ffb0e8f0000 	C:\Program Files\Android\Android Studio\jbr\bin\verify.dll
0x00007ffa911a0000 - 0x00007ffa911c7000 	C:\Users\<USER>\.gradle\native\e1d6ef7f7dcc3fd88c89a11ec53ec762bb8ba0a96d01ffa2cd45eb1d1d8dd5c5\windows-amd64\native-platform.dll
0x00007ffa66360000 - 0x00007ffa664a4000 	C:\Users\<USER>\.gradle\native\0d407fdbe67a94daf76414ababcb853783967236a71b16ec16e742cd7a986fd3\windows-amd64\native-platform-file-events.dll
0x00007ffac10a0000 - 0x00007ffac10a9000 	C:\Program Files\Android\Android Studio\jbr\bin\management.dll
0x00007ffac0c60000 - 0x00007ffac0c6b000 	C:\Program Files\Android\Android Studio\jbr\bin\management_ext.dll
0x00007ffb86860000 - 0x00007ffb86868000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007ffb84050000 - 0x00007ffb8406c000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007ffb837d0000 - 0x00007ffb8380a000 	C:\WINDOWS\system32\rsaenh.dll
0x00007ffb83e50000 - 0x00007ffb83e7b000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007ffb84840000 - 0x00007ffb84866000 	C:\WINDOWS\SYSTEM32\bcrypt.dll
0x00007ffb84070000 - 0x00007ffb8407c000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007ffb831e0000 - 0x00007ffb83213000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007ffb86650000 - 0x00007ffb8665a000 	C:\WINDOWS\System32\NSI.dll
0x00007ffb7dba0000 - 0x00007ffb7dbbf000 	C:\WINDOWS\SYSTEM32\dhcpcsvc6.DLL
0x00007ffb7d700000 - 0x00007ffb7d725000 	C:\WINDOWS\SYSTEM32\dhcpcsvc.DLL
0x00007ffb83270000 - 0x00007ffb83395000 	C:\WINDOWS\SYSTEM32\DNSAPI.dll
0x00007ffae7e30000 - 0x00007ffae7e38000 	C:\WINDOWS\system32\wshunix.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Program Files\Android\Android Studio\jbr\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.3624_none_3e086962e3345f24;C:\Program Files\Android\Android Studio\jbr\bin\server;C:\Users\<USER>\.gradle\native\e1d6ef7f7dcc3fd88c89a11ec53ec762bb8ba0a96d01ffa2cd45eb1d1d8dd5c5\windows-amd64;C:\Users\<USER>\.gradle\native\0d407fdbe67a94daf76414ababcb853783967236a71b16ec16e742cd7a986fd3\windows-amd64

VM Arguments:
jvm_args: --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED -Xmx4G -Dfile.encoding=windows-1252 -Duser.country=US -Duser.language=en -Duser.variant 
java_command: org.gradle.launcher.daemon.bootstrap.GradleDaemon 7.5
java_class_path (initial): C:\Users\<USER>\.gradle\wrapper\dists\gradle-7.5-all\6qsw290k5lz422uaf8jf6m7co\gradle-7.5\lib\gradle-launcher-7.5.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 12                                        {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 13                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 2097152                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 266338304                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 4294967296                                {product} {command line}
   size_t MaxNewSize                               = 2575302656                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 2097152                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 7602480                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122027880                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122027880                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 4294967296                             {manageable} {ergonomic}
     bool UseCompressedClassPointers               = true                           {product lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
JAVA_HOME=C:\Program Files\Android\Android Studio\jbr
CLASSPATH=D:\Ecommerce-App\android\\gradle\wrapper\gradle-wrapper.jar
PATH=C:\Program Files\Android\Android Studio\jbr\bin;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Microsoft SQL Server\150\Tools\Binn\;C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\170\Tools\Binn\;C:\Program Files\dotnet\;C:\Program Files (x86)\Microsoft SQL Server\160\Tools\Binn\;C:\Program Files\Microsoft SQL Server\160\Tools\Binn\;C:\Program Files\Microsoft SQL Server\160\DTS\Binn\;C:\Program Files (x86)\Microsoft SQL Server\160\DTS\Binn\;C:\Program Files\Azure Data Studio\bin;C:\Program Files\Git\cmd;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\.dotnet\tools;C:\Program Files\Azure Data Studio\bin;C:\flutter\bin;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin;;C:\flutter\bin\mingit\cmd;C:\flutter\bin\mingit\cmd
USERNAME=zaher
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 165 Stepping 5, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp


JNI global refs:
JNI global refs: 32, weak refs: 0

JNI global refs memory usage: 843, weak refs: 841

OOME stack traces (most recent first):
Classloader memory used:
Loader org.gradle.internal.classloader.VisitableURLClassLoader                         : 4251K
Loader bootstrap                                                                       : 2822K
Loader org.gradle.initialization.MixInLegacyTypesClassLoader                           : 1205K
Loader org.gradle.internal.classloader.VisitableURLClassLoader                         : 1126K
Loader jdk.internal.loader.ClassLoaders$PlatformClassLoader                            : 65385B
Loader jdk.internal.reflect.DelegatingClassLoader                                      : 33685B
Loader jdk.internal.loader.ClassLoaders$AppClassLoader                                 : 25681B
Loader org.gradle.groovy.scripts.internal.DefaultScriptCompilationHandler$ScriptClassLoader: 18758B
Loader org.codehaus.groovy.runtime.callsite.CallSiteClassLoader                        : 5625B
Loader sun.reflect.misc.MethodUtil                                                     : 373B

Classes loaded by more than one classloader:
Class Program                                                                         : loaded 3 times (x 70B)
Class com.google.common.collect.UsingToStringOrdering                                 : loaded 2 times (x 112B)
Class com.google.common.collect.Iterators$MergingIterator                             : loaded 2 times (x 79B)
Class org.objectweb.asm.ModuleVisitor                                                 : loaded 2 times (x 78B)
Class [Lcom.google.common.collect.AbstractMapEntry;                                   : loaded 2 times (x 67B)
Class com.google.common.collect.SingletonImmutableList                                : loaded 2 times (x 167B)
Class com.google.common.cache.CacheLoader$SupplierToCacheLoader                       : loaded 2 times (x 73B)
Class org.gradle.internal.classpath.ClassPath                                         : loaded 2 times (x 68B)
Class com.google.common.cache.RemovalListener                                         : loaded 2 times (x 68B)
Class org.gradle.api.internal.classpath.DefaultModuleRegistry                         : loaded 2 times (x 84B)
Class com.google.common.collect.ImmutableEnumSet                                      : loaded 2 times (x 146B)
Class com.google.common.collect.ListMultimap                                          : loaded 2 times (x 68B)
Class com.google.common.base.CharMatcher$JavaDigit                                    : loaded 2 times (x 109B)
Class com.google.common.base.CharMatcher$Digit                                        : loaded 2 times (x 110B)
Class com.google.common.collect.AbstractMultimap                                      : loaded 2 times (x 121B)
Class com.google.common.cache.CacheBuilder$OneWeigher                                 : loaded 2 times (x 80B)
Class org.gradle.api.Action                                                           : loaded 2 times (x 68B)
Class com.google.common.collect.ImmutableEntry                                        : loaded 2 times (x 80B)
Class com.google.common.collect.Lists$StringAsImmutableList                           : loaded 2 times (x 167B)
Class com.google.common.cache.LocalCache$StrongEntry                                  : loaded 2 times (x 106B)
Class org.objectweb.asm.FieldWriter                                                   : loaded 2 times (x 75B)
Class com.google.common.base.CharMatcher                                              : loaded 2 times (x 109B)
Class com.google.common.base.CharMatcher$IsNot                                        : loaded 2 times (x 109B)
Class com.google.common.base.Splitter                                                 : loaded 2 times (x 70B)
Class [Lcom.google.common.cache.Weigher;                                              : loaded 2 times (x 67B)
Class com.google.common.collect.Iterators$ArrayItr                                    : loaded 2 times (x 95B)
Class com.google.common.cache.LocalCache$Segment                                      : loaded 2 times (x 152B)
Class org.gradle.api.internal.DefaultClassPathProvider                                : loaded 2 times (x 74B)
Class org.gradle.internal.installation.GradleInstallation$1                           : loaded 2 times (x 73B)
Class com.google.common.cache.LocalCache$AbstractReferenceEntry                       : loaded 2 times (x 105B)
Class org.objectweb.asm.Type                                                          : loaded 2 times (x 70B)
Class com.google.common.util.concurrent.AbstractFuture$Failure                        : loaded 2 times (x 70B)
Class com.google.common.base.CharMatcher$BitSetMatcher                                : loaded 2 times (x 110B)
Class com.google.common.collect.ImmutableMap$IteratorBasedImmutableMap                : loaded 2 times (x 123B)
Class com.google.common.collect.ImmutableMap                                          : loaded 2 times (x 118B)
Class com.google.common.base.Converter                                                : loaded 2 times (x 88B)
Class com.google.common.base.Equivalence                                              : loaded 2 times (x 80B)
Class com.google.common.cache.LocalCache$EntryFactory$1                               : loaded 2 times (x 81B)
Class com.google.common.primitives.Ints                                               : loaded 2 times (x 69B)
Class com.google.common.cache.LocalCache$EntryFactory$2                               : loaded 2 times (x 81B)
Class com.google.common.cache.LocalCache$EntryFactory$3                               : loaded 2 times (x 81B)
Class com.google.common.cache.LocalCache$EntryFactory$4                               : loaded 2 times (x 81B)
Class com.google.common.cache.LocalCache$EntryFactory$5                               : loaded 2 times (x 81B)
Class com.google.common.cache.LocalCache$EntryFactory$6                               : loaded 2 times (x 81B)
Class com.google.common.cache.LocalCache$EntryFactory$7                               : loaded 2 times (x 81B)
Class com.google.common.cache.LocalCache$EntryFactory$8                               : loaded 2 times (x 81B)
Class com.google.common.base.Predicate                                                : loaded 2 times (x 68B)
Class com.google.common.cache.LocalCache$StrongValueReference                         : loaded 2 times (x 88B)
Class org.gradle.internal.classloader.FilteringClassLoader                            : loaded 2 times (x 102B)
Class com.google.common.collect.RegularImmutableSet                                   : loaded 2 times (x 146B)
Class [Lcom.google.common.cache.LocalCache$Strength;                                  : loaded 2 times (x 67B)
Class org.gradle.internal.classpath.DefaultClassPath$ImmutableUniqueList$Builder      : loaded 2 times (x 73B)
Class com.google.common.collect.ExplicitOrdering                                      : loaded 2 times (x 113B)
Class com.google.common.collect.Maps$8                                                : loaded 2 times (x 80B)
Class com.google.common.base.PatternCompiler                                          : loaded 2 times (x 68B)
Class [Lcom.google.common.cache.CacheBuilder$NullListener;                            : loaded 2 times (x 67B)
Class com.google.common.base.CharMatcher$InRange                                      : loaded 2 times (x 109B)
Class com.google.common.collect.Sets$SetView                                          : loaded 2 times (x 136B)
Class com.google.common.collect.BiMap                                                 : loaded 2 times (x 68B)
Class com.google.common.collect.Lists                                                 : loaded 2 times (x 69B)
Class org.objectweb.asm.AnnotationWriter                                              : loaded 2 times (x 76B)
Class com.google.common.math.IntMath$1                                                : loaded 2 times (x 69B)
Class org.gradle.internal.classloader.ClassLoaderVisitor                              : loaded 2 times (x 74B)
Class org.objectweb.asm.Label                                                         : loaded 2 times (x 71B)
Class com.google.common.cache.CacheBuilder$NullListener                               : loaded 2 times (x 80B)
Class com.google.common.math.MathPreconditions                                        : loaded 2 times (x 69B)
Class org.gradle.internal.service.DefaultServiceLocator                               : loaded 2 times (x 81B)
Class org.gradle.internal.service.UnknownServiceException                             : loaded 2 times (x 81B)
Class com.google.common.util.concurrent.AbstractFuture$SynchronizedHelper             : loaded 2 times (x 74B)
Class com.google.common.collect.ArrayListMultimap                                     : loaded 2 times (x 170B)
Class com.google.common.base.Strings                                                  : loaded 2 times (x 69B)
Class com.google.common.cache.CacheLoader$InvalidCacheLoadException                   : loaded 2 times (x 80B)
Class org.gradle.internal.classloader.DefaultClassLoaderFactory                       : loaded 2 times (x 80B)
Class com.google.common.collect.UnmodifiableIterator                                  : loaded 2 times (x 78B)
Class com.google.common.base.Stopwatch                                                : loaded 2 times (x 70B)
Class com.google.common.base.Platform$JdkPatternCompiler                              : loaded 2 times (x 73B)
Class com.google.common.cache.LocalCache$LoadingValueReference                        : loaded 2 times (x 94B)
Class com.google.common.base.CharMatcher$SingleWidth                                  : loaded 2 times (x 110B)
Class com.google.common.collect.Hashing                                               : loaded 2 times (x 69B)
Class com.google.common.collect.NullsFirstOrdering                                    : loaded 2 times (x 113B)
Class com.google.common.collect.NaturalOrdering                                       : loaded 2 times (x 113B)
Class com.google.common.base.JdkPattern                                               : loaded 2 times (x 73B)
Class com.google.common.collect.ByFunctionOrdering                                    : loaded 2 times (x 113B)
Class com.google.common.collect.Multimap                                              : loaded 2 times (x 68B)
Class com.google.common.base.FunctionalEquivalence                                    : loaded 2 times (x 81B)
Class org.objectweb.asm.AnnotationVisitor                                             : loaded 2 times (x 75B)
Class org.objectweb.asm.RecordComponentWriter                                         : loaded 2 times (x 76B)
Class com.google.common.cache.LocalCache$1                                            : loaded 2 times (x 87B)
Class com.google.common.cache.LocalCache$2                                            : loaded 2 times (x 140B)
Class org.gradle.internal.classloader.VisitableURLClassLoader$Spec                    : loaded 2 times (x 72B)
Class org.gradle.api.GradleException                                                  : loaded 2 times (x 80B)
Class com.google.common.base.CharMatcher$JavaLetterOrDigit                            : loaded 2 times (x 109B)
Class org.gradle.api.internal.classpath.ModuleRegistry                                : loaded 2 times (x 68B)
Class com.google.common.collect.CompoundOrdering                                      : loaded 2 times (x 113B)
Class com.google.common.cache.CacheBuilder                                            : loaded 2 times (x 70B)
Class org.objectweb.asm.ByteVector                                                    : loaded 2 times (x 76B)
Class com.google.common.collect.ImmutableCollection                                   : loaded 2 times (x 123B)
Class com.google.common.base.PairwiseEquivalence                                      : loaded 2 times (x 81B)
Class com.google.common.base.Ticker                                                   : loaded 2 times (x 70B)
Class org.gradle.api.internal.ClassPathProvider                                       : loaded 2 times (x 68B)
Class com.google.common.collect.RegularImmutableMap$Values                            : loaded 2 times (x 167B)
Class org.objectweb.asm.ModuleWriter                                                  : loaded 2 times (x 79B)
Class org.gradle.internal.classloader.ClasspathUtil$1                                 : loaded 2 times (x 74B)
Class com.google.common.collect.ImmutableEnumMap                                      : loaded 2 times (x 123B)
Class com.google.common.collect.ImmutableList$ReverseImmutableList                    : loaded 2 times (x 168B)
Class build_cbgkpd0obcy601vdg3ozkkrde                                                 : loaded 2 times (x 178B)
Class com.google.common.cache.AbstractCache$StatsCounter                              : loaded 2 times (x 68B)
Class org.objectweb.asm.FieldVisitor                                                  : loaded 2 times (x 74B)
Class org.objectweb.asm.Symbol                                                        : loaded 2 times (x 71B)
Class com.google.common.collect.Ordering                                              : loaded 2 times (x 112B)
Class com.google.common.cache.LocalCache$Strength$1                                   : loaded 2 times (x 79B)
Class com.google.common.cache.LocalCache$Strength$2                                   : loaded 2 times (x 79B)
Class com.google.common.cache.LocalCache$Strength$3                                   : loaded 2 times (x 79B)
Class org.gradle.internal.classloader.ClassLoaderFactory                              : loaded 2 times (x 68B)
Class com.google.common.collect.ObjectArrays                                          : loaded 2 times (x 69B)
Class com.google.common.util.concurrent.AbstractFuture$Waiter                         : loaded 2 times (x 70B)
Class com.google.common.util.concurrent.Uninterruptibles                              : loaded 2 times (x 69B)
Class com.google.common.collect.Iterators$10                                          : loaded 2 times (x 79B)
Class com.google.common.collect.ImmutableList                                         : loaded 2 times (x 166B)
Class org.gradle.api.internal.classpath.ManifestUtil                                  : loaded 2 times (x 69B)
Class org.gradle.api.specs.Spec                                                       : loaded 2 times (x 68B)
Class com.google.common.cache.CacheLoader$UnsupportedLoadingOperationException        : loaded 2 times (x 80B)
Class com.google.common.base.CharMatcher$Whitespace                                   : loaded 2 times (x 110B)
Class com.google.common.util.concurrent.ListenableFuture                              : loaded 2 times (x 68B)
Class com.google.common.collect.Iterators$1                                           : loaded 2 times (x 79B)
Class com.google.common.collect.Iterators$4                                           : loaded 2 times (x 80B)
Class com.google.common.collect.Iterators$5                                           : loaded 2 times (x 80B)
Class com.google.common.util.concurrent.AbstractFuture$UnsafeAtomicHelper             : loaded 2 times (x 74B)
Class com.google.common.base.Equivalence$Equals                                       : loaded 2 times (x 80B)
Class com.google.common.base.Preconditions                                            : loaded 2 times (x 69B)
Class com.google.common.base.Function                                                 : loaded 2 times (x 68B)
Class com.google.common.collect.Iterators$9                                           : loaded 2 times (x 79B)
Class org.gradle.internal.IoActions                                                   : loaded 2 times (x 69B)
Class settings_2gruv7bjn7ke34ukd7sgbaiha                                              : loaded 2 times (x 177B)
Class com.google.common.collect.AllEqualOrdering                                      : loaded 2 times (x 112B)
Class com.google.common.collect.ComparatorOrdering                                    : loaded 2 times (x 113B)
Class com.google.common.cache.ReferenceEntry                                          : loaded 2 times (x 68B)
Class com.google.common.collect.RegularImmutableMap$KeySet                            : loaded 2 times (x 148B)
Class com.google.common.collect.CollectPreconditions                                  : loaded 2 times (x 69B)
Class com.google.common.primitives.IntsMethodsForWeb                                  : loaded 2 times (x 69B)
Class com.google.common.collect.LexicographicalOrdering                               : loaded 2 times (x 113B)
Class com.google.common.collect.Maps                                                  : loaded 2 times (x 69B)
Class com.google.common.collect.RegularImmutableMap                                   : loaded 2 times (x 119B)
Class com.google.common.collect.AbstractIndexedListIterator                           : loaded 2 times (x 94B)
Class com.google.common.base.CharMatcher$None                                         : loaded 2 times (x 110B)
Class org.gradle.api.internal.classpath.EffectiveClassPath                            : loaded 2 times (x 88B)
Class com.google.common.collect.UnmodifiableListIterator                              : loaded 2 times (x 93B)
Class com.google.common.cache.CacheLoader$FunctionToCacheLoader                       : loaded 2 times (x 73B)
Class com.google.common.cache.CacheBuilder$1                                          : loaded 2 times (x 83B)
Class com.google.common.cache.CacheBuilder$2                                          : loaded 2 times (x 77B)
Class com.google.common.collect.ImmutableList$1                                       : loaded 2 times (x 95B)
Class com.google.common.base.Splitter$Strategy                                        : loaded 2 times (x 68B)
Class com.google.common.cache.CacheBuilder$3                                          : loaded 2 times (x 70B)
Class com.google.common.collect.ImmutableMapEntrySet$RegularEntrySet                  : loaded 2 times (x 149B)
Class [Lcom.google.common.cache.RemovalListener;                                      : loaded 2 times (x 67B)
Class [Lcom.google.common.collect.ImmutableMapEntry;                                  : loaded 2 times (x 67B)
Class org.gradle.internal.installation.CurrentGradleInstallation                      : loaded 2 times (x 71B)
Class org.gradle.internal.installation.CurrentGradleInstallationLocator               : loaded 2 times (x 69B)
Class [Lcom.google.common.cache.LocalCache$Segment;                                   : loaded 2 times (x 67B)
Class org.gradle.api.internal.classpath.Module                                        : loaded 2 times (x 68B)
Class com.google.common.base.Splitter$1$1                                             : loaded 2 times (x 84B)
Class com.google.common.collect.ImmutableSet$JdkBackedSetBuilderImpl                  : loaded 2 times (x 74B)
Class com.google.common.collect.ImmutableSet$RegularSetBuilderImpl                    : loaded 2 times (x 75B)
Class [Lorg.objectweb.asm.AnnotationWriter;                                           : loaded 2 times (x 67B)
Class org.gradle.internal.service.CachingServiceLocator                               : loaded 2 times (x 80B)
Class [Lcom.google.common.collect.ImmutableEntry;                                     : loaded 2 times (x 67B)
Class org.gradle.internal.classpath.DefaultClassPath                                  : loaded 2 times (x 88B)
Class org.gradle.api.Transformer                                                      : loaded 2 times (x 68B)
Class com.google.common.util.concurrent.AbstractFuture$SetFuture                      : loaded 2 times (x 73B)
Class com.google.common.base.Splitter$SplittingIterator                               : loaded 2 times (x 82B)
Class com.google.common.cache.LocalCache$LocalManualCache$1                           : loaded 2 times (x 73B)
Class org.objectweb.asm.SymbolTable$Entry                                             : loaded 2 times (x 72B)
Class [Lcom.google.common.cache.LocalCache$EntryFactory;                              : loaded 2 times (x 67B)
Class com.google.common.base.CharMatcher$Is                                           : loaded 2 times (x 109B)
Class com.google.common.base.Platform                                                 : loaded 2 times (x 69B)
Class com.google.common.collect.RegularImmutableAsList                                : loaded 2 times (x 176B)
Class com.google.common.collect.PeekingIterator                                       : loaded 2 times (x 68B)
Class com.google.common.collect.ImmutableMapEntrySet                                  : loaded 2 times (x 149B)
Class com.google.common.cache.CacheLoader                                             : loaded 2 times (x 72B)
Class com.google.common.collect.ImmutableBiMapFauxverideShim                          : loaded 2 times (x 118B)
Class org.objectweb.asm.MethodTooLargeException                                       : loaded 2 times (x 81B)
Class com.google.common.cache.Cache                                                   : loaded 2 times (x 68B)
Class org.gradle.internal.classloader.SystemClassLoaderSpec                           : loaded 2 times (x 69B)
Class com.google.common.util.concurrent.internal.InternalFutureFailureAccess          : loaded 2 times (x 70B)
Class com.google.common.base.Charsets                                                 : loaded 2 times (x 69B)
Class com.google.common.primitives.Ints$IntConverter                                  : loaded 2 times (x 88B)
Class com.google.common.collect.SingletonImmutableSet                                 : loaded 2 times (x 146B)
Class [Lcom.google.common.base.AbstractIterator$State;                                : loaded 2 times (x 67B)
Class com.google.common.collect.ImmutableMap$Builder                                  : loaded 2 times (x 78B)
Class com.google.common.base.AbstractIterator                                         : loaded 2 times (x 78B)
Class org.objectweb.asm.ClassWriter                                                   : loaded 2 times (x 102B)
Class com.google.common.base.AbstractIterator$1                                       : loaded 2 times (x 69B)
Class [Lcom.google.common.cache.CacheBuilder$OneWeigher;                              : loaded 2 times (x 67B)
Class com.google.common.collect.Iterators                                             : loaded 2 times (x 69B)
Class com.google.common.base.CharMatcher$1                                            : loaded 2 times (x 111B)
Class com.google.common.base.CharMatcher$Ascii                                        : loaded 2 times (x 110B)
Class org.gradle.api.UncheckedIOException                                             : loaded 2 times (x 80B)
Class com.google.common.base.CharMatcher$And                                          : loaded 2 times (x 110B)
Class com.google.common.collect.IndexedImmutableSet                                   : loaded 2 times (x 148B)
Class com.google.common.collect.IndexedImmutableSet$1                                 : loaded 2 times (x 172B)
Class com.google.common.collect.AbstractListMultimap                                  : loaded 2 times (x 170B)
Class com.google.common.base.CharMatcher$Any                                          : loaded 2 times (x 110B)
Class org.gradle.internal.installation.GradleInstallation                             : loaded 2 times (x 73B)
Class com.google.common.cache.LocalCache$Strength                                     : loaded 2 times (x 79B)
Class com.google.common.collect.ArrayListMultimapGwtSerializationDependencies         : loaded 2 times (x 170B)
Class com.google.common.base.CharMatcher$RangesMatcher                                : loaded 2 times (x 110B)
Class org.objectweb.asm.Handler                                                       : loaded 2 times (x 70B)
Class com.google.common.collect.ImmutableList$SubList                                 : loaded 2 times (x 168B)
Class com.google.common.cache.LocalCache$ValueReference                               : loaded 2 times (x 68B)
Class org.gradle.internal.classloader.ClasspathUtil                                   : loaded 2 times (x 69B)
Class org.objectweb.asm.CurrentFrame                                                  : loaded 2 times (x 71B)
Class com.google.common.util.concurrent.AbstractFuture                                : loaded 2 times (x 93B)
Class com.google.common.base.Splitter$1                                               : loaded 2 times (x 75B)
Class com.google.common.collect.ReverseOrdering                                       : loaded 2 times (x 113B)
Class com.google.common.base.Ticker$1                                                 : loaded 2 times (x 70B)
Class com.google.common.collect.Maps$BiMapConverter                                   : loaded 2 times (x 88B)
Class org.gradle.api.internal.DefaultClassPathRegistry                                : loaded 2 times (x 74B)
Class com.google.common.util.concurrent.AbstractFuture$Cancellation                   : loaded 2 times (x 70B)
Class [Lorg.objectweb.asm.Symbol;                                                     : loaded 2 times (x 67B)
Class com.google.common.collect.ImmutableSet$SetBuilderImpl                           : loaded 2 times (x 74B)
Class org.gradle.api.internal.classpath.DefaultModuleRegistry$DefaultModule           : loaded 2 times (x 84B)
Class com.google.common.base.CharMatcher$JavaIsoControl                               : loaded 2 times (x 110B)
Class com.google.common.collect.ImmutableMap$1                                        : loaded 2 times (x 79B)
Class com.google.common.base.CharMatcher$Or                                           : loaded 2 times (x 110B)
Class com.google.common.base.Suppliers$SupplierOfInstance                             : loaded 2 times (x 77B)
Class org.objectweb.asm.RecordComponentVisitor                                        : loaded 2 times (x 75B)
Class com.google.common.collect.Iterables                                             : loaded 2 times (x 69B)
Class com.google.common.base.CharMatcher$JavaLowerCase                                : loaded 2 times (x 109B)
Class org.objectweb.asm.ClassTooLargeException                                        : loaded 2 times (x 81B)
Class org.gradle.api.internal.classpath.UnknownModuleException                        : loaded 2 times (x 80B)
Class com.google.common.util.concurrent.AbstractFuture$Listener                       : loaded 2 times (x 70B)
Class org.objectweb.asm.Edge                                                          : loaded 2 times (x 70B)
Class com.google.common.collect.Maps$EntryTransformer                                 : loaded 2 times (x 68B)
Class com.google.common.collect.ImmutableCollection$Builder                           : loaded 2 times (x 74B)
Class [Lorg.objectweb.asm.SymbolTable$Entry;                                          : loaded 2 times (x 67B)
Class com.google.common.base.CommonPattern                                            : loaded 2 times (x 72B)
Class com.google.common.base.Suppliers                                                : loaded 2 times (x 69B)
Class org.objectweb.asm.ClassVisitor                                                  : loaded 2 times (x 85B)
Class com.google.common.cache.LoadingCache                                            : loaded 2 times (x 68B)
Class org.gradle.internal.service.ServiceLookupException                              : loaded 2 times (x 80B)
Class org.gradle.cache.GlobalCache                                                    : loaded 2 times (x 68B)
Class com.google.common.base.CharMatcher$NegatedFastMatcher                           : loaded 2 times (x 111B)
Class [Lorg.gradle.api.internal.ClassPathProvider;                                    : loaded 2 times (x 67B)
Class com.google.common.util.concurrent.AbstractFuture$SafeAtomicHelper               : loaded 2 times (x 75B)
Class org.gradle.util.internal.GUtil                                                  : loaded 2 times (x 69B)
Class com.google.common.math.IntMath                                                  : loaded 2 times (x 69B)
Class com.google.common.collect.AbstractIterator                                      : loaded 2 times (x 80B)
Class org.gradle.internal.classloader.ClassLoaderSpec                                 : loaded 2 times (x 69B)
Class org.objectweb.asm.Frame                                                         : loaded 2 times (x 71B)
Class com.google.common.cache.LocalCache$LocalManualCache                             : loaded 2 times (x 97B)
Class com.google.common.collect.AbstractMapEntry                                      : loaded 2 times (x 79B)
Class com.google.common.collect.ImmutableList$Builder                                 : loaded 2 times (x 75B)
Class com.google.common.base.CharMatcher$Negated                                      : loaded 2 times (x 111B)
Class com.google.common.cache.CacheLoader$1                                           : loaded 2 times (x 73B)
Class com.google.common.util.concurrent.AbstractFuture$TrustedFuture                  : loaded 2 times (x 95B)
Class com.google.common.collect.Sets                                                  : loaded 2 times (x 69B)
Class com.google.common.collect.ImmutableSet$Builder                                  : loaded 2 times (x 83B)
Class com.google.common.base.CharMatcher$ForPredicate                                 : loaded 2 times (x 110B)
Class com.google.common.collect.ImmutableMap$MapViewOfValuesAsSingletonSets           : loaded 2 times (x 123B)
Class com.google.common.base.MoreObjects                                              : loaded 2 times (x 69B)
Class com.google.common.collect.SortedMapDifference                                   : loaded 2 times (x 68B)
Class org.objectweb.asm.SymbolTable                                                   : loaded 2 times (x 70B)
Class [Lorg.objectweb.asm.AnnotationVisitor;                                          : loaded 2 times (x 67B)
Class com.google.common.cache.CacheStats                                              : loaded 2 times (x 69B)
Class org.objectweb.asm.Attribute                                                     : loaded 2 times (x 75B)
Class org.gradle.internal.classloader.VisitableURLClassLoader                         : loaded 2 times (x 114B)
Class com.google.common.cache.LocalCache$LocalLoadingCache                            : loaded 2 times (x 132B)
Class com.google.common.base.Supplier                                                 : loaded 2 times (x 68B)
Class com.google.common.util.concurrent.AbstractFuture$AtomicHelper                   : loaded 2 times (x 74B)
Class com.google.common.collect.ImmutableBiMap                                        : loaded 2 times (x 142B)
Class org.gradle.internal.Cast                                                        : loaded 2 times (x 69B)
Class Build_gradle                                                                    : loaded 2 times (x 128B)
Class org.gradle.api.internal.ClassPathRegistry                                       : loaded 2 times (x 68B)
Class com.google.common.cache.LocalCache$EntryFactory                                 : loaded 2 times (x 81B)
Class com.google.common.util.concurrent.UncheckedExecutionException                   : loaded 2 times (x 80B)
Class com.google.common.collect.ImmutableSet                                          : loaded 2 times (x 145B)
Class org.gradle.internal.classloader.ClassLoaderHierarchy                            : loaded 2 times (x 68B)
Class [Lorg.objectweb.asm.Type;                                                       : loaded 2 times (x 67B)
Class com.google.common.base.AbstractIterator$State                                   : loaded 2 times (x 77B)
Class com.google.common.cache.Weigher                                                 : loaded 2 times (x 68B)
Class com.google.common.base.CharMatcher$NamedFastMatcher                             : loaded 2 times (x 110B)
Class org.gradle.internal.service.ServiceLocator                                      : loaded 2 times (x 68B)
Class com.google.common.util.concurrent.SettableFuture                                : loaded 2 times (x 95B)
Class com.google.common.collect.ImmutableMapEntry                                     : loaded 2 times (x 83B)
Class com.google.common.base.CharMatcher$Invisible                                    : loaded 2 times (x 110B)
Class com.google.common.base.CharMatcher$FastMatcher                                  : loaded 2 times (x 109B)
Class org.gradle.internal.classpath.DefaultClassPath$ImmutableUniqueList              : loaded 2 times (x 159B)
Class com.google.common.base.CharMatcher$JavaLetter                                   : loaded 2 times (x 109B)
Class com.google.common.collect.MapDifference                                         : loaded 2 times (x 68B)
Class com.google.common.collect.Sets$1                                                : loaded 2 times (x 137B)
Class com.google.common.collect.Sets$2                                                : loaded 2 times (x 137B)
Class com.google.common.util.concurrent.AbstractFuture$Trusted                        : loaded 2 times (x 68B)
Class com.google.common.collect.Sets$3                                                : loaded 2 times (x 137B)
Class com.google.common.collect.Sets$4                                                : loaded 2 times (x 137B)
Class org.objectweb.asm.MethodWriter                                                  : loaded 2 times (x 103B)
Class com.google.common.collect.Platform                                              : loaded 2 times (x 69B)
Class com.google.common.collect.ImmutableAsList                                       : loaded 2 times (x 169B)
Class com.google.common.util.concurrent.ExecutionError                                : loaded 2 times (x 80B)
Class com.google.common.base.Equivalence$Identity                                     : loaded 2 times (x 80B)
Class com.google.common.base.CharMatcher$AnyOf                                        : loaded 2 times (x 110B)
Class com.google.common.base.CharMatcher$IsEither                                     : loaded 2 times (x 109B)
Class com.google.common.cache.LocalCache                                              : loaded 2 times (x 185B)
Class com.google.common.collect.RegularImmutableList                                  : loaded 2 times (x 172B)
Class com.google.common.util.concurrent.AbstractFuture$UnsafeAtomicHelper$1           : loaded 2 times (x 74B)
Class com.google.common.base.CharMatcher$JavaUpperCase                                : loaded 2 times (x 109B)
Class com.google.common.collect.Multiset                                              : loaded 2 times (x 68B)
Class build_cbgkpd0obcy601vdg3ozkkrde$_run_closure1                                   : loaded 2 times (x 134B)
Class org.objectweb.asm.MethodVisitor                                                 : loaded 2 times (x 102B)
Class com.google.common.collect.NullsLastOrdering                                     : loaded 2 times (x 113B)
Class com.google.common.collect.ImmutableMapEntry$NonTerminalImmutableMapEntry        : loaded 2 times (x 83B)
Class com.google.common.collect.AbstractMapBasedMultimap                              : loaded 2 times (x 137B)


---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 26100 (10.0.26100.3775)
OS uptime: 9 days 4:14 hours
Hyper-V role detected

CPU: total 16 (initial active 16) (8 cores per cpu, 2 threads per core) family 6 model 165 stepping 5 microcode 0xe0, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, fma, vzeroupper, clflush, clflushopt, hv

Memory: 4k page, system-wide physical 16195M (862M free)
TotalPageFile size 64338M (AvailPageFile size 24M)
current process WorkingSet (physical memory assigned to process): 350M, peak: 350M
current process commit charge ("private bytes"): 410M, peak: 437M

vm_info: OpenJDK 64-Bit Server VM (17.0.7+0-b2043.56-10550314) for windows-amd64 JRE (17.0.7+0-b2043.56-10550314), built on Jul 24 2023 18:27:45 by "androidbuild" with MS VC++ 16.10 / 16.11 (VS2019)

END.
