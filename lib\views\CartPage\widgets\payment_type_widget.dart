import 'package:alderishop/constants/constants.dart';
import 'package:alderishop/data/model/order/order_model.dart';
import 'package:alderishop/services/helper.dart';
import 'package:flutter/material.dart';

class PaymentTypeWidget extends StatefulWidget {
  final Function(int selectedPaymentTypeVal)? selectedPaymentTypeVal;
  const PaymentTypeWidget({super.key, this.selectedPaymentTypeVal});

  @override
  State<PaymentTypeWidget> createState() => _PaymentTypeWidgetState();
}

class _PaymentTypeWidgetState extends State<PaymentTypeWidget> {
  PaymentType? selectedPaymentType;
  @override
  void initState() {
    super.initState();

    selectedPaymentType = PaymentType.values.first;
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 10),
      child: Column(
        children: [
          const SizedBox(
            height: 10,
          ),
          Row(
            children: [
              Text(
                T("Payment Method"),
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: AppColors.PRIMARY_COLOR,
                ),
              ),
            ],
          ),
          const SizedBox(
            height: 10,
          ),
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: AppColors.BLACK_GREY,
              ),
            ),
            child: Column(
              children: PaymentType.values
                  .expand((payment) => [
                        RadioListTile<PaymentType>(
                          title: Row(
                            children: [
                              Icon(
                                payment == PaymentType.OnDoor
                                    ? Icons.home
                                    : Icons.credit_card,
                                color: AppColors.PRIMARY_COLOR,
                              ),
                              const SizedBox(width: 8),
                              Text(
                                payment == PaymentType.OnDoor
                                    ? T("OnDoor")
                                    : T("Electronic Payment"),
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.PRIMARY_COLOR,
                                ),
                              ),
                            ],
                          ),
                          value: payment,
                          groupValue: selectedPaymentType,
                          onChanged: (PaymentType? value) {
                            setState(() {
                              selectedPaymentType = value;
                            });
                            if (widget.selectedPaymentTypeVal != null) {
                              widget.selectedPaymentTypeVal!(
                                  selectedPaymentType!.index);
                            }
                          },
                        ),
                        if (payment != PaymentType.values.last)
                          Divider(
                            color: AppColors.PRIMARY_COLOR.withOpacity(0.2),
                            thickness: 1,
                            height: 0,
                          ),
                      ])
                  .toList(),
            ),
          )
        ],
      ),
    );
  }
}
