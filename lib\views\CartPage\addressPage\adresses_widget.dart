import 'package:alderishop/controllers/app_controller.dart';
import 'package:alderishop/views/CartPage/addressPage/widget/selected_address_widget.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:alderishop/controllers/addresses_controller.dart';
import 'package:alderishop/views/CartPage/addressPage/widget/address_list_items_widget.dart';
import 'package:alderishop/constants/constants.dart';

class MyAdressesWidget extends StatefulWidget {
  final bool showText;
  final String? txt;
  final String? txt2;
  final bool isAccountPage;
  final double height
  ;
  final Axis axisDirection;
  final Function(
    int,
  )? onAddressSelected;

  const MyAdressesWidget({
    Key? key,
    required this.axisDirection,
    required this.height,
    required this.showText,
    this.txt,
    required this.isAccountPage,
    this.txt2,
    this.onAddressSelected,
  }) : super(key: key);

  @override
  State<MyAdressesWidget> createState() => _MyAdressesWidgetState();
}

class _MyAdressesWidgetState extends State<MyAdressesWidget> {
  int? selectedAddressId;

  @override
  void initState() {
    super.initState();

    final data =
        Provider.of<AddressesController>(context, listen: false).addresses;
    if (data.isNotEmpty && selectedAddressId == null) {
      selectedAddressId = data[0].id;
    }
  }

  @override
  Widget build(BuildContext context) {
    final data = Provider.of<AddressesController>(context).addresses;

    return SingleChildScrollView(

      child: Padding(
          padding:const EdgeInsets.symmetric(horizontal: 15,vertical: 0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,

          children: [
           
            FutureBuilder(
              future: null,
              builder: (context, snapshot) {
                if (widget.isAccountPage) {
                  return AddressListWidgetForSelect(data: data);
                } else {
                  return SizedBox(
                    height:widget.height,
                    width: AppController.W,
                    child: ListView.builder(
                      shrinkWrap: false,
                      physics: const NeverScrollableScrollPhysics(),
                      scrollDirection: widget.axisDirection,
                      itemCount: data.length,
                      itemBuilder: (context, index) {
                        final isSelected = selectedAddressId == data[index].id;
                        return 
                        InkWell(
                          onTap: () {
                            setState(() {
                              selectedAddressId = data[index].id;
                            });
                            if (widget.onAddressSelected != null) {
                              widget.onAddressSelected!(
                                data[index].id ?? 0,
                              );
                            }
                          },
                          child: AddressListItemsWidget(
                            model: data[index],
                            isSelected: isSelected,
                            borderColor: isSelected
                                ? AppColors.PRIMARY_COLOR
                                : Colors.grey.shade300,
                            onChange: (id) {
                              setState(() {
                                selectedAddressId = id;
                              });
                            },
                          ),
                        );
                      },
                    ),
                  );
                }
              },
            ),
          ],
       
        ),
      ),
    );
  }
}
