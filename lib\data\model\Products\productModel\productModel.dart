import 'package:alderishop/data/model/Products/productModel/productListModel.dart';

class ProductModel {
  int? id;
  String? name;
  String? shortDescription;
  String? fullDescription;
  double? price;
  double? discountPrice;
  double? profitPointOnBuy;
  int? sku;
  int? quantity;
  List<Images>? images;
  String? imageUrl;
  bool? popularProduct;
  int? productRating;
  bool? isActive;
  bool? isWishListAdded;
  int? erpProductId;
  int? erpStoreId;
  int? erpUnitId;
  int? erpCategoryId;
  
  List<int>? categoryIds;
  List<String>? categories;
  List<ProductAttribute>? productAttribute;
  List<ProductListModel>? relatedProducts;

  ProductModel({
    this.id,
    this.name,
    this.shortDescription,
    this.fullDescription,
    this.price,
    this.profitPointOnBuy,
    this.discountPrice,
    this.sku,
    this.quantity,
    this.images,
    this.imageUrl,
    this.popularProduct,
    this.productRating,
    this.isActive,
    this.isWishListAdded,
    this.erpProductId,
    this.erpStoreId,
    this.erpUnitId,
    this.erpCategoryId,
    this.categoryIds,
    this.categories,
    this.productAttribute,
    this.relatedProducts,
  });

  ProductModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    shortDescription = json['shortDescription'];
    fullDescription = json['fullDescription'];
    price = json['price'];
    discountPrice = json['discountPrice'];
    profitPointOnBuy = json['profitPointOnBuy'];
    sku = json['sku'];
    quantity = json['quantity'];

    if (json['images'] != null) {
      images = List<Images>.from(json['images'].map((v) => Images.fromJson(v)));
    }

    imageUrl = json['imageUrl'];
    popularProduct = json['popularProduct'];
    productRating = json['productRating'];
    isActive = json['isActive'];

    isWishListAdded = json['isWishListAdded'];
    erpProductId = json['erpProductId'];
    erpStoreId = json['erpStoreId'];
    erpUnitId = json['erpUnitId'];
    erpCategoryId = json['erpCategoryId'];

    categoryIds = json['categoryIds'] != null
        ? List<int>.from(json['categoryIds'])
        : null;
    categories = json['categories'] != null
        ? List<String>.from(json['categories'].map((v) => v['name'].toString()))
        : null;

    if (json['productAttribute'] != null) {
      productAttribute = List<ProductAttribute>.from(
          json['productAttribute'].map((v) => ProductAttribute.fromJson(v)));
    }

    if (json['relatedProducts'] != null) {
      relatedProducts = List<ProductListModel>.from(
          json['relatedProducts'].map((v) => ProductListModel.fromJson(v)));
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['id'] = id;
    data['name'] = name;
    data['shortDescription'] = shortDescription;
    data['fullDescription'] = fullDescription;
    data['price'] = price;
    data['discountPrice'] = discountPrice;
    data['sku'] = sku;
    data['profitPointOnBuy'] = this.profitPointOnBuy;
    data['quantity'] = quantity;
    if (images != null) {
      data['images'] = images!.map((v) => v.toJson()).toList();
    }
    data['imageUrl'] = imageUrl;
    data['popularProduct'] = popularProduct;
    data['productRating'] = productRating;
    data['isActive'] = isActive;
    data['isWishListAdded'] = isWishListAdded;
    data['erpProductId'] = erpProductId;
    data['erpStoreId'] = erpStoreId;
    data['erpUnitId'] = erpUnitId;
    data['erpCategoryId'] = erpCategoryId;
    data['categoryIds'] = categoryIds;
    data['categories'] = categories;
    if (productAttribute != null) {
      data['productAttribute'] =
          productAttribute!.map((v) => v.toJson()).toList();
    }
    if (relatedProducts != null) {
      data['relatedProducts'] =
          relatedProducts!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class ProductAttribute {
  int? productId;
  int? attributeTypeId;
  int? controlType;
  List<Options>? options;
  int? id;
  int? order;
  String? name;
  String? controlTypeString;

  ProductAttribute(
      {this.productId,
      this.attributeTypeId,
      this.controlType,
      this.options,
      this.order,
      this.id,
      this.name,
      this.controlTypeString});

  ProductAttribute.fromJson(Map<String, dynamic> json) {
    productId = json['productId'];
    attributeTypeId = json['attributeTypeId'];
    controlType = json['controlType'];
    options = json['options'] != null
        ? List<Options>.from(json['options'].map((v) => Options.fromJson(v)))
        : null;
    order = json['order'];
    id = json['id'];
    name = json['name'];
    controlTypeString = json['controlTypeString'];
  }

  Map<String, dynamic> toJson() => {
        'productId': productId,
        'attributeTypeId': attributeTypeId,
        'controlType': controlType,
        'options': options?.map((v) => v.toJson()).toList(),
        'id': id,
        'order': order,
        'name': name,
        'controlTypeString': controlTypeString,
      };
}

class Options {
  bool? status;
  bool? isSelected;
  int? id;
  int? productAttributeId;
  String? name;
  String? value;
  double? newPrice;
  String? image;

  Options(
      {this.status,
      this.isSelected,
      this.id,
      this.productAttributeId,
      this.name,
      this.value,
      this.newPrice,
      this.image});

  Options.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    isSelected = json['isSelected'];
    id = json['id'];
    productAttributeId = json['productAttributeId'];
    name = json['name'];
    value = json['value'];
    newPrice = json['newPrice'];
    image = json['image'];
  }

  Map<String, dynamic> toJson() => {
        'status': status,
        'isSelected': isSelected,
        'id': id,
        'productAttributeId': productAttributeId,
        'name': name,
        'value': value,
        'newPrice': newPrice,
      };
}
