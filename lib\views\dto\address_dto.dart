class AddressesDto {
  String? title;
  String? fullName;
  String? firstName;
  String? lastName;
  String? email;
  int? countryId;

  bool? isActive;
  String? address1;
  String? address2;
  String? phoneNumber;

  AddressesDto({
    this.title,
    this.fullName,
    this.firstName,
    this.lastName,
    this.email,
    this.countryId,
    this.isActive,
    this.address1,
    this.address2,
    this.phoneNumber,
  });

  AddressesDto.fromJson(Map<String, dynamic> json) {
    title = json['Title'];
    fullName = json['FullName'];
    firstName = json['FirstName'];
    lastName = json['LastName'];
    email = json['Email'];

    countryId = json['CountryId'];

    isActive = json['IsActive'];
    address1 = json['Address1'];
    address2 = json['Address2'];

    phoneNumber = json['PhoneNumber'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();

    data['Title'] = this.title;
    data['FullName'] = this.fullName;
    data['FirstName'] = this.firstName;
    data['LastName'] = this.lastName;
    data['Email'] = this.email;

    data['CountryId'] = this.countryId;

    data['IsActive'] = this.isActive;
    data['Address1'] = this.address1;
    data['Address2'] = this.address2;

    data['PhoneNumber'] = this.phoneNumber;

    return data;
  }
}
