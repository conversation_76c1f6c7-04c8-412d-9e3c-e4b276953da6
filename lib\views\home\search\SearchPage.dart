import 'package:alderishop/components/layout.dart';
import 'package:alderishop/controllers/app_controller.dart';
import 'package:alderishop/controllers/categores_controller.dart';
import 'package:alderishop/controllers/products_controller.dart';
import 'package:alderishop/data/model/categories/categroy_model.dart';
import 'package:alderishop/data/model/search/search_model.dart';
import 'package:alderishop/services/helper.dart';
import 'package:alderishop/views/profile/widgets/customContanier.dart';
import 'package:alderishop/views/categories/widgets/Header.dart';
import 'package:alderishop/views/home/<USER>/search_result_screen.dart';
import 'package:alderishop/views/home/<USER>/widget/MultiSelectChip.dart';
import 'package:alderishop/views/home/<USER>/widget/search.dart';
import 'package:flutter/material.dart';
import 'package:alderishop/constants/constants.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

// ignore: must_be_immutable
class SearchPage extends StatefulWidget {
  const SearchPage({super.key});

  @override

  // ignore: library_private_types_in_public_api
  _SearchPageState createState() => _SearchPageState();
}

class _SearchPageState extends State<SearchPage> {
  String search = '';
  List<String> searchHistory = [];

  List<AttrFilter> selectedAttrFilters = [];

  @override
  void initState() {
    super.initState();

    _loadSearchHistory();
  }

  void _loadSearchHistory() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    setState(() {
      searchHistory = prefs.getStringList('searchHistory') ?? [];
    });
  }

 void _handleSearch(String searchText) {
  setState(() {
    search = searchText;
  });

  final controller = Provider.of<ProductsController>(context, listen: false);
  controller.searchFilter.search = searchText;
  controller.getProducts(); // Or getFilteredProducts()
}


  Map<String, String> selectedAttributes = {};
  @override
  Widget build(BuildContext context) {
    var filter =
        Provider.of<ProductsController>(context, listen: false).searchFilter;
    final dataProvider = Provider.of<CategoryController>(context);
    final categories = dataProvider.category;
    final providerdata = Provider.of<ProductsController>(context);
    final attr = providerdata.attributes;
    List<CategoryModel> subCategories = [];

    return ApplicationLayout(
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CategoriesHeader(text: T('Search')),
              Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                child: Column(
                  children: [
                    //-------------------Search ------------------------------------
                    Search(onSearchSubmitted: _handleSearch),
                    //-------------------Price ------------------------------------
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 5),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          //----------------------------Categories----------------------------------------
                          Text(
                            T("Categories"),
                            style: TextStyle(
                              fontSize: 15,
                              color: AppColors.BLACK_COLOR,
                              fontWeight: FontWeight.bold,
                            ),
                          ),

                          SelectCategoryForFilter(
                            items: categories.toList(),
                          onSelectionChanged: (selectedCategory) async {
  filter.categoryId = selectedCategory?.id ?? 0;

  // fetch attributes
  await Provider.of<ProductsController>(context, listen: false)
      .getAttributesWithOptionsCategoryById(
    categoryId: selectedCategory?.id ?? 0,
  );

  // Clear previous filters
  filter.attrFilter?.clear();

  // Load subcategories (assuming your CategoryModel has a `parentId` or similar)
  final allCategories = Provider.of<CategoryController>(context, listen: false).category;

  setState(() {
    subCategories = allCategories
        .where((cat) => cat.parentId == selectedCategory?.id)
        .toList();
  });
},

                          ),

                          const SizedBox(height: 10),
                          //----------------------------Size----------------------------------------
                          if (attr.isNotEmpty)
                            ...attr.map((entry) {
                              String attributeName = entry.attributeName ?? "";

                              return Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    attributeName,
                                    style: TextStyle(
                                      fontSize: 15,
                                      color: AppColors.BLACK_COLOR,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  const SizedBox(height: 8),
                                  MultiSelectChip(
                                    atterName: entry.attributeName ?? "",
                                    items: entry.options ?? [],
                                    onSelectionChanged: (selectedFilters) {
                                      if (filter.attrFilter?.any((e) =>
                                              e.optionId ==
                                              selectedFilters.optionId) ??
                                          false) {
                                        filter.attrFilter?.removeWhere((w) =>
                                            w.optionId ==
                                            selectedFilters.optionId);
                                      } else {
                                        filter.attrFilter?.add(selectedFilters);
                                      }
                                    },
                                  ),
                                  const SizedBox(height: 16),
                                ],
                              );
                            }).toList(),

                          //--------------------------------------------------------------------
                          const SizedBox(height: 10),
                          InkWell(
                            onTap: () async {
                              final provider = Provider.of<ProductsController>(
                                  context,
                                  listen: false);
                              final categoryProvider =
                                  Provider.of<ProductsController>(context,
                                      listen: false);

                              int? selectedCategoryId =
                                  categoryProvider.selectedCategoryId;

                              setState(() {
                                selectedAttrFilters.clear();
                              });
                              provider.getProductCategories(
                                  resetAndRefresh: false,
                                  categoryId:
                                      provider.searchFilter.categoryId ?? 0);

                              Navigator.of(context).pushReplacement(
                                MaterialPageRoute(
                                  builder: (context) => SearchResultScreen(
                                    searchFilter: ProductSearchFilter(
                                        search: search,
                                        pageNumber: 1,
                                        pageSize: 10,
                                        categoryId: selectedCategoryId,
                                        attrFilter: filter.attrFilter),
                                  ),
                                ),
                              );
                            },
                            child: CustomContainer(
                              color: AppColors.brown,
                              text: T("Search"),
                              width: AppController.W,
                            ),
                          ),
                        ],
                      ),
                    )
                  ],
                ),
              )
            ],
          ),
        ),
        selectedBottomNavbarItem: BottomNavbarItems.none);
  }
}
