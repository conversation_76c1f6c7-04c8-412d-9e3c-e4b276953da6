import 'package:alderishop/components/layout.dart';
import 'package:alderishop/constants/constants.dart';
import 'package:alderishop/controllers/app_controller.dart';
import 'package:alderishop/controllers/categores_controller.dart';
import 'package:alderishop/controllers/products_controller.dart';
import 'package:alderishop/data/model/Products/attributesDto.dart';
import 'package:alderishop/data/model/categories/categroy_model.dart';
import 'package:alderishop/data/model/search/search_model.dart';
import 'package:alderishop/services/helper.dart';
import 'package:alderishop/views/profile/widgets/customContanier.dart';
import 'package:alderishop/views/categories/widgets/Header.dart';
import 'package:alderishop/views/home/<USER>/search_result_screen.dart';
import 'package:alderishop/views/home/<USER>/widget/MultiSelectChip.dart';
import 'package:alderishop/views/home/<USER>/widget/search.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

// ignore: must_be_immutable
class SearchPage extends StatefulWidget {
  const SearchPage({super.key});

  @override
  _SearchPageState createState() => _SearchPageState();
}

class _SearchPageState extends State<SearchPage> {
  String search = '';

  /// keeps the *provider’s* attr‑filters users pick
  List<AttrFilter> selectedAttrFilters = [];

  /// sub‑cats for the currently selected parent category
  List<CategoryModel> subCategories = [];

  /// which sub‑category the user tapped
  CategoryModel? selectedSubCategory;

  /// *local* copy of attributes so we can clear / update it
  List<AttributesDTO> attributes = [];

  /// search history cached in SharedPreferences
  List<String> searchHistory = [];

  @override
  void initState() {
    super.initState();
    _loadSearchHistory();
  }

  Future<void> _loadSearchHistory() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      searchHistory = prefs.getStringList('searchHistory') ?? [];
    });
  }

  void _handleSearch(String searchText) {
    setState(() => search = searchText);

    final controller = Provider.of<ProductsController>(context, listen: false);
    controller.searchFilter.search = searchText;
    controller.getProducts(); // or controller.getFilteredProducts();
    _navigateToResults();
  }

  void _navigateToResults() {
    final prodCtrl = context.read<ProductsController>();

    Navigator.pushReplacement(
      context,
      MaterialPageRoute(
        builder: (_) => SearchResultScreen(
          searchFilter: ProductSearchFilter(
            search: search,
            pageNumber: 1,
            pageSize: 10,
            categoryId: prodCtrl.searchFilter.categoryId,
            attrFilter: prodCtrl.searchFilter.attrFilter,
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // short‑hands
    final productCtrl = Provider.of<ProductsController>(context);
    final catCtrl = Provider.of<CategoryController>(context);

    // keep local attributes list in sync with provider
    attributes = productCtrl.attributes;

    final filter = productCtrl.searchFilter;
    final categories = catCtrl.category;
    final bool showAttr = subCategories.isEmpty || selectedSubCategory != null;

    return ApplicationLayout(
      selectedBottomNavbarItem: BottomNavbarItems.none,
      content: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CategoriesHeader(text: T('Search')),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
              child: Column(
                children: [
                  // -------------------Search bar --------------------------------
                  Search(onSearchSubmitted: _handleSearch),

                  // -------------------Filters -----------------------------------
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 5),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // -------------------Parent Categories -------------------
                        Text(
                          T("Categories"),
                          style: TextStyle(
                            fontSize: 15,
                            fontWeight: FontWeight.bold,
                            color: AppColors.BLACK_COLOR,
                          ),
                        ),

                        // parent category selector
                        SelectCategoryForFilter(
                          items: categories.toList(),
                          onSelectionChanged: (selectedCategory) async {
                            // reset state
                            setState(() {
                              selectedSubCategory = null;
                              attributes = [];
                              subCategories = [];
                            });

                            filter.categoryId = selectedCategory?.id ?? 0;

                            // pull sub‑categories
                            await Provider.of<CategoryController>(context,
                                    listen: false)
                                .getSubCategories(
                                    parentId: filter.categoryId ?? 0);

                            setState(() {
                              subCategories = Provider.of<CategoryController>(
                                      context,
                                      listen: false)
                                  .subcategories;
                            });

                            // if *no* sub‑cats → fetch attributes directly
                            if (subCategories.isEmpty) {
                              await Provider.of<ProductsController>(context,
                                      listen: false)
                                  .getAttributesWithOptionsCategoryById(
                                      categoryId: filter.categoryId ?? 0);

                              setState(() {
                                attributes = Provider.of<ProductsController>(
                                        context,
                                        listen: false)
                                    .attributes;
                              });
                            }
                          },
                        ),

                        // -------------------Sub‑Categories ----------------------
                        if (subCategories.isNotEmpty) ...[
                          const SizedBox(height: 10),
                          Text(
                            T("الفئات الفرعية"),
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: AppColors.BLACK_COLOR,
                            ),
                          ),
                          const SizedBox(height: 5),
                          Wrap(
                            spacing: 10,
                            children: subCategories.map((subcat) {
                              final bool isSelected =
                                  selectedSubCategory?.id == subcat.id;

                              return GestureDetector(
                                onTap: () async {
                                  setState(() {
                                    // toggle
                                    selectedSubCategory =
                                        isSelected ? null : subcat;
                                    attributes = []; // hide attr immediately
                                  });

                                  if (!isSelected) {
                                    filter.categoryId = subcat.id;

                                    await Provider.of<ProductsController>(
                                            context,
                                            listen: false)
                                        .getAttributesWithOptionsCategoryById(
                                            categoryId: subcat.id ?? 0);

                                    setState(() {
                                      attributes =
                                          Provider.of<ProductsController>(
                                                  context,
                                                  listen: false)
                                              .attributes;
                                    });
                                  }
                                },
                                child: Container(
                                  padding: const EdgeInsets.symmetric(
                                      vertical: 5, horizontal: 15),
                                  decoration: BoxDecoration(
                                    color: isSelected
                                        ? AppColors.PRIMARY_COLOR
                                        : Colors.white,
                                    border: Border.all(color: Colors.grey),
                                    borderRadius: BorderRadius.circular(5),
                                  ),
                                  child: Text(
                                    subcat.name ?? '',
                                    style: TextStyle(
                                      color: isSelected
                                          ? Colors.white
                                          : Colors.black,
                                    ),
                                  ),
                                ),
                              );
                            }).toList(),
                          ),
                        ],

                        // -------------------Attribute Chips --------------------
                        if (showAttr && attributes.isNotEmpty)
                          ...attributes.map((entry) {
                            return Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  entry.attributeName ?? '',
                                  style: TextStyle(
                                    fontSize: 15,
                                    fontWeight: FontWeight.bold,
                                    color: AppColors.BLACK_COLOR,
                                  ),
                                ),
                                const SizedBox(height: 8),
                                MultiSelectChip(
                                  atterName: entry.attributeName ?? '',
                                  items: entry.options ?? [],
                                  onSelectionChanged: (selected) {
                                    final inList = filter.attrFilter?.any((e) =>
                                            e.optionId == selected.optionId) ??
                                        false;
                                    if (inList) {
                                      filter.attrFilter?.removeWhere((e) =>
                                          e.optionId == selected.optionId);
                                    } else {
                                      filter.attrFilter?.add(selected);
                                    }
                                  },
                                ),
                                const SizedBox(height: 16),
                              ],
                            );
                          }).toList(),

                        // -------------------Search Button ----------------------
                        const SizedBox(height: 10),
                        InkWell(
                          onTap: () {
                            final prodCtrl = Provider.of<ProductsController>(
                                context,
                                listen: false);

                            prodCtrl.getProductCategories(
                              refresh: true,
                              resetFilters: false,
                              categoryId: prodCtrl.searchFilter.categoryId ?? 0,
                            );

                            Navigator.of(context).pushReplacement(
                              MaterialPageRoute(
                                builder: (context) => SearchResultScreen(
                                  searchFilter: ProductSearchFilter(
                                    search: search,
                                    pageNumber: 1,
                                    pageSize: 10,
                                    categoryId: prodCtrl
                                        .selectedCategoryId, // may be null
                                    attrFilter: filter.attrFilter,
                                  ),
                                ),
                              ),
                            );
                          },
                          child: CustomContainer(
                            color: AppColors.brown,
                            text: 'Search',
                            width: AppController.W,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
