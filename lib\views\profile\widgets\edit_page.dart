import 'package:alderishop/components/common_text_field.dart';
import 'package:alderishop/components/layout.dart';
import 'package:alderishop/constants/constants.dart';
import 'package:alderishop/controllers/app_controller.dart';
import 'package:alderishop/controllers/auth_Controller.dart';
import 'package:alderishop/services/helper.dart';
import 'package:alderishop/views/profile/widgets/profile_header.dart';
import 'package:alderishop/views/favorite/widgets/custom_card_Container.dart';
import 'package:alderishop/views/home/<USER>/CustomText.dart';
import 'package:flutter/material.dart';

class EditWidget extends StatefulWidget {
  final String title;
  final bool isPassword;
  final bool isFullName;
  final bool isEmail;
  const EditWidget(
      {Key? key,
      required this.title,
      this.isPassword = false,
      required this.isFullName,
      required this.isEmail})
      : super(key: key);

  @override
  State<EditWidget> createState() => _EditWidgetState();
}

class _EditWidgetState extends State<EditWidget> {
  final TextEditingController _emailEditingController = TextEditingController();
  final TextEditingController _firstnameEditingController =
      TextEditingController();
  final TextEditingController _lastnameEditingController =
      TextEditingController();
  final TextEditingController _usernameEditingController =
      TextEditingController();
  final TextEditingController _fullnameEditingController =
      TextEditingController();

  @override
  void initState() {
    super.initState();
    if (widget.isFullName) {
      // Split the full name into first name and last name
      String fullName = AuthController.getCustomerFullName();
      List<String> nameParts = fullName.split(' ');
      if (nameParts.length > 1) {
        _firstnameEditingController.text = nameParts.first;
        _lastnameEditingController.text = nameParts.sublist(1).join(' ');
      } else {
        _firstnameEditingController.text = fullName;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Layout(
      content: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const ProfileHeader(text: ""),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 30, vertical: 25),
            child: Column(
              children: [
                Row(
                  children: [
                    CustomText(
                      text: widget.title,
                      fontSize: 14,
                      fontWeight: FontWeight.w700,
                      color: AppColors.BLACK_GREY,
                    ),
                  ],
                ),
                if (widget.isEmail)
                  TextFieldWidget(controller: _emailEditingController)
                else if (widget.title == 'FirstName')
                  TextFieldWidget(controller: _firstnameEditingController)
                else if (widget.title == 'LastName')
                  TextFieldWidget(controller: _lastnameEditingController)
                else if (widget.isFullName) ...[
                  TextFieldWidget(controller: _firstnameEditingController),
                  SizedBox(height: AppController.h * 0.01),
                  TextFieldWidget(controller: _lastnameEditingController),
                ] else if (widget.title == 'Username')
                  TextFieldWidget(controller: _usernameEditingController)
                else
                  TextFieldWidget(controller: _fullnameEditingController),
              ],
            ),
          ),
          Center(
            child: InkWell(
              onTap: () async {
                showDialog(
                  context: context,
                  barrierDismissible: false,
                  builder: (BuildContext context) {
                    return const Center(child: CircularProgressIndicator());
                  },
                );

                // String email = _emailEditingController.text.isNotEmpty
                //     ? _emailEditingController.text
                //     : AuthController.getEmail();
                // String firstName = _firstnameEditingController.text.isNotEmpty
                //     ? _firstnameEditingController.text
                //     : AuthController.getCustomerFullName().split(' ').first;
                // String lastName = _lastnameEditingController.text.isNotEmpty
                //     ? _lastnameEditingController.text
                //     : AuthController.getCustomerFullName()
                //         .split(' ')
                //         .skip(1)
                //         .join(' ');
                // String username = _usernameEditingController.text.isNotEmpty
                //     ? _usernameEditingController.text
                //     : AuthController.getUserName();

                // bool isEdited =
                //     await Provider.of<ProfileController>(context, listen: false)
                //         .updateProfile(
                //             profileModel: ProfileModel(
                //   email: email,
                //   firstName: firstName,
                //   lastName: lastName,
                //   username: username,
                // ));

                // ignore: use_build_context_synchronously
                // Navigator.of(context).pop();

                // if (isEdited) {
                //   AuthController.setEmail(email);
                //   AuthController.setCustomerFullName('$firstName $lastName');
                //   AuthController.setUserName(username);

                //   // ignore: use_build_context_synchronously
                //   Navigator.of(context).pushReplacement(
                //       MaterialPageRoute(builder: (context) => AccountPage()));
                // } else {
                //   // ignore: use_build_context_synchronously
                //   errorMsg(
                //       context: context,
                //       title: T(
                //           "An error occurred during the account update process, please try again later."));
                // }
              },
              child: CustomContainerWidget(
                color: AppColors.PRIMARY_COLOR,
                height: 40,
                fontSize: 15,
                width: 360,
                text: T('Save'),
              ),
            ),
          ),
        ],
      ),
      selectedBottomNavbarItem: BottomNavbarItems.none,
    );
  }
}
