import 'package:alderishop/constants/constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CustomTextField extends StatefulWidget {
  final String text;
  final double height;
  final double width;
  final TextEditingController? controller; // Add controller parameter
  final ValueChanged<String>? onChanged;

  CustomTextField({
    Key? key,
    required this.text,
    required this.width,
    required this.height,
    this.controller, // Include controller in constructor
    this.onChanged,
  }) : super(key: key);

  @override
  State<CustomTextField> createState() => _CustomTextFieldState();
}

class _CustomTextFieldState extends State<CustomTextField> {
  @override
  Widget build(BuildContext context) {
    return Container(
      height: widget.height,
      width: widget.width,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(5.r),
        color: AppColors.SECOUND_COLOR,
      ),
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 10.w),
        child: TextField(
          controller: widget.controller, // Pass controller to TextField
          onChanged: widget.onChanged,
          decoration: InputDecoration(
            border: InputBorder.none,
            hintText: widget.text,
            hintStyle: TextStyle(
              color: AppColors.SOFT_GREY,
              fontSize: 11.sp,
              fontWeight: FontWeight.w300,
            ),
          ),
        ),
      ),
    );
  }
}
