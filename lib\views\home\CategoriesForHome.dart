import 'package:alderishop/constants/constants.dart';
import 'package:alderishop/services/helper.dart';
import 'package:alderishop/views/home/<USER>/CustomContanier.dart';
import 'package:flutter/material.dart';

class CategoriesForHomePage extends StatefulWidget {
  const CategoriesForHomePage({super.key});

  @override
  State<CategoriesForHomePage> createState() => _CategoriesForHomePageState();
}

class _CategoriesForHomePageState extends State<CategoriesForHomePage> {
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        const SizedBox(
          height: 5,
        ),
        Text(
          T('Categories'),
          style: TextStyle(
            color: AppColors.BLACK_COLOR,
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        const SizedBox(
          height: 5,
        ),
        const CustomContainerList(),
      ],
    );
  }
}
