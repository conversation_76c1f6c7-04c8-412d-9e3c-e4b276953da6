import 'package:alderishop/components/layout.dart';
import 'package:alderishop/controllers/configration_controller.dart';

import 'package:alderishop/services/helper.dart';
import 'package:alderishop/views/categories/items/widgets/headerWidget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:provider/provider.dart';

class TermsToUsePage extends StatefulWidget {
  const TermsToUsePage({super.key});

  @override
  State<TermsToUsePage> createState() => _TermsToUsePageState();
}

class _TermsToUsePageState extends State<TermsToUsePage> {
  @override
  Widget build(BuildContext context) {
    final data = Provider.of<ConfigrationController>(context, listen: false);
//
    return ApplicationLayout(
      selectedBottomNavbarItem: BottomNavbarItems.none,
      content: Column(
        children: [
          HeaderWidget(
            onTap: () {
              if (Navigator.of(context).canPop()) {
                Navigator.of(context).pop();
              }
            },
            text: T("Terms of use"),
          ),
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: data.termsToUse == null
                  ? const Center(child: CircularProgressIndicator())
                  : Html(
                      data: data.termsToUse!.body?.value,
                    ),
            ),
          ),
        ],
      ),
    );
  }
}
