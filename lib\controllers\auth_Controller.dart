import 'package:alderishop/components/common_snckbar.dart';
import 'package:alderishop/controllers/addresses_controller.dart';
import 'package:alderishop/controllers/app_controller.dart';
import 'package:alderishop/controllers/favorites_controller.dart';
import 'package:alderishop/data/api/api.dart';

import 'package:alderishop/data/model/auth_model.dart';
import 'package:alderishop/data/model/response.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

class AuthController with ChangeNotifier {
  static String _token = "";
  static String _userName = "";
  static String _email = "";
  static String _phoneNumber = "";
  static String _customerFullName = "";
  // ignore: unused_field
  static double _profitPointOnBuy = 0.0;
  static UserTypes _userType = UserTypes.customer;
  static void setEmail(String email) async {
    _email = email;
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setString('email', email);
  }

  static Future<void> setCustomerFullName(String fullName) async {
    _customerFullName = fullName;
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setString('customerFullName', fullName);
  }

  static void setUserName(String username) {
    _userName = username;
  }

  static Future<void> setPhoneNumber(String phoneNumber) async {
    _phoneNumber = phoneNumber;
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setString('phoneNumber', phoneNumber);
  }

  static Future<void> setProfitPointOnBuy(double points) async {
    _profitPointOnBuy = points;
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setDouble(
        'profitPointOnBuy', points); // Save profit points as double
  }

  static Future<double> getProfitPointOnBuy() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    return prefs.getDouble('profitPointOnBuy') ??
        0.0; // Retrieve profit points as double
  }

  Future<void> updateProfile(
      {required String fullName,
      required String email,
      required String phoneNumber}) async {
    _customerFullName = fullName;
    _email = email;
    _phoneNumber = phoneNumber;

    // Save updated values in SharedPreferences
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setString('customerFullName', fullName);
    await prefs.setString('email', email);
    await prefs.setString('phoneNumber', phoneNumber);

    // Notify listeners if needed (if using state management)
    notifyListeners();
  }

  static Map<String, String> headers = Map.fromEntries({
    'Accept-Language': appCtrl.languageCulture,
    "Authorization": 'Bearer ${getToken()}',
    'Accept': 'application/json',
  }.entries);

  bool get isAuth => _token.isNotEmpty;

  static String getToken() => _token;

  static String getUserName() => _userName;
  static String getEmail() => _email;
  static UserTypes getUserType() => _userType;
  static String getPhoneNumber() => _phoneNumber;
  static String getCustomerFullName() => _customerFullName;
  static Map<String, String> getHeader() => headers;

  static Map<String, String> get headersGet => headers;
  static Future<bool> tryAutoLogin() async {
    try {
      var pref = await SharedPreferences.getInstance();
      var token = pref.getString('token');
      var phoneNumber = pref.getString('phoneNumber');
      var email = pref.getString('email');
      var profitPoints = pref.getDouble('profitPointOnBuy') ?? 0.0;
      var customerFullName = pref.getString('customerFullName');
      var userType = pref.getInt('userType');

      if (token == null || token.isEmpty) {
        return false;
      }
      _token = token;
      _userType = UserTypes.values[userType ?? 1];
      _email = email ?? "";
      _customerFullName = customerFullName ?? "";
      _phoneNumber = phoneNumber ?? "";
      _profitPointOnBuy = profitPoints;
      AppController.isAuth = true;
      return true;
    } catch (e) {
      _token = '';
      return false;
    }
  }

  Future<ResponseResultModel> login(
    BuildContext context,
    LoginModel model,
  ) async {
    var result = await Api.post(action: 'api/Auth/Login', body: model.toJson());

    if (result == null) {
      return ResponseResultModel(isSuccess: false);
    }

    try {
      if (result.isSuccess) {
        print("Login API Response: ${result.data}");

        // Accessing the nested 'data' property
        final loginData = result.data;

        // Extract values from the API response
        var userId = loginData["id"]?.toString() ?? "";
        var token = loginData["tokenKey"]?.toString() ?? "";
        var email = loginData["email"]?.toString() ?? "";
        var phoneNumber = loginData["phoneNumber"]?.toString() ?? "";
        var fullName = loginData["userFullName"]?.toString() ?? "";
        var userType = UserTypes.values[loginData["userTypeIndex"]];
        var profitPoints = loginData["profitPointOnBuy"]?.toDouble() ?? 0.0;
        _token = token;
        if (_token.isEmpty || userId.isEmpty) {
          print("Warning: Token or User ID is empty!");
        }

        // Save values in SharedPreferences using consistent key names.
        var prefs = await SharedPreferences.getInstance();
        await prefs.setString('token', token);
        await prefs.setString('id', userId);
        await prefs.setString('customerFullName', fullName);
        await prefs.setString('email', email);
        await prefs.setString('phoneNumber', phoneNumber);
        await prefs.setInt('userType', userType.index);
        await prefs.setDouble('profitPointOnBuy', profitPoints);
        _token = token;

        _email = email;
        _customerFullName = fullName;
        _phoneNumber = phoneNumber;
        _profitPointOnBuy = profitPoints;
        _userType = userType;
        await initApp(context);
        // Update authentication state
        AppController.isAuth = true;
        notifyListeners();
      } else {
        errorMsg(context: context, title: result.errors![0]);
      }
      return result;
    } catch (e) {
      print("Error in login: $e");
      return result;
    }
  }

//------------------------------------------------------------------------------
  static Future<void> initApp(BuildContext context) async {
    await FavoritesController.getFavoritesItems();
    await Provider.of<AddressesController>(context, listen: false)
        .getAddresses();
    //   await CartController.getCartConfigrationFromShared();
    getToken();
    headers = Map.fromEntries({
      "Authorization": 'Bearer ${getToken()}',
      'Accept': 'application/json',
      'Accept-Language': appCtrl.languageCulture,
    }.entries);
  }

  //============================================================================
  static Future<bool> logout(BuildContext context) async {
    _token = '';
    var pref = await SharedPreferences.getInstance();
    pref.remove('token');
    AppController.isAuth = false;
    return true;
  }

  // //============================================================================
  // register
  Future<ResponseResultModel> register(
      BuildContext context, RegisterModel model) async {
    var result = await Api.post(
        action: 'api/Auth/RegisterCustomer', body: model.toJson());
    if (result == null) {
      return ResponseResultModel(isSuccess: false);
    }
    try {
      if (result.isSuccess) {
        AppController.isAuth = true;
        return result;
      } else {
        print('error register');
        return result;
      }
    } catch (e) {
      return ResponseResultModel(
          isSuccess: false); // Return a failure response on exception
    }
  }

  //============================================================================
  Future<bool> deleteMyAccount(BuildContext context, int? id) async {
    try {
      var response = await Api.delete(
          action: 'api/v1/Account/DeleteCustomerAccount?confirmUserId=$id');

      if (response?.isSuccess == true) {
        // ignore: use_build_context_synchronously
        var pref = await SharedPreferences.getInstance();
        pref.remove('token');
        AppController.isAuth = false;
        return true;
      } else {
        // Handle  failure case or show error message
        return false;
      }
    } catch (e) {
      // Handle error if API call fails
      print('Error deleting account: $e');
      return false;
    }
  }

  //============================================================================
  // static void updateHeaders(String newCulture) async {
  //   AppController().languageCulture = newCulture;
  //   var pref = await SharedPreferences.getInstance();
  //   var acceptLanguge = pref.getString('AcceptLanguage');
  //   if (acceptLanguge == null) {
  //     pref.setString('AcceptLanguage', AppController().languageCulture);
  //   } else {
  //     pref.remove('AcceptLanguage');
  //     var sdfcsf = AppController().languageCulture;
  //     pref.setString('AcceptLanguage', AppController().languageCulture);
  //   }
  //   _headers['Accept-Language'] = AppController().languageCulture;
  // }

  //============================================================================
  // void getHeadersOnStart() async {
  //   var pref = await SharedPreferences.getInstance();
  //   var acceptLanguge = pref.getString('AcceptLanguage');
  //   if (acceptLanguge == null) {
  //     pref.setString('AcceptLanguage', "en-Us");
  //     _headers['Accept-Language'] = "en-Us";
  //   } else {
  //     _headers['Accept-Language'] = acceptLanguge;
  //   }
  // }
}
