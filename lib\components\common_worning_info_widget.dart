import 'package:alderishop/constants/constants.dart';
import 'package:alderishop/controllers/app_controller.dart';
import 'package:flutter/material.dart';

class CommonWorningInfoWidget extends StatelessWidget {
  const CommonWorningInfoWidget({super.key, required this.text});
  final String text;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: AppController.W,
      // height: 40,
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
      decoration: BoxDecoration(
        color: AppColors.CHARCOAL,
        borderRadius: BorderRadius.circular(10),
      ),
      child: Row(
        children: [
          Icon(
            Icons.info,
            color: AppColors.RED_COLOR,
            size: 30,
          ),
          const SizedBox(width: 5),
          SizedBox(
            width: AppController.W - 100,
            child: Text(
              text,
              softWrap: true,
              style: TextStyle(
                fontSize: 12,
                color: AppColors.WHITE_COLOR,
              ),
            ),
          )
        ],
      ),
    );
  }
}
