class SliderItem {
  int? id;
  String? title;
  String? mobileImageUrl;
  String? supTitle;
  String? description2;
  String? imageUrl;
  String? url;
  int? order;
  bool? isActive;
 String? sliderType;
 int? referenceId;
  SliderItem(
      {this.id,
      this.title,
      this.mobileImageUrl,
      this.supTitle,
      this.description2,
      this.imageUrl, this.sliderType,
      this.url,
      this.referenceId,
      this.order,
      this.isActive});

  SliderItem.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    referenceId = json['referenceId'];
    title = json['title'];
    supTitle = json['supTitle'];
    mobileImageUrl = json['mobileImageUrl'];
    description2 = json['description2'];
    imageUrl = json['imageUrl'];
    url = json['url'];
    order = json['order'];
    isActive = json['isActive'];
    sliderType = json['sliderType']?.toString();

  }
}
