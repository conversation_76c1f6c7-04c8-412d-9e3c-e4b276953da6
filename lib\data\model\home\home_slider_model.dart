class SliderItem {
  int? id;
  String? title;
  String? description;
  String? description2;
  String? imageUrl;
  String? url;
  int? order;
  bool? isActive;
 String? sliderType;
 int? referenceId;
  SliderItem(
      {this.id,
      this.title,
      this.description,
      this.description2,
      this.imageUrl, this.sliderType,
      this.url,
      this.referenceId,
      this.order,
      this.isActive});

  SliderItem.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    referenceId = json['referenceId'];
    title = json['title'];
    description = json['description'];
    description2 = json['description2'];
    imageUrl = json['imageUrl'];
    url = json['url'];
    order = json['order'];
    isActive = json['isActive'];
    sliderType = json['sliderType']?.toString();

  }
}
