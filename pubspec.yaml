name: alderishop
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+5

environment:
  sdk: '>=3.0.0 <4.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  chat_bubbles: ^1.6.0 

  cupertino_icons: ^1.0.2
  flutter:
    sdk: flutter
  flutter_bloc: ^8.1.3
  curved_navigation_bar: ^1.0.3
  flutter_screenutil: ^5.9.0
  fluttertoast: ^8.2.4
  dio: ^5.4.3+1
  shared_preferences: ^2.2.3
  cached_network_image: ^3.3.1
  intl: ^0.19.0
  shimmer: ^3.0.0
  carousel_slider: ^4.2.1
  font_awesome_flutter: ^10.7.0
  get: ^4.6.6
  another_carousel_pro: ^1.0.2
  progress_stepper: ^1.0.0
  raised_buttons: ^0.0.2
  url_launcher: ^6.0.20
  percent_indicator: ^4.2.3
  dotted_border: ^2.0.0+1
  flutter_svg: ^0.22.0
  flutter_staggered_grid_view: ^0.7.0
  table_calendar: ^3.0.9
  image_picker: ^1.0.7
  provider: ^6.1.2
  get_storage: ^2.1.1
  emoji_picker_flutter: ^1.1.5
  pull_to_refresh: ^2.0.0
  another_flushbar: ^1.12.30
  flutter_html: ^3.0.0-beta.2
  lottie: ^2.3.2
  page_transition: ^2.1.0
  flutter_widget_from_html: ^0.8.0
  webview_flutter: ^3.0.4
  easy_localization: ^3.0.7
  flutter_pw_validator: ^1.5.0
  intl_phone_field: ^3.1.0
  syncfusion_flutter_calendar: ^26.1.41
  flutter_image_slideshow: ^0.1.4
  photo_view: ^0.14.0
  ticket_clippers: ^0.0.8
  smooth_page_indicator: ^1.0.0
  qr_code_scanner: ^1.0.0
  qr_flutter: ^4.0.0
  flutter_custom_clippers: ^2.1.0
  timelines_plus: ^1.0.6
  timeline_tile: ^2.0.0
  flutter_simple_calculator: ^2.3.0

  
  

dev_dependencies:
  flutter_lints: ^2.0.0
  flutter_test:
    sdk: flutter
  flutter_launcher_icons: ^0.11.0
flutter_icons:
  android: true
  ios: true
  image_path: "assets/img/logo.jpg"
# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec
# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true
  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/img/
    - assets/img/new/
    - assets/langs/
  #   - images/a_dot_ham.jpeg
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware
  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages
  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
  - family: Montserrat
    fonts:
      - asset: assets/fonts/Montserrat-Medium.ttf
      - asset: assets/fonts/Montserrat-SemiBold.ttf
        weight: 600

  - family: MontserratAR
    fonts:
      - asset: assets/fonts/Montserrat-Arabic-Regular.ttf
      - asset: assets/fonts/Montserrat-Arabic-SemiBold.ttf
        weight: 600

  
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
