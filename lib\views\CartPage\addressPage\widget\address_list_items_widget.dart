import 'package:alderishop/constants/constants.dart';
import 'package:alderishop/controllers/addresses_controller.dart';
import 'package:alderishop/controllers/app_controller.dart';
import 'package:alderishop/data/model/addresses_model.dart';
import 'package:alderishop/views/favorite/widgets/Icon_custom_Container.dart';
import 'package:alderishop/views/home/<USER>/CustomText.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class AddressListItemsWidget extends StatefulWidget {
    final AddressesModel model;
  final Function onChange;
  final Color borderColor;
  final bool isSelected;

  const AddressListItemsWidget({
    super.key,
    required this.model,
    required this.onChange,
    this.borderColor = Colors.grey,
    this.isSelected = false,
  });


  @override
  State<AddressListItemsWidget> createState() => _AddressListItemsWidgetState();
}

class _AddressListItemsWidgetState extends State<AddressListItemsWidget> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(
        horizontal: 5,
        vertical: 5,
      ),
      child: Container(
        width: 270,
        padding: const EdgeInsets.all(10),
       decoration: BoxDecoration(
  color: AppColors.BLACK_GREY.withOpacity(0.1),
  borderRadius: BorderRadius.circular(8),
  border: Border.all(
    color: widget.borderColor, // <- add this line
    width: 2, // you can adjust thickness
  ),
),

        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                CustomText(
                  text: widget.model.addressName??"عنوان الشحن",
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: AppColors.PRIMARY_COLOR,
                ),
                 InkWell(
          onTap: () async {
            await Provider.of<AddressesController>(context,
                    listen: false)
                .deleteAddress(id: widget.model.id??0);
            setState(() {});
          },
          child: IconCustomContainerWidget(
            color: AppColors.PRIMARY_COLOR,

            image: 'assets/img/delete.png',
          ),
        ),
                // InkWell(
                //   onTap: () async {
                //     Navigator.push(
                //       context,
                //       MaterialPageRoute(
                //         builder: (context) =>
                //             AddAddressesPage(id: widget.model.id),
                //       ),
                //     );
                //   },
                //   child: Icon(
                //     Icons.edit,
                //     size: 18,
                //     color: AppColors.PRIMARY_COLOR,
                //   ),
                // ),
              ],
            ),
            SizedBox(
              height: AppController.h * 0.005,
            ),
            Divider(
              height: 1,
              color: AppColors.PRIMARY_COLOR.withOpacity(0.2),
            ),
            SizedBox(
              height: AppController.h * 0.01,
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                SizedBox(
                  width: AppController.W / 2,
                  child: CustomText(
                    text:
                        "${widget.model.street ?? ""} building ${widget.model.buildingNumber ?? ""} apartment ${widget.model.apartmentNumber ?? ""}",
                    fontSize: 12,
                    fontWeight: FontWeight.w400,
                    color: AppColors.BLACK_COLOR,
                  ),
                ),
              ],
            ),
            SizedBox(
              height: AppController.h * 0.01,
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                SizedBox(
                  width: AppController.W / 2,
                  child: CustomText(
                    text:
                        "${widget.model.district ?? ""} ${widget.model.city ?? ""}",
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: AppColors.BLACK_COLOR,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
