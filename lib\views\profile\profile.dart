import 'package:alderishop/components/common_inputField.dart';
import 'package:alderishop/components/common_snckbar.dart';
import "package:alderishop/components/layout.dart";
import 'package:alderishop/constants/constants.dart';
import 'package:alderishop/controllers/app_controller.dart';
import 'package:alderishop/controllers/auth_Controller.dart';
import 'package:alderishop/services/helper.dart';
import 'package:alderishop/views/CartPage/addressPage/adresses_widget.dart';
import 'package:alderishop/views/CartPage/addressPage/widget/addAddressPage.dart';
import 'package:alderishop/components/common_header_widget.dart';
import 'package:alderishop/views/profile/my_addresses_page.dart';
import 'package:alderishop/views/profile/widgets/customContanier.dart';
import 'package:alderishop/views/auth/Sign_up.dart';
import 'package:alderishop/views/auth/sign_in.dart';
import "package:alderishop/views/favorite/widgets/custom_card_Container.dart";
import 'package:alderishop/views/home/<USER>/%C4%B0magebgWidget.dart';
import 'package:alderishop/views/home/<USER>/CustomText.dart';
import 'package:alderishop/views/profile/widgets/profit_points_widget.dart';
import 'package:flutter/material.dart';
import 'package:qr_flutter/qr_flutter.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  late TextEditingController nameController;
  late TextEditingController emailController;
  late TextEditingController phoneController;
  late TextEditingController profitPointOnBuyController;
  // late TextEditingController PasswordController;

  @override
  void initState() {
    super.initState();
    nameController = TextEditingController(
      text: AuthController.getCustomerFullName(),
    );
    emailController = TextEditingController(
      text: AuthController.getEmail(),
    );
    phoneController = TextEditingController(
      text: AuthController.getPhoneNumber(),
    );
    profitPointOnBuyController = TextEditingController(
      text: AuthController.getProfitPointOnBuy().toString(),
    );
    // PasswordController = TextEditingController(
    //   text: AuthController.getProfitPointOnBuy().toString(),
    // );
  }

  @override
  void dispose() {
    nameController.dispose();
    emailController.dispose();
    phoneController.dispose();
    profitPointOnBuyController.dispose();
    super.dispose();
  }

  Future<double> getProfitPoints() async {
    return await AuthController.getProfitPointOnBuy();
  }

  @override
  Widget build(BuildContext context) {
    return ApplicationLayout(
      selectedBottomNavbarItem: BottomNavbarItems.profile,
      content: SingleChildScrollView(
        child: Column(children: [
          CommonCategoriesHeader(
            text: T(''),
            onTap: () {
              Navigator.of(context).pop();
            },
          ),
          ImageBgWidget(),
          SizedBox(
            height: AppController.h * 0.05,
          ),
          //  --------------------------------------------------------------//

          AppController.isAuth == true
              ? Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 25),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      CustomText(
                          text: T('Profile'),
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: AppColors.BLACK_COLOR),
                      //  -----------------------------------------------------------//
                      QrImageView(
                        data:
                            "Name: ${nameController.text}\nEmail: ${emailController.text}\nPhone: ${phoneController.text}",
                        version: QrVersions.auto,
                        size: 120.0,
                      ),

                      SizedBox(height: AppController.h * 0.03),
                      InputFieldWidget(
                          controller: nameController, hint: T('Full Name *')),
                      InputFieldWidget(
                          controller: emailController, hint: T('Email *')),
                      InputFieldWidget(
                          controller: phoneController, hint: T('Phone *')),
                      //  InputFieldWidget(
                      // controller: PasswordController, hint: T('Password *')),
                    ],
                  ),
                )
              : const SizedBox.shrink(),
          const SizedBox(height: 15),
          AppController.isAuth
              ? InkWell(
                  onTap: () async {
                    await AuthController().updateProfile(
                      fullName: nameController.text,
                      email: emailController.text,
                      phoneNumber: phoneController.text,
                    );

                    // ignore: use_build_context_synchronously
                    successMsg(context: context, msg: T("Profile updated"));
                  },
                  child: CustomContainer(
                    width: AppController.W / 1.5,
                    text: T("Update"),
                    color: AppColors.brown,
                  ))
              : SizedBox(),
          SizedBox(
            height: AppController.h * 0.03,
          ),
          AppController.isAuth
              ? Column(
                  children: [
                    CustomText(
                        text: T('My Addresses'),
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: AppColors.BLACK_COLOR),
                    SizedBox(
                      height: AppController.h * 0.01,
                    ),
                    const MyAdressesWidget(
                      height: 120,
                      axisDirection: Axis.horizontal,
                      isAccountPage: true,
                      showText: false,
                    ),
                    InkWell(
                      onTap: () {
                        Navigator.of(context).push(MaterialPageRoute(
                            builder: (context) => const MyAddressesPage()));
                      },
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 35),
                        child: Container(
                          width: AppController.W,
                          padding: const EdgeInsets.symmetric(vertical: 10),
                          decoration: BoxDecoration(
                              color: AppColors.SECOUND_COLOR,
                              borderRadius: const BorderRadius.only(
                                  bottomLeft: Radius.circular(10),
                                  bottomRight: Radius.circular(10))),
                          child: Text(
                              textAlign: TextAlign.center,
                              T("See more"),
                              style: TextStyle(
                                  color: AppColors.BLACK_COLOR,
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold)),
                        ),
                      ),
                    ),
                    SizedBox(
                      height: AppController.h * 0.01,
                    ),
                    InkWell(
                      onTap: () {
                        Navigator.of(context).pushReplacement(MaterialPageRoute(
                            builder: (context) => const AddAddressesPage()));
                      },
                      child: CustomContainerWidget(
                        fontSize: 12,
                        height: 40,
                        width: AppController.W - 30,
                        color: AppColors.brown,
                        text: T("Add new Address"),
                      ),
                    ),
                    SizedBox(
                      height: AppController.h * 0.01,
                    ),
                  ],
                )
              : SizedBox(),
          AppController.isAuth
              ? Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Text(
                      T("My points"),
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: AppColors.BLACK_COLOR,
                        fontSize: 18,
                      ),
                    ),
                    SizedBox(
                      height: MediaQuery.of(context).size.height * 0.005,
                    ),
                    const ProfitPointsWidget(),
                    SizedBox(
                      height: MediaQuery.of(context).size.height * 0.01,
                    ),
                    SizedBox(
                      width: AppController.W / 1.5,
                      child: Text(
                        T("You can purchase the following coupons using your points balance according to the value"),
                        style: TextStyle(
                          height: 1.2,
                          color: AppColors.BLACK_COLOR,
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ],
                )
              : SizedBox(),
          SizedBox(
            height: MediaQuery.of(context).size.height * 0.04,
          ),
          AppController.isAuth == true
              ? InkWell(
                  onTap: () {
                    AuthController.logout(context);
                    Navigator.of(context).pushReplacement(MaterialPageRoute(
                        builder: (context) => const SignInPage()));
                  },
                  child: CustomContainer(
                    width: AppController.W / 1.12,
                    text: T("Sign Out"),
                    color: AppColors.brown,
                  ))
              : Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    InkWell(
                      onTap: () {
                        Navigator.of(context).push(MaterialPageRoute(
                            builder: (context) => const SignInPage()));
                      },
                      child: CustomContainerWidget(
                        fontSize: 12,
                        height: 40,
                        width: AppController.W - 30,
                        color: AppColors.PRIMARY_COLOR,
                        text: T("Sign In"),
                      ),
                    ),
                    InkWell(
                      onTap: () {
                        Navigator.of(context).push(MaterialPageRoute(
                            builder: (context) => const SignUp()));
                      },
                      child: CustomContainerWidget(
                        fontSize: 12,
                        height: 40,
                        width: AppController.W - 30,
                        color: AppColors.Theard_COLOR,
                        text: T("Sign Up"),
                      ),
                    ),
                  ],
                ),
          SizedBox(
            height: MediaQuery.of(context).size.height * 0.04,
          ),
        ]),
      ),
    );
  }
}
