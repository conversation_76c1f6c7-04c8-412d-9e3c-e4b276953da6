import 'package:alderishop/constants/constants.dart';
import 'package:alderishop/controllers/app_controller.dart';
import 'package:alderishop/views/home/<USER>/SearchPage.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

// ignore: must_be_immutable
class SearchWidget extends StatefulWidget {
  double height;
  double width;
  String text;

  SearchWidget(
      {super.key,
      required this.height,
      required this.width,
      required this.text});

  @override
  State<SearchWidget> createState() => _SearchWidgetState();
}

class _SearchWidgetState extends State<SearchWidget> {
  @override
  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        SizedBox(
          width: widget.width,
          height: widget.height,
          child: TextButton(
              style: ButtonStyle(
                backgroundColor:
                    MaterialStateProperty.all(AppColors.SECOUND_COLOR),
                shape: MaterialStateProperty.all(RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                )),
              ),
              onPressed: () {
                Navigator.push(context,
                    MaterialPageRoute(builder: (context) => SearchPage()));
              },
              child: Row(
                children: [
                  SizedBox(width: AppController.W * 0.01),
                  Text(
                    widget.text,
                    style: TextStyle(
                        fontSize: 12.sp,
                        color: AppColors.PRIMARY_COLOR,
                        fontWeight: FontWeight.normal),
                  )
                ],
              )),
        ),
      ],
    );
  }
}
