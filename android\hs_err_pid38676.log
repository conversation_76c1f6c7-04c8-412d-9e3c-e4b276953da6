#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 65536 bytes for Failed to commit metaspace.
# Possible reasons:
#   The system is out of physical RAM or swap space
#   The process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (virtualSpaceNode.cpp:110), pid=38676, tid=4328
#
# JRE version: OpenJDK Runtime Environment (17.0.10) (build 17.0.10+0--11572160)
# Java VM: OpenJDK 64-Bit Server VM (17.0.10+0--11572160, mixed mode, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: -Dorg.gradle.appname=gradlew org.gradle.wrapper.GradleWrapperMain -q -Ptarget-platform=android-x64 -Ptarget=C:\Users\<USER>\Documents\GitHub\Ecommerce-App\lib\main.dart -Pbase-application-name=android.app.Application -Pdart-defines=RkxVVFRFUl9XRUJfQ0FOVkFTS0lUX1VSTD1odHRwczovL3d3dy5nc3RhdGljLmNvbS9mbHV0dGVyLWNhbnZhc2tpdC9mNjM0NGI3NWRjZjg2MWQ4YmYxZjEzMjI3ODBiODgxMWY5ODJlMzFhLw== -Pdart-obfuscation=false -Ptrack-widget-creation=true -Ptree-shake-icons=false -Pfilesystem-scheme=org-dartlang-root assembleDebug

Host: AMD Ryzen 5 5600X 6-Core Processor             , 12 cores, 31G,  Windows 10 , 64 bit Build 19041 (10.0.19041.5915)
Time: Mon Jun 30 13:55:33 2025 Turkey Standard Time elapsed time: 0.562061 seconds (0d 0h 0m 0s)

---------------  T H R E A D  ---------------

Current thread (0x000001f55ae42b40):  JavaThread "main" [_thread_in_vm, id=4328, stack(0x00000017da600000,0x00000017da700000)]

Stack: [0x00000017da600000,0x00000017da700000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x688e89]
V  [jvm.dll+0x84247a]
V  [jvm.dll+0x8440be]
V  [jvm.dll+0x844723]
V  [jvm.dll+0x24be0f]
V  [jvm.dll+0x83d45b]
V  [jvm.dll+0x62d4a6]
V  [jvm.dll+0x1c28ce]
V  [jvm.dll+0x62fe01]
V  [jvm.dll+0x62de46]
V  [jvm.dll+0x23facf]
V  [jvm.dll+0x637cf5]
V  [jvm.dll+0x1eeaab]
V  [jvm.dll+0x1eefe5]
V  [jvm.dll+0x1ef985]
V  [jvm.dll+0x1e4e1c]
V  [jvm.dll+0x54dd1c]
V  [jvm.dll+0x7bb845]
V  [jvm.dll+0x7bb95e]
V  [jvm.dll+0x41628a]
V  [jvm.dll+0x41c429]
C  [java.dll+0x17ec]

Java frames: (J=compiled Java code, j=interpreted, Vv=VM code)
J 771  java.lang.ClassLoader.defineClass1(Ljava/lang/ClassLoader;Ljava/lang/String;[BIILjava/security/ProtectionDomain;Ljava/lang/String;)Ljava/lang/Class; java.base@17.0.10 (0 bytes) @ 0x000001f567606fe3 [0x000001f567606f20+0x00000000000000c3]
J 912 c1 java.lang.ClassLoader.defineClass(Ljava/lang/String;[BIILjava/security/ProtectionDomain;)Ljava/lang/Class; java.base@17.0.10 (43 bytes) @ 0x000001f55fc915dc [0x000001f55fc91280+0x000000000000035c]
J 836 c1 java.security.SecureClassLoader.defineClass(Ljava/lang/String;[BIILjava/security/CodeSource;)Ljava/lang/Class; java.base@17.0.10 (16 bytes) @ 0x000001f55fc7948c [0x000001f55fc793c0+0x00000000000000cc]
J 769 c1 java.net.URLClassLoader.defineClass(Ljava/lang/String;Ljdk/internal/loader/Resource;)Ljava/lang/Class; java.base@17.0.10 (224 bytes) @ 0x000001f55fc4df9c [0x000001f55fc4c4c0+0x0000000000001adc]
J 737 c1 java.net.URLClassLoader$1.run()Ljava/lang/Class; java.base@17.0.10 (81 bytes) @ 0x000001f55fc3b39c [0x000001f55fc3a720+0x0000000000000c7c]
J 682 c1 java.net.URLClassLoader$1.run()Ljava/lang/Object; java.base@17.0.10 (5 bytes) @ 0x000001f55fc1dac4 [0x000001f55fc1da40+0x0000000000000084]
J 613 c1 java.security.AccessController.doPrivileged(Ljava/security/PrivilegedExceptionAction;Ljava/security/AccessControlContext;)Ljava/lang/Object; java.base@17.0.10 (26 bytes) @ 0x000001f55fbf2f7c [0x000001f55fbf2da0+0x00000000000001dc]
J 680 c1 java.net.URLClassLoader.findClass(Ljava/lang/String;)Ljava/lang/Class; java.base@17.0.10 (47 bytes) @ 0x000001f55fc1cd24 [0x000001f55fc1cb80+0x00000000000001a4]
J 574 c1 java.lang.ClassLoader.loadClass(Ljava/lang/String;Z)Ljava/lang/Class; java.base@17.0.10 (121 bytes) @ 0x000001f55fbde1f4 [0x000001f55fbddc40+0x00000000000005b4]
J 690 c1 java.lang.ClassLoader.loadClass(Ljava/lang/String;)Ljava/lang/Class; java.base@17.0.10 (7 bytes) @ 0x000001f55fc2604c [0x000001f55fc25f40+0x000000000000010c]
v  ~StubRoutines::call_stub
J 771  java.lang.ClassLoader.defineClass1(Ljava/lang/ClassLoader;Ljava/lang/String;[BIILjava/security/ProtectionDomain;Ljava/lang/String;)Ljava/lang/Class; java.base@17.0.10 (0 bytes) @ 0x000001f567606fe3 [0x000001f567606f20+0x00000000000000c3]
J 912 c1 java.lang.ClassLoader.defineClass(Ljava/lang/String;[BIILjava/security/ProtectionDomain;)Ljava/lang/Class; java.base@17.0.10 (43 bytes) @ 0x000001f55fc915dc [0x000001f55fc91280+0x000000000000035c]
J 836 c1 java.security.SecureClassLoader.defineClass(Ljava/lang/String;[BIILjava/security/CodeSource;)Ljava/lang/Class; java.base@17.0.10 (16 bytes) @ 0x000001f55fc7948c [0x000001f55fc793c0+0x00000000000000cc]
J 769 c1 java.net.URLClassLoader.defineClass(Ljava/lang/String;Ljdk/internal/loader/Resource;)Ljava/lang/Class; java.base@17.0.10 (224 bytes) @ 0x000001f55fc4df9c [0x000001f55fc4c4c0+0x0000000000001adc]
J 737 c1 java.net.URLClassLoader$1.run()Ljava/lang/Class; java.base@17.0.10 (81 bytes) @ 0x000001f55fc3b39c [0x000001f55fc3a720+0x0000000000000c7c]
J 682 c1 java.net.URLClassLoader$1.run()Ljava/lang/Object; java.base@17.0.10 (5 bytes) @ 0x000001f55fc1dac4 [0x000001f55fc1da40+0x0000000000000084]
J 613 c1 java.security.AccessController.doPrivileged(Ljava/security/PrivilegedExceptionAction;Ljava/security/AccessControlContext;)Ljava/lang/Object; java.base@17.0.10 (26 bytes) @ 0x000001f55fbf2f7c [0x000001f55fbf2da0+0x00000000000001dc]
J 680 c1 java.net.URLClassLoader.findClass(Ljava/lang/String;)Ljava/lang/Class; java.base@17.0.10 (47 bytes) @ 0x000001f55fc1cd24 [0x000001f55fc1cb80+0x00000000000001a4]
J 574 c1 java.lang.ClassLoader.loadClass(Ljava/lang/String;Z)Ljava/lang/Class; java.base@17.0.10 (121 bytes) @ 0x000001f55fbde1f4 [0x000001f55fbddc40+0x00000000000005b4]
J 690 c1 java.lang.ClassLoader.loadClass(Ljava/lang/String;)Ljava/lang/Class; java.base@17.0.10 (7 bytes) @ 0x000001f55fc2604c [0x000001f55fc25f40+0x000000000000010c]
v  ~StubRoutines::call_stub
j  org.gradle.api.internal.model.InstantiatorBackedObjectFactory.property(Ljava/lang/Class;)Lorg/gradle/api/provider/Property;+0
j  org.gradle.process.internal.DefaultJavaDebugOptions.<init>(Lorg/gradle/api/model/ObjectFactory;)V+8
j  org.gradle.process.internal.DefaultJavaDebugOptions.<init>()V+11
j  org.gradle.process.internal.JvmOptions.<init>(Lorg/gradle/api/internal/file/FileCollectionFactory;)V+6
j  org.gradle.launcher.daemon.configuration.DaemonJvmOptions.<init>(Lorg/gradle/api/internal/file/FileCollectionFactory;)V+2
j  org.gradle.launcher.daemon.configuration.DaemonParameters.<init>(Lorg/gradle/launcher/configuration/BuildLayoutResult;Lorg/gradle/api/internal/file/FileCollectionFactory;Ljava/util/Map;)V+42
j  org.gradle.launcher.cli.ParametersConverter.convert(Lorg/gradle/cli/ParsedCommandLine;Ljava/io/File;)Lorg/gradle/launcher/cli/Parameters;+74
j  org.gradle.launcher.cli.BuildActionsFactory.createAction(Lorg/gradle/cli/CommandLineParser;Lorg/gradle/cli/ParsedCommandLine;)Lorg/gradle/api/Action;+6
j  org.gradle.launcher.cli.DefaultCommandLineActionFactory$ParseAndBuildAction.createAction(Lorg/gradle/cli/CommandLineParser;Lorg/gradle/cli/ParsedCommandLine;)Lorg/gradle/api/Action;+46
j  org.gradle.launcher.cli.DefaultCommandLineActionFactory$ParseAndBuildAction.execute(Lorg/gradle/launcher/bootstrap/ExecutionListener;)V+37
j  org.gradle.launcher.cli.DefaultCommandLineActionFactory$ParseAndBuildAction.execute(Ljava/lang/Object;)V+5
j  org.gradle.launcher.cli.DebugLoggerWarningAction.execute(Lorg/gradle/launcher/bootstrap/ExecutionListener;)V+9
j  org.gradle.launcher.cli.DebugLoggerWarningAction.execute(Ljava/lang/Object;)V+5
j  org.gradle.launcher.cli.WelcomeMessageAction.execute(Lorg/gradle/launcher/bootstrap/ExecutionListener;)V+218
j  org.gradle.launcher.cli.WelcomeMessageAction.execute(Ljava/lang/Object;)V+5
j  org.gradle.launcher.cli.NativeServicesInitializingAction.execute(Lorg/gradle/launcher/bootstrap/ExecutionListener;)V+35
j  org.gradle.launcher.cli.NativeServicesInitializingAction.execute(Ljava/lang/Object;)V+5
j  org.gradle.launcher.cli.ExceptionReportingAction.execute(Lorg/gradle/launcher/bootstrap/ExecutionListener;)V+5
j  org.gradle.launcher.cli.ExceptionReportingAction.execute(Ljava/lang/Object;)V+5
j  org.gradle.launcher.cli.DefaultCommandLineActionFactory$WithLogging.execute(Lorg/gradle/launcher/bootstrap/ExecutionListener;)V+299
j  org.gradle.launcher.Main.doAction([Ljava/lang/String;Lorg/gradle/launcher/bootstrap/ExecutionListener;)V+14
j  org.gradle.launcher.bootstrap.EntryPoint.run([Ljava/lang/String;)V+12
v  ~StubRoutines::call_stub
j  jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Ljava/lang/reflect/Method;Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;+0 java.base@17.0.10
j  jdk.internal.reflect.NativeMethodAccessorImpl.invoke(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;+133 java.base@17.0.10
j  jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;+6 java.base@17.0.10
j  java.lang.reflect.Method.invoke(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;+59 java.base@17.0.10
j  org.gradle.launcher.bootstrap.ProcessBootstrap.runNoExit(Ljava/lang/String;[Ljava/lang/String;)V+159
j  org.gradle.launcher.bootstrap.ProcessBootstrap.run(Ljava/lang/String;[Ljava/lang/String;)V+2
v  ~StubRoutines::call_stub
j  jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Ljava/lang/reflect/Method;Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;+0 java.base@17.0.10
j  jdk.internal.reflect.NativeMethodAccessorImpl.invoke(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;+133 java.base@17.0.10
j  jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;+6 java.base@17.0.10
j  java.lang.reflect.Method.invoke(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;+59 java.base@17.0.10
j  org.gradle.launcher.GradleMain.main([Ljava/lang/String;)V+100
v  ~StubRoutines::call_stub
j  jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Ljava/lang/reflect/Method;Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;+0 java.base@17.0.10
j  jdk.internal.reflect.NativeMethodAccessorImpl.invoke(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;+133 java.base@17.0.10
j  jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;+6 java.base@17.0.10
j  java.lang.reflect.Method.invoke(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;+59 java.base@17.0.10
j  org.gradle.wrapper.BootstrapMainStarter.start([Ljava/lang/String;Ljava/io/File;)V+82
j  org.gradle.wrapper.WrapperExecutor.execute([Ljava/lang/String;Lorg/gradle/wrapper/Install;Lorg/gradle/wrapper/BootstrapMainStarter;)V+14
j  org.gradle.wrapper.GradleWrapperMain.main([Ljava/lang/String;)V+190
v  ~StubRoutines::call_stub

Compiled method (n/a)     579  771     n 0       java.lang.ClassLoader::defineClass1 (native)
 total in heap  [0x000001f567606d90,0x000001f567607270] = 1248
 relocation     [0x000001f567606ee8,0x000001f567606f20] = 56
 main code      [0x000001f567606f20,0x000001f567607270] = 848

[Constant Pool (empty)]

[MachCode]
[Entry Point]
  # {method} {0x000001f508024f38} 'defineClass1' '(Ljava/lang/ClassLoader;Ljava/lang/String;[BIILjava/security/ProtectionDomain;Ljava/lang/String;)Ljava/lang/Class;' in 'java/lang/ClassLoader'
  # parm0:    rdx:rdx   = 'java/lang/ClassLoader'
  # parm1:    r8:r8     = 'java/lang/String'
  # parm2:    r9:r9     = '[B'
  # parm3:    rdi       = int
  # parm4:    rsi       = int
  # parm5:    rcx:rcx   = 'java/security/ProtectionDomain'
  # parm6:    [sp+0xa0]   = 'java/lang/String'  (sp of caller)
  0x000001f567606f20: 448b 5208 | 49bb 0000 | 0000 0800 | 0000 4d03 | d349 3bc2 | 0f84 0600 

  0x000001f567606f38: ;   {runtime_call ic_miss_stub}
  0x000001f567606f38: 0000 e941 | a1a6 ff90 
[Verified Entry Point]
  0x000001f567606f40: 8984 2400 | 90ff ff55 | 488b ec48 | 81ec 9000 | 0000 4881 | 7d10 0000 | 0000 488d | 4510 480f 
  0x000001f567606f60: 4445 1048 | 8944 2440 | 4889 4c24 | 7048 83f9 | 0048 8d44 | 2470 480f | 4444 2470 | 4889 4424 
  0x000001f567606f80: 3848 8974 | 2430 4889 | 7c24 284c | 894c 2458 | 4983 f900 | 488d 4424 | 5848 0f44 | 4424 5848 
  0x000001f567606fa0: 8944 2420 | 4c89 4424 | 5049 83f8 | 004c 8d4c | 2450 4c0f | 444c 2450 | 4889 5424 | 4848 83fa 
  0x000001f567606fc0: 004c 8d44 | 2448 4c0f | 4444 2448 

  0x000001f567606fcc: ;   {oop(a 'java/lang/Class'{0x0000000620030848} = 'java/lang/ClassLoader')}
  0x000001f567606fcc: 49be 4808 | 0320 0600 | 0000 4c89 | 7424 784c | 8d74 2478 | 498b d6c5 

  0x000001f567606fe4: ;   {internal_word}
  0x000001f567606fe4: f877 49ba | e36f 6067 | f501 0000 | 4d89 97a0 | 0200 0049 | 89a7 9802 

  0x000001f567606ffc: ;   {external_word}
  0x000001f567606ffc: 0000 49ba | 8839 6ce0 | f97f 0000 | 4180 3a00 | 0f84 5200 | 0000 5241 

  0x000001f567607014: ;   {metadata({method} {0x000001f508024f38} 'defineClass1' '(Ljava/lang/ClassLoader;Ljava/lang/String;[BIILjava/security/ProtectionDomain;Ljava/lang/String;)Ljava/lang/Class;' in 'java/lang/ClassLoader')}
  0x000001f567607014: 5041 5148 | ba30 4f02 | 08f5 0100 | 0049 8bcf | 4883 ec20 | f7c4 0f00 | 0000 0f84 | 1a00 0000 
  0x000001f567607034: 4883 ec08 

  0x000001f567607038: ;   {runtime_call}
  0x000001f567607038: 49ba b0f3 | 21e0 f97f | 0000 41ff | d248 83c4 | 08e9 0d00 

  0x000001f56760704c: ;   {runtime_call}
  0x000001f56760704c: 0000 49ba | b0f3 21e0 | f97f 0000 | 41ff d248 | 83c4 2041 | 5941 585a | 498d 8fb8 | 0200 0041 
  0x000001f56760706c: c787 4803 | 0000 0400 

  0x000001f567607074: ;   {runtime_call}
  0x000001f567607074: 0000 49ba | ac16 1b2e | fa7f 0000 | 41ff d2c5 | f877 41c7 | 8748 0300 | 0005 0000 | 00f0 8344 
  0x000001f567607094: 24c0 0049 | 3baf 5003 | 0000 0f87 | 1100 0000 | 4181 bf38 | 0300 0000 | 0000 000f | 842c 0000 
  0x000001f5676070b4: 00c5 f877 | 4889 45f8 | 498b cf4c | 8be4 4883 | ec20 4883 

  0x000001f5676070c8: ;   {runtime_call}
  0x000001f5676070c8: e4f0 49ba | d011 32e0 | f97f 0000 | 41ff d249 | 8be4 4d33 | e448 8b45 | f841 c787 | 4803 0000 
  0x000001f5676070e8: 0800 0000 | 4181 bfb8 | 0300 0002 | 0000 000f | 843f 0100 

  0x000001f5676070fc: ;   {external_word}
  0x000001f5676070fc: 0049 ba88 | 396c e0f9 | 7f00 0041 | 803a 000f | 8450 0000 | 0048 8945 

  0x000001f567607114: ;   {metadata({method} {0x000001f508024f38} 'defineClass1' '(Ljava/lang/ClassLoader;Ljava/lang/String;[BIILjava/security/ProtectionDomain;Ljava/lang/String;)Ljava/lang/Class;' in 'java/lang/ClassLoader')}
  0x000001f567607114: f848 ba30 | 4f02 08f5 | 0100 0049 | 8bcf 4883 | ec20 f7c4 | 0f00 0000 | 0f84 1a00 | 0000 4883 
  0x000001f567607134: ;   {runtime_call}
  0x000001f567607134: ec08 49ba | b0f3 21e0 | f97f 0000 | 41ff d248 | 83c4 08e9 | 0d00 0000 

  0x000001f56760714c: ;   {runtime_call}
  0x000001f56760714c: 49ba b0f3 | 21e0 f97f | 0000 41ff | d248 83c4 | 2048 8b45 | f849 c787 | 9802 0000 | 0000 0000 
  0x000001f56760716c: 49c7 87a0 | 0200 0000 | 0000 00c5 | f877 4885 | c00f 8493 | 0000 0048 | f7c0 0100 | 0000 0f84 
  0x000001f56760718c: 8300 0000 | 488b 40ff | 4180 7f38 | 000f 846f | 0000 0048 | 83f8 000f | 8465 0000 | 0049 8b4f 
  0x000001f5676071ac: 2048 83f9 | 000f 8414 | 0000 0048 | 83e9 0849 | 894f 2049 | 034f 3048 | 8901 e943 | 0000 0050 
  0x000001f5676071cc: 498b d748 | 8bc8 4883 | ec20 f7c4 | 0f00 0000 | 0f84 1a00 | 0000 4883 

  0x000001f5676071e4: ;   {runtime_call}
  0x000001f5676071e4: ec08 49ba | 908f e0df | f97f 0000 | 41ff d248 | 83c4 08e9 | 0d00 0000 

  0x000001f5676071fc: ;   {runtime_call}
  0x000001f5676071fc: 49ba 908f | e0df f97f | 0000 41ff | d248 83c4 | 2058 e903 | 0000 0048 | 8b00 498b | 8fd8 0000 
  0x000001f56760721c: 00c7 8100 | 0100 0000 | 0000 00c9 | 4981 7f08 | 0000 0000 | 0f85 0100 

  0x000001f567607234: ;   {runtime_call StubRoutines (1)}
  0x000001f567607234: 0000 c3e9 | c49c a1ff | c5f8 7748 | 8945 f84c | 8be4 4883 | ec20 4883 

  0x000001f56760724c: ;   {runtime_call}
  0x000001f56760724c: e4f0 49ba | 3024 22e0 | f97f 0000 | 41ff d249 | 8be4 4d33 | e448 8b45 | f8e9 93fe | ffff f4f4 
  0x000001f56760726c: f4f4 f4f4 
[/MachCode]


Compiled method (c1)     584  912       3       java.lang.ClassLoader::defineClass (43 bytes)
 total in heap  [0x000001f55fc91090,0x000001f55fc919c8] = 2360
 relocation     [0x000001f55fc911e8,0x000001f55fc91278] = 144
 main code      [0x000001f55fc91280,0x000001f55fc91720] = 1184
 stub code      [0x000001f55fc91720,0x000001f55fc91798] = 120
 metadata       [0x000001f55fc91798,0x000001f55fc917c8] = 48
 scopes data    [0x000001f55fc917c8,0x000001f55fc918b8] = 240
 scopes pcs     [0x000001f55fc918b8,0x000001f55fc91998] = 224
 dependencies   [0x000001f55fc91998,0x000001f55fc919a0] = 8
 nul chk table  [0x000001f55fc919a0,0x000001f55fc919c8] = 40

[Constant Pool (empty)]

[MachCode]
[Entry Point]
  # {method} {0x000001f508024d18} 'defineClass' '(Ljava/lang/String;[BIILjava/security/ProtectionDomain;)Ljava/lang/Class;' in 'java/lang/ClassLoader'
  # this:     rdx:rdx   = 'java/lang/ClassLoader'
  # parm0:    r8:r8     = 'java/lang/String'
  # parm1:    r9:r9     = '[B'
  # parm2:    rdi       = int
  # parm3:    rsi       = int
  # parm4:    rcx:rcx   = 'java/security/ProtectionDomain'
  #           [sp+0xc0]  (sp of caller)
  0x000001f55fc91280: 448b 5208 | 49bb 0000 | 0000 0800 | 0000 4d03 | d34c 3bd0 

  0x000001f55fc91294: ;   {runtime_call ic_miss_stub}
  0x000001f55fc91294: 0f85 e6fd | 3d07 660f | 1f44 0000 
[Verified Entry Point]
  0x000001f55fc912a0: 8984 2400 | 90ff ff55 | 4881 ecb0 | 0000 0048 | 8954 2478 

  0x000001f55fc912b4: ;   {metadata(method data for {method} {0x000001f508024d18} 'defineClass' '(Ljava/lang/String;[BIILjava/security/ProtectionDomain;)Ljava/lang/Class;' in 'java/lang/ClassLoader')}
  0x000001f55fc912b4: 48bb b098 | 9e08 f501 | 0000 8b83 | cc00 0000 | 83c0 0289 | 83cc 0000 | 0081 e0fe | 0700 0083 
  0x000001f55fc912d4: f800 0f84 | 5603 0000 | 89b4 2494 | 0000 0089 | bc24 9000 | 0000 4c89 | 8c24 8800 | 0000 488b 
  0x000001f55fc912f4: ;   {metadata(method data for {method} {0x000001f508024d18} 'defineClass' '(Ljava/lang/String;[BIILjava/security/ProtectionDomain;)Ljava/lang/Class;' in 'java/lang/ClassLoader')}
  0x000001f55fc912f4: da48 b8b0 | 989e 08f5 | 0100 0048 | 8380 1001 | 0000 0149 | 8bd8 4c8b | c34c 8bc9 | 488b ca48 
  0x000001f55fc91314: 8bd1 4889 | 9c24 8000 

  0x000001f55fc9131c: ;   {optimized virtual_call}
  0x000001f55fc9131c: 0000 90e8 

  0x000001f55fc91320: ; ImmutableOopMap {[120]=Oop [136]=Oop [128]=Oop }
                      ;*invokevirtual preDefineClass {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.ClassLoader::defineClass@4 (line 1015)
  0x000001f55fc91320: 7c2c 0000 | 4889 8424 | 9800 0000 | 488b 5424 

  0x000001f55fc91330: ;   {metadata(method data for {method} {0x000001f508024d18} 'defineClass' '(Ljava/lang/String;[BIILjava/security/ProtectionDomain;)Ljava/lang/Class;' in 'java/lang/ClassLoader')}
  0x000001f55fc91330: 7849 b8b0 | 989e 08f5 | 0100 0049 | 8380 4801 

  0x000001f55fc91340: ;   {metadata(method data for {method} {0x000001f508024ae0} 'defineClassSourceLocation' '(Ljava/security/ProtectionDomain;)Ljava/lang/String;' in 'java/lang/ClassLoader')}
  0x000001f55fc91340: 0000 0149 | b818 56b2 | 08f5 0100 | 0041 8b90 | cc00 0000 | 83c2 0241 | 8990 cc00 | 0000 81e2 
  0x000001f55fc91360: feff 1f00 | 83fa 000f | 84e6 0200 | 0048 3b00 

  0x000001f55fc91370: ;   {metadata(method data for {method} {0x000001f508024ae0} 'defineClassSourceLocation' '(Ljava/security/ProtectionDomain;)Ljava/lang/String;' in 'java/lang/ClassLoader')}
  0x000001f55fc91370: 4c8b c048 | ba18 56b2 | 08f5 0100 | 0048 8382 | 1001 0000 | 0144 8b40 | 1049 c1e0 | 0349 83f8 
  0x000001f55fc91390: ;   {metadata(method data for {method} {0x000001f508024ae0} 'defineClassSourceLocation' '(Ljava/security/ProtectionDomain;)Ljava/lang/String;' in 'java/lang/ClassLoader')}
  0x000001f55fc91390: 0048 ba18 | 56b2 08f5 | 0100 0048 | be58 0100 | 0000 0000 | 000f 850a | 0000 0048 | be48 0100 
  0x000001f55fc913b0: 0000 0000 | 0048 8b3c | 3248 8d7f | 0148 893c | 320f 850f 

  0x000001f55fc913c4: ;   {oop(NULL)}
  0x000001f55fc913c4: 0000 0048 | bb00 0000 | 0000 0000 | 00e9 b101 | 0000 493b | 0049 8bd0 

  0x000001f55fc913dc: ;   {metadata(method data for {method} {0x000001f508024ae0} 'defineClassSourceLocation' '(Ljava/security/ProtectionDomain;)Ljava/lang/String;' in 'java/lang/ClassLoader')}
  0x000001f55fc913dc: 48be 1856 | b208 f501 | 0000 4883 | 8668 0100 | 0001 418b | 500c 48c1 | e203 4883 

  0x000001f55fc913f8: ;   {metadata(method data for {method} {0x000001f508024ae0} 'defineClassSourceLocation' '(Ljava/security/ProtectionDomain;)Ljava/lang/String;' in 'java/lang/ClassLoader')}
  0x000001f55fc913f8: fa00 48be | 1856 b208 | f501 0000 | 48bf b001 | 0000 0000 | 0000 0f85 | 0a00 0000 | 48bf a001 
  0x000001f55fc91418: 0000 0000 | 0000 488b | 1c3e 488d | 5b01 4889 | 1c3e 0f85 | 0f00 0000 

  0x000001f55fc91430: ;   {oop(NULL)}
  0x000001f55fc91430: 48bb 0000 | 0000 0000 | 0000 e948 

  0x000001f55fc9143c: ;   {metadata(method data for {method} {0x000001f508024ae0} 'defineClassSourceLocation' '(Ljava/security/ProtectionDomain;)Ljava/lang/String;' in 'java/lang/ClassLoader')}
  0x000001f55fc9143c: 0100 0048 | be18 56b2 | 08f5 0100 | 0048 8386 | c001 0000 | 0148 3b02 

  0x000001f55fc91454: ;   {metadata(method data for {method} {0x000001f508024ae0} 'defineClassSourceLocation' '(Ljava/security/ProtectionDomain;)Ljava/lang/String;' in 'java/lang/ClassLoader')}
  0x000001f55fc91454: 4c8b c248 | be18 56b2 | 08f5 0100 | 0048 8386 | f801 0000 

  0x000001f55fc91468: ;   {metadata(method data for {method} {0x000001f5080e87e8} 'toString' '()Ljava/lang/String;' in 'java/net/URL')}
  0x000001f55fc91468: 0149 b8a0 | 2576 08f5 | 0100 0041 | 8bb0 cc00 | 0000 83c6 | 0241 89b0 | cc00 0000 | 81e6 feff 
  0x000001f55fc91488: 1f00 83fe | 000f 84f0 | 0100 004c 

  0x000001f55fc91494: ;   {metadata(method data for {method} {0x000001f5080e87e8} 'toString' '()Ljava/lang/String;' in 'java/net/URL')}
  0x000001f55fc91494: 8bc2 48be | a025 7608 | f501 0000 | 4883 8610 | 0100 0001 

  0x000001f55fc914a8: ;   {metadata(method data for {method} {0x000001f5080e88a0} 'toExternalForm' '()Ljava/lang/String;' in 'java/net/URL')}
  0x000001f55fc914a8: 49b8 2827 | 7608 f501 | 0000 418b | b0cc 0000 | 0083 c602 | 4189 b0cc | 0000 0081 | e6fe ff1f 
  0x000001f55fc914c8: 0083 fe00 | 0f84 d201 | 0000 8b72 | 3848 c1e6 | 0348 3b06 

  0x000001f55fc914dc: ;   {metadata(method data for {method} {0x000001f5080e88a0} 'toExternalForm' '()Ljava/lang/String;' in 'java/net/URL')}
  0x000001f55fc914dc: 4c8b c648 | bf28 2776 | 08f5 0100 | 0045 8b40 | 0849 ba00 | 0000 0008 | 0000 004d | 03c2 4c3b 
  0x000001f55fc914fc: 8720 0100 | 0075 0d48 | 8387 2801 | 0000 01e9 | 6600 0000 | 4c3b 8730 | 0100 0075 | 0d48 8387 
  0x000001f55fc9151c: 3801 0000 | 01e9 5000 | 0000 4881 | bf20 0100 | 0000 0000 | 0075 174c | 8987 2001 | 0000 48c7 
  0x000001f55fc9153c: 8728 0100 | 0001 0000 | 00e9 2c00 | 0000 4881 | bf30 0100 | 0000 0000 | 0075 174c | 8987 3001 
  0x000001f55fc9155c: 0000 48c7 | 8738 0100 | 0001 0000 | 00e9 0800 | 0000 4883 | 8710 0100 | 0001 4c8b | c248 8bd6 
  0x000001f55fc9157c: ;   {optimized virtual_call}
  0x000001f55fc9157c: 6666 90e8 

  0x000001f55fc91580: ; ImmutableOopMap {[120]=Oop [136]=Oop [128]=Oop [152]=Oop }
                      ;*invokevirtual toExternalForm {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.net.URL::toExternalForm@5 (line 1039)
                      ; - java.net.URL::toString@1 (line 1025)
                      ; - java.lang.ClassLoader::defineClassSourceLocation@22 (line 917)
                      ; - java.lang.ClassLoader::defineClass@12 (line 1016)
  0x000001f55fc91580: 3c63 faff | 488b d848 | 8b84 2498 | 0000 008b | b424 9400 | 0000 8bbc | 2490 0000 | 004c 8b8c 
  0x000001f55fc915a0: 2488 0000 | 004c 8b84 | 2480 0000 | 0048 8b54 

  0x000001f55fc915b0: ;   {metadata(method data for {method} {0x000001f508024d18} 'defineClass' '(Ljava/lang/String;[BIILjava/security/ProtectionDomain;)Ljava/lang/Class;' in 'java/lang/ClassLoader')}
  0x000001f55fc915b0: 2478 48b9 | b098 9e08 | f501 0000 | 4883 8180 | 0100 0001 | 4c8b da49 | 8bd3 488b | c848 891c 
  0x000001f55fc915d0: 2466 0f1f 

  0x000001f55fc915d4: ;   {static_call}
  0x000001f55fc915d4: 4400 00e8 

  0x000001f55fc915d8: ; ImmutableOopMap {[152]=Oop [120]=Oop }
                      ;*invokestatic defineClass1 {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.ClassLoader::defineClass@27 (line 1017)
  0x000001f55fc915d8: 6459 9707 | 488b 5424 

  0x000001f55fc915e0: ;   {metadata(method data for {method} {0x000001f508024d18} 'defineClass' '(Ljava/lang/String;[BIILjava/security/ProtectionDomain;)Ljava/lang/Class;' in 'java/lang/ClassLoader')}
  0x000001f55fc915e0: 7849 b8b0 | 989e 08f5 | 0100 0049 | 8380 9001 | 0000 014c | 8bc0 4c8b | 8c24 9800 | 0000 488b 
  0x000001f55fc91600: 5424 7848 | 8984 24a0 | 0000 000f 

  0x000001f55fc9160c: ;   {optimized virtual_call}
  0x000001f55fc9160c: 1f40 00e8 

  0x000001f55fc91610: ; ImmutableOopMap {[160]=Oop }
                      ;*invokevirtual postDefineClass {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.ClassLoader::defineClass@37 (line 1018)
  0x000001f55fc91610: 0c23 fcff | 488b 8424 | a000 0000 | 4881 c4b0 | 0000 005d 

  0x000001f55fc91624: ;   {poll_return}
  0x000001f55fc91624: 493b a750 | 0300 000f | 8799 0000 

  0x000001f55fc91630: ;   {metadata({method} {0x000001f508024d18} 'defineClass' '(Ljava/lang/String;[BIILjava/security/ProtectionDomain;)Ljava/lang/Class;' in 'java/lang/ClassLoader')}
  0x000001f55fc91630: 00c3 49ba | 104d 0208 | f501 0000 | 4c89 5424 | 0848 c704 | 24ff ffff 

  0x000001f55fc91648: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000001f55fc91648: ffe8 3293 

  0x000001f55fc9164c: ; ImmutableOopMap {rdx=Oop [120]=Oop r8=Oop r9=Oop rcx=Oop }
                      ;*synchronization entry
                      ; - java.lang.ClassLoader::defineClass@-1 (line 1015)
  0x000001f55fc9164c: 4807 e989 

  0x000001f55fc91650: ;   {metadata({method} {0x000001f508024ae0} 'defineClassSourceLocation' '(Ljava/security/ProtectionDomain;)Ljava/lang/String;' in 'java/lang/ClassLoader')}
  0x000001f55fc91650: fcff ff49 | bad8 4a02 | 08f5 0100 | 004c 8954 | 2408 48c7 | 0424 ffff 

  0x000001f55fc91668: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000001f55fc91668: ffff e811 

  0x000001f55fc9166c: ; ImmutableOopMap {[120]=Oop [136]=Oop [128]=Oop rax=Oop [152]=Oop }
                      ;*synchronization entry
                      ; - java.lang.ClassLoader::defineClassSourceLocation@-1 (line 914)
                      ; - java.lang.ClassLoader::defineClass@12 (line 1016)
  0x000001f55fc9166c: 9348 07e9 | f9fc ffff 

  0x000001f55fc91674: ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x000001f55fc91674: e827 3648 

  0x000001f55fc91678: ; ImmutableOopMap {[120]=Oop [136]=Oop [128]=Oop rax=Oop [152]=Oop }
                      ;*invokevirtual getCodeSource {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.ClassLoader::defineClassSourceLocation@1 (line 914)
                      ; - java.lang.ClassLoader::defineClass@12 (line 1016)
                      ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x000001f55fc91678: 07e8 2236 

  0x000001f55fc9167c: ; ImmutableOopMap {[120]=Oop [136]=Oop [128]=Oop rax=Oop [152]=Oop r8=Oop }
                      ;*invokevirtual getLocation {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.ClassLoader::defineClassSourceLocation@12 (line 916)
                      ; - java.lang.ClassLoader::defineClass@12 (line 1016)
                      ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x000001f55fc9167c: 4807 e81d 

  0x000001f55fc91680: ; ImmutableOopMap {[120]=Oop [136]=Oop [128]=Oop [152]=Oop rdx=Oop }
                      ;*invokevirtual toString {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.ClassLoader::defineClassSourceLocation@22 (line 917)
                      ; - java.lang.ClassLoader::defineClass@12 (line 1016)
                      ;   {metadata({method} {0x000001f5080e87e8} 'toString' '()Ljava/lang/String;' in 'java/net/URL')}
  0x000001f55fc91680: 3648 0749 | bae0 870e | 08f5 0100 | 004c 8954 | 2408 48c7 | 0424 ffff 

  0x000001f55fc91698: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000001f55fc91698: ffff e8e1 

  0x000001f55fc9169c: ; ImmutableOopMap {[120]=Oop [136]=Oop [128]=Oop [152]=Oop rdx=Oop }
                      ;*synchronization entry
                      ; - java.net.URL::toString@-1 (line 1025)
                      ; - java.lang.ClassLoader::defineClassSourceLocation@22 (line 917)
                      ; - java.lang.ClassLoader::defineClass@12 (line 1016)
  0x000001f55fc9169c: 9248 07e9 | effd ffff 

  0x000001f55fc916a4: ;   {metadata({method} {0x000001f5080e88a0} 'toExternalForm' '()Ljava/lang/String;' in 'java/net/URL')}
  0x000001f55fc916a4: 49ba 9888 | 0e08 f501 | 0000 4c89 | 5424 0848 | c704 24ff 

  0x000001f55fc916b8: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000001f55fc916b8: ffff ffe8 

  0x000001f55fc916bc: ; ImmutableOopMap {[120]=Oop [136]=Oop [128]=Oop [152]=Oop rdx=Oop }
                      ;*synchronization entry
                      ; - java.net.URL::toExternalForm@-1 (line 1039)
                      ; - java.net.URL::toString@1 (line 1025)
                      ; - java.lang.ClassLoader::defineClassSourceLocation@22 (line 917)
                      ; - java.lang.ClassLoader::defineClass@12 (line 1016)
  0x000001f55fc916bc: c092 4807 | e90d feff 

  0x000001f55fc916c4: ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x000001f55fc916c4: ffe8 d635 

  0x000001f55fc916c8: ; ImmutableOopMap {[120]=Oop [136]=Oop [128]=Oop [152]=Oop rdx=Oop rsi=Oop }
                      ;*invokevirtual toExternalForm {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.net.URL::toExternalForm@5 (line 1039)
                      ; - java.net.URL::toString@1 (line 1025)
                      ; - java.lang.ClassLoader::defineClassSourceLocation@22 (line 917)
                      ; - java.lang.ClassLoader::defineClass@12 (line 1016)
                      ;   {internal_word}
  0x000001f55fc916c8: 4807 49ba | 2416 c95f | f501 0000 | 4d89 9768 

  0x000001f55fc916d8: ;   {runtime_call SafepointBlob}
  0x000001f55fc916d8: 0300 00e9 | 2063 3e07 | 9090 498b | 87e0 0300 | 0049 c787 | e003 0000 | 0000 0000 | 49c7 87e8 
  0x000001f55fc916f8: 0300 0000 | 0000 0048 | 81c4 b000 

  0x000001f55fc91704: ;   {runtime_call unwind_exception Runtime1 stub}
  0x000001f55fc91704: 0000 5de9 | f455 3f07 | f4f4 f4f4 | f4f4 f4f4 | f4f4 f4f4 | f4f4 f4f4 | f4f4 f4f4 
[Stub Code]
  0x000001f55fc91720: ;   {no_reloc}
  0x000001f55fc91720: 0f1f 4400 

  0x000001f55fc91724: ;   {static_stub}
  0x000001f55fc91724: 0048 bbe8 | 4902 08f5 

  0x000001f55fc9172c: ;   {runtime_call I2C/C2I adapters}
  0x000001f55fc9172c: 0100 00e9 | adc2 3d07 

  0x000001f55fc91734: ;   {static_stub}
  0x000001f55fc91734: 9048 bb00 | 0000 0000 

  0x000001f55fc9173c: ;   {runtime_call}
  0x000001f55fc9173c: 0000 00e9 | fbff ffff 

  0x000001f55fc91744: ;   {static_stub}
  0x000001f55fc91744: 9048 bb00 | 0000 0000 

  0x000001f55fc9174c: ;   {runtime_call}
  0x000001f55fc9174c: 0000 00e9 | fbff ffff 

  0x000001f55fc91754: ;   {static_stub}
  0x000001f55fc91754: 9048 bb00 | 0000 0000 

  0x000001f55fc9175c: ;   {runtime_call}
  0x000001f55fc9175c: 0000 00e9 | fbff ffff 
[Exception Handler]
  0x000001f55fc91764: ;   {runtime_call handle_exception_from_callee Runtime1 stub}
  0x000001f55fc91764: e817 5f48 

  0x000001f55fc91768: ;   {external_word}
  0x000001f55fc91768: 0748 b9a0 | 7d46 e0f9 | 7f00 0048 

  0x000001f55fc91774: ;   {runtime_call}
  0x000001f55fc91774: 83e4 f049 | baf0 e40e | e0f9 7f00 | 0041 ffd2 

  0x000001f55fc91784: ;   {section_word}
  0x000001f55fc91784: f449 ba85 | 17c9 5ff5 | 0100 0041 

  0x000001f55fc91790: ;   {runtime_call DeoptimizationBlob}
  0x000001f55fc91790: 52e9 0a55 | 3e07 f4f4 
[/MachCode]


Compiled method (c1)     593  836       3       java.security.SecureClassLoader::defineClass (16 bytes)
 total in heap  [0x000001f55fc79210,0x000001f55fc79600] = 1008
 relocation     [0x000001f55fc79368,0x000001f55fc793b0] = 72
 main code      [0x000001f55fc793c0,0x000001f55fc79500] = 320
 stub code      [0x000001f55fc79500,0x000001f55fc79558] = 88
 metadata       [0x000001f55fc79558,0x000001f55fc79560] = 8
 scopes data    [0x000001f55fc79560,0x000001f55fc795a8] = 72
 scopes pcs     [0x000001f55fc795a8,0x000001f55fc795f8] = 80
 dependencies   [0x000001f55fc795f8,0x000001f55fc79600] = 8

[Constant Pool (empty)]

[MachCode]
[Entry Point]
  # {method} {0x000001f50803da68} 'defineClass' '(Ljava/lang/String;[BIILjava/security/CodeSource;)Ljava/lang/Class;' in 'java/security/SecureClassLoader'
  # this:     rdx:rdx   = 'java/security/SecureClassLoader'
  # parm0:    r8:r8     = 'java/lang/String'
  # parm1:    r9:r9     = '[B'
  # parm2:    rdi       = int
  # parm3:    rsi       = int
  # parm4:    rcx:rcx   = 'java/security/CodeSource'
  #           [sp+0x80]  (sp of caller)
  0x000001f55fc793c0: 448b 5208 | 49bb 0000 | 0000 0800 | 0000 4d03 | d34c 3bd0 

  0x000001f55fc793d4: ;   {runtime_call ic_miss_stub}
  0x000001f55fc793d4: 0f85 a67c | 3f07 660f | 1f44 0000 
[Verified Entry Point]
  0x000001f55fc793e0: 8984 2400 | 90ff ff55 | 4883 ec70 

  0x000001f55fc793ec: ;   {metadata(method data for {method} {0x000001f50803da68} 'defineClass' '(Ljava/lang/String;[BIILjava/security/CodeSource;)Ljava/lang/Class;' in 'java/security/SecureClassLoader')}
  0x000001f55fc793ec: 48bb 4805 | 9b08 f501 | 0000 8b83 | cc00 0000 | 83c0 0289 | 83cc 0000 | 0081 e0fe | 0700 0083 
  0x000001f55fc7940c: f800 0f84 | 8b00 0000 | 8974 245c | 897c 2458 | 4c89 4c24 | 504c 8944 | 2448 488b 

  0x000001f55fc79428: ;   {metadata(method data for {method} {0x000001f50803da68} 'defineClass' '(Ljava/lang/String;[BIILjava/security/CodeSource;)Ljava/lang/Class;' in 'java/security/SecureClassLoader')}
  0x000001f55fc79428: da48 b848 | 059b 08f5 | 0100 0048 | 8380 1001 | 0000 014c | 8bc1 488b | c248 8bd0 | 4889 4424 
  0x000001f55fc79448: 4066 0f1f 

  0x000001f55fc7944c: ;   {optimized virtual_call}
  0x000001f55fc7944c: 4400 00e8 

  0x000001f55fc79450: ; ImmutableOopMap {[80]=Oop [64]=Oop [72]=Oop }
                      ;*invokevirtual getProtectionDomain {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.security.SecureClassLoader::defineClass@9 (line 150)
  0x000001f55fc79450: cc03 0000 | 488b 5424 

  0x000001f55fc79458: ;   {metadata(method data for {method} {0x000001f50803da68} 'defineClass' '(Ljava/lang/String;[BIILjava/security/CodeSource;)Ljava/lang/Class;' in 'java/security/SecureClassLoader')}
  0x000001f55fc79458: 4049 b848 | 059b 08f5 | 0100 0049 | 8380 4801 | 0000 014c | 8b44 2448 | 4c8b 4c24 | 508b 7c24 
  0x000001f55fc79478: 588b 7424 | 5c48 8bc8 | 488b 5424 

  0x000001f55fc79484: ;   {optimized virtual_call}
  0x000001f55fc79484: 4066 90e8 

  0x000001f55fc79488: ; ImmutableOopMap {}
                      ;*invokevirtual defineClass {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.security.SecureClassLoader::defineClass@12 (line 150)
  0x000001f55fc79488: 147e 0100 | 4883 c470 

  0x000001f55fc79490: ;   {poll_return}
  0x000001f55fc79490: 5d49 3ba7 | 5003 0000 | 0f87 2200 

  0x000001f55fc7949c: ;   {metadata({method} {0x000001f50803da68} 'defineClass' '(Ljava/lang/String;[BIILjava/security/CodeSource;)Ljava/lang/Class;' in 'java/security/SecureClassLoader')}
  0x000001f55fc7949c: 0000 c349 | ba60 da03 | 08f5 0100 | 004c 8954 | 2408 48c7 | 0424 ffff 

  0x000001f55fc794b4: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000001f55fc794b4: ffff e8c5 

  0x000001f55fc794b8: ; ImmutableOopMap {rdx=Oop r8=Oop r9=Oop rcx=Oop }
                      ;*synchronization entry
                      ; - java.security.SecureClassLoader::defineClass@-1 (line 150)
  0x000001f55fc794b8: 144a 07e9 | 54ff ffff 

  0x000001f55fc794c0: ;   {internal_word}
  0x000001f55fc794c0: 49ba 9194 | c75f f501 | 0000 4d89 | 9768 0300 

  0x000001f55fc794d0: ;   {runtime_call SafepointBlob}
  0x000001f55fc794d0: 00e9 2ae5 | 3f07 9090 | 498b 87e0 | 0300 0049 | c787 e003 | 0000 0000 | 0000 49c7 | 87e8 0300 
  0x000001f55fc794f0: 0000 0000 | 0048 83c4 

  0x000001f55fc794f8: ;   {runtime_call unwind_exception Runtime1 stub}
  0x000001f55fc794f8: 705d e901 | d840 07f4 
[Stub Code]
  0x000001f55fc79500: ;   {no_reloc}
  0x000001f55fc79500: 0f1f 4400 

  0x000001f55fc79504: ;   {static_stub}
  0x000001f55fc79504: 0048 bbf0 | dc03 08f5 

  0x000001f55fc7950c: ;   {runtime_call I2C/C2I adapters}
  0x000001f55fc7950c: 0100 00e9 | 49d1 3f07 

  0x000001f55fc79514: ;   {static_stub}
  0x000001f55fc79514: 9048 bb10 | 4d02 08f5 

  0x000001f55fc7951c: ;   {runtime_call I2C/C2I adapters}
  0x000001f55fc7951c: 0100 00e9 | c7de 3e07 
[Exception Handler]
  0x000001f55fc79524: ;   {runtime_call handle_exception_from_callee Runtime1 stub}
  0x000001f55fc79524: e857 e149 

  0x000001f55fc79528: ;   {external_word}
  0x000001f55fc79528: 0748 b9a0 | 7d46 e0f9 | 7f00 0048 

  0x000001f55fc79534: ;   {runtime_call}
  0x000001f55fc79534: 83e4 f049 | baf0 e40e | e0f9 7f00 | 0041 ffd2 

  0x000001f55fc79544: ;   {section_word}
  0x000001f55fc79544: f449 ba45 | 95c7 5ff5 | 0100 0041 

  0x000001f55fc79550: ;   {runtime_call DeoptimizationBlob}
  0x000001f55fc79550: 52e9 4ad7 | 3f07 f4f4 
[/MachCode]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000001f50cb68110, length=14, elements={
0x000001f55ae42b40, 0x000001f57a9a3470, 0x000001f57a9a49a0, 0x000001f57d539900,
0x000001f57d53c1e0, 0x000001f57d53cab0, 0x000001f57d53db20, 0x000001f57d53e720,
0x000001f57d540510, 0x000001f57d540a30, 0x000001f57d6a9650, 0x000001f57d6be0a0,
0x000001f50c1d80a0, 0x000001f50ceb6610
}

Java Threads: ( => current thread )
=>0x000001f55ae42b40 JavaThread "main" [_thread_in_vm, id=4328, stack(0x00000017da600000,0x00000017da700000)]
  0x000001f57a9a3470 JavaThread "Reference Handler" daemon [_thread_blocked, id=32904, stack(0x00000017dad00000,0x00000017dae00000)]
  0x000001f57a9a49a0 JavaThread "Finalizer" daemon [_thread_blocked, id=37576, stack(0x00000017dae00000,0x00000017daf00000)]
  0x000001f57d539900 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=22252, stack(0x00000017daf00000,0x00000017db000000)]
  0x000001f57d53c1e0 JavaThread "Attach Listener" daemon [_thread_blocked, id=21472, stack(0x00000017db000000,0x00000017db100000)]
  0x000001f57d53cab0 JavaThread "Service Thread" daemon [_thread_blocked, id=44128, stack(0x00000017db100000,0x00000017db200000)]
  0x000001f57d53db20 JavaThread "Monitor Deflation Thread" daemon [_thread_blocked, id=43904, stack(0x00000017db200000,0x00000017db300000)]
  0x000001f57d53e720 JavaThread "C2 CompilerThread0" daemon [_thread_blocked, id=42676, stack(0x00000017db300000,0x00000017db400000)]
  0x000001f57d540510 JavaThread "C1 CompilerThread0" daemon [_thread_blocked, id=18592, stack(0x00000017db400000,0x00000017db500000)]
  0x000001f57d540a30 JavaThread "Sweeper thread" daemon [_thread_blocked, id=11960, stack(0x00000017db500000,0x00000017db600000)]
  0x000001f57d6a9650 JavaThread "Notification Thread" daemon [_thread_blocked, id=14480, stack(0x00000017db600000,0x00000017db700000)]
  0x000001f57d6be0a0 JavaThread "Common-Cleaner" daemon [_thread_blocked, id=14972, stack(0x00000017db800000,0x00000017db900000)]
  0x000001f50c1d80a0 JavaThread "C2 CompilerThread1" daemon [_thread_blocked, id=32208, stack(0x00000017db900000,0x00000017dba00000)]
  0x000001f50ceb6610 JavaThread "pool-1-thread-1" [_thread_blocked, id=22444, stack(0x00000017dba00000,0x00000017dbb00000)]

Other Threads:
  0x000001f57d4eea30 VMThread "VM Thread" [stack: 0x00000017dac00000,0x00000017dad00000] [id=10016]
  0x000001f57d4d0df0 WatcherThread [stack: 0x00000017db700000,0x00000017db800000] [id=10620]
  0x000001f55aeef690 GCTaskThread "GC Thread#0" [stack: 0x00000017da700000,0x00000017da800000] [id=45548]
  0x000001f50c5c9880 GCTaskThread "GC Thread#1" [stack: 0x00000017dbb00000,0x00000017dbc00000] [id=25588]
  0x000001f50cd8e610 GCTaskThread "GC Thread#2" [stack: 0x00000017dbc00000,0x00000017dbd00000] [id=32240]
  0x000001f50ca1f9f0 GCTaskThread "GC Thread#3" [stack: 0x00000017dbd00000,0x00000017dbe00000] [id=34592]
  0x000001f50c9afed0 GCTaskThread "GC Thread#4" [stack: 0x00000017dbe00000,0x00000017dbf00000] [id=39268]
  0x000001f50c35df80 GCTaskThread "GC Thread#5" [stack: 0x00000017dbf00000,0x00000017dc000000] [id=45100]
  0x000001f50c35e240 GCTaskThread "GC Thread#6" [stack: 0x00000017dc000000,0x00000017dc100000] [id=9460]
  0x000001f50ca2fdd0 GCTaskThread "GC Thread#7" [stack: 0x00000017dc100000,0x00000017dc200000] [id=12272]
  0x000001f50ca30460 GCTaskThread "GC Thread#8" [stack: 0x00000017dc200000,0x00000017dc300000] [id=42716]
  0x000001f50ca30720 GCTaskThread "GC Thread#9" [stack: 0x00000017dc300000,0x00000017dc400000] [id=27388]
  0x000001f57a7b4050 ConcurrentGCThread "G1 Main Marker" [stack: 0x00000017da800000,0x00000017da900000] [id=1128]
  0x000001f57a7b4980 ConcurrentGCThread "G1 Conc#0" [stack: 0x00000017da900000,0x00000017daa00000] [id=25612]
  0x000001f57a891bb0 ConcurrentGCThread "G1 Refine#0" [stack: 0x00000017daa00000,0x00000017dab00000] [id=8508]
  0x000001f57a8924f0 ConcurrentGCThread "G1 Service" [stack: 0x00000017dab00000,0x00000017dac00000] [id=22736]

Threads with active compile tasks:

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x000001f55ae3e5e0] Metaspace_lock - owner thread: 0x000001f55ae42b40

Heap address: 0x0000000601c00000, size: 8164 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) not mapped
Compressed class space mapped at: 0x0000000800000000-0x0000000840000000, reserved size: 1073741824
Narrow klass base: 0x0000000800000000, Narrow klass shift: 0, Narrow klass range: 0x40000000

GC Precious Log:
 CPUs: 12 total, 12 available
 Memory: 32648M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (Zero based)
 Heap Region Size: 4M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 512M
 Heap Max Capacity: 8164M
 Pre-touch: Disabled
 Parallel Workers: 10
 Concurrent Workers: 3
 Concurrent Refinement Workers: 10
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 524288K, used 10173K [0x0000000601c00000, 0x0000000800000000)
  region size 4096K, 2 young (8192K), 1 survivors (4096K)
 Metaspace       used 12572K, committed 12800K, reserved 1114112K
  class space    used 1401K, committed 1536K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, OA=open archive, CA=closed archive, TAMS=top-at-mark-start (previous, next)
|   0|0x0000000601c00000, 0x0000000601c00000, 0x0000000602000000|  0%| F|  |TAMS 0x0000000601c00000, 0x0000000601c00000| Untracked 
|   1|0x0000000602000000, 0x0000000602400000, 0x0000000602400000|100%|HS|  |TAMS 0x0000000602000000, 0x0000000602000000| Complete 
|   2|0x0000000602400000, 0x00000006025ef600, 0x0000000602800000| 48%| O|  |TAMS 0x0000000602400000, 0x0000000602400000| Untracked 
|   3|0x0000000602800000, 0x0000000602800000, 0x0000000602c00000|  0%| F|  |TAMS 0x0000000602800000, 0x0000000602800000| Untracked 
|   4|0x0000000602c00000, 0x0000000602c00000, 0x0000000603000000|  0%| F|  |TAMS 0x0000000602c00000, 0x0000000602c00000| Untracked 
|   5|0x0000000603000000, 0x0000000603000000, 0x0000000603400000|  0%| F|  |TAMS 0x0000000603000000, 0x0000000603000000| Untracked 
|   6|0x0000000603400000, 0x0000000603400000, 0x0000000603800000|  0%| F|  |TAMS 0x0000000603400000, 0x0000000603400000| Untracked 
|   7|0x0000000603800000, 0x0000000603800000, 0x0000000603c00000|  0%| F|  |TAMS 0x0000000603800000, 0x0000000603800000| Untracked 
|   8|0x0000000603c00000, 0x0000000603c00000, 0x0000000604000000|  0%| F|  |TAMS 0x0000000603c00000, 0x0000000603c00000| Untracked 
|   9|0x0000000604000000, 0x0000000604000000, 0x0000000604400000|  0%| F|  |TAMS 0x0000000604000000, 0x0000000604000000| Untracked 
|  10|0x0000000604400000, 0x0000000604400000, 0x0000000604800000|  0%| F|  |TAMS 0x0000000604400000, 0x0000000604400000| Untracked 
|  11|0x0000000604800000, 0x0000000604800000, 0x0000000604c00000|  0%| F|  |TAMS 0x0000000604800000, 0x0000000604800000| Untracked 
|  12|0x0000000604c00000, 0x0000000604c00000, 0x0000000605000000|  0%| F|  |TAMS 0x0000000604c00000, 0x0000000604c00000| Untracked 
|  13|0x0000000605000000, 0x0000000605000000, 0x0000000605400000|  0%| F|  |TAMS 0x0000000605000000, 0x0000000605000000| Untracked 
|  14|0x0000000605400000, 0x0000000605400000, 0x0000000605800000|  0%| F|  |TAMS 0x0000000605400000, 0x0000000605400000| Untracked 
|  15|0x0000000605800000, 0x0000000605800000, 0x0000000605c00000|  0%| F|  |TAMS 0x0000000605800000, 0x0000000605800000| Untracked 
|  16|0x0000000605c00000, 0x0000000605c00000, 0x0000000606000000|  0%| F|  |TAMS 0x0000000605c00000, 0x0000000605c00000| Untracked 
|  17|0x0000000606000000, 0x0000000606000000, 0x0000000606400000|  0%| F|  |TAMS 0x0000000606000000, 0x0000000606000000| Untracked 
|  18|0x0000000606400000, 0x0000000606400000, 0x0000000606800000|  0%| F|  |TAMS 0x0000000606400000, 0x0000000606400000| Untracked 
|  19|0x0000000606800000, 0x0000000606800000, 0x0000000606c00000|  0%| F|  |TAMS 0x0000000606800000, 0x0000000606800000| Untracked 
|  20|0x0000000606c00000, 0x0000000606c00000, 0x0000000607000000|  0%| F|  |TAMS 0x0000000606c00000, 0x0000000606c00000| Untracked 
|  21|0x0000000607000000, 0x0000000607000000, 0x0000000607400000|  0%| F|  |TAMS 0x0000000607000000, 0x0000000607000000| Untracked 
|  22|0x0000000607400000, 0x0000000607400000, 0x0000000607800000|  0%| F|  |TAMS 0x0000000607400000, 0x0000000607400000| Untracked 
|  23|0x0000000607800000, 0x0000000607800000, 0x0000000607c00000|  0%| F|  |TAMS 0x0000000607800000, 0x0000000607800000| Untracked 
|  24|0x0000000607c00000, 0x0000000607c00000, 0x0000000608000000|  0%| F|  |TAMS 0x0000000607c00000, 0x0000000607c00000| Untracked 
|  25|0x0000000608000000, 0x0000000608000000, 0x0000000608400000|  0%| F|  |TAMS 0x0000000608000000, 0x0000000608000000| Untracked 
|  26|0x0000000608400000, 0x0000000608400000, 0x0000000608800000|  0%| F|  |TAMS 0x0000000608400000, 0x0000000608400000| Untracked 
|  27|0x0000000608800000, 0x0000000608800000, 0x0000000608c00000|  0%| F|  |TAMS 0x0000000608800000, 0x0000000608800000| Untracked 
|  28|0x0000000608c00000, 0x0000000608c00000, 0x0000000609000000|  0%| F|  |TAMS 0x0000000608c00000, 0x0000000608c00000| Untracked 
|  29|0x0000000609000000, 0x0000000609000000, 0x0000000609400000|  0%| F|  |TAMS 0x0000000609000000, 0x0000000609000000| Untracked 
|  30|0x0000000609400000, 0x0000000609400000, 0x0000000609800000|  0%| F|  |TAMS 0x0000000609400000, 0x0000000609400000| Untracked 
|  31|0x0000000609800000, 0x0000000609800000, 0x0000000609c00000|  0%| F|  |TAMS 0x0000000609800000, 0x0000000609800000| Untracked 
|  32|0x0000000609c00000, 0x0000000609c00000, 0x000000060a000000|  0%| F|  |TAMS 0x0000000609c00000, 0x0000000609c00000| Untracked 
|  33|0x000000060a000000, 0x000000060a000000, 0x000000060a400000|  0%| F|  |TAMS 0x000000060a000000, 0x000000060a000000| Untracked 
|  34|0x000000060a400000, 0x000000060a400000, 0x000000060a800000|  0%| F|  |TAMS 0x000000060a400000, 0x000000060a400000| Untracked 
|  35|0x000000060a800000, 0x000000060a800000, 0x000000060ac00000|  0%| F|  |TAMS 0x000000060a800000, 0x000000060a800000| Untracked 
|  36|0x000000060ac00000, 0x000000060ac00000, 0x000000060b000000|  0%| F|  |TAMS 0x000000060ac00000, 0x000000060ac00000| Untracked 
|  37|0x000000060b000000, 0x000000060b000000, 0x000000060b400000|  0%| F|  |TAMS 0x000000060b000000, 0x000000060b000000| Untracked 
|  38|0x000000060b400000, 0x000000060b400000, 0x000000060b800000|  0%| F|  |TAMS 0x000000060b400000, 0x000000060b400000| Untracked 
|  39|0x000000060b800000, 0x000000060b800000, 0x000000060bc00000|  0%| F|  |TAMS 0x000000060b800000, 0x000000060b800000| Untracked 
|  40|0x000000060bc00000, 0x000000060bc00000, 0x000000060c000000|  0%| F|  |TAMS 0x000000060bc00000, 0x000000060bc00000| Untracked 
|  41|0x000000060c000000, 0x000000060c000000, 0x000000060c400000|  0%| F|  |TAMS 0x000000060c000000, 0x000000060c000000| Untracked 
|  42|0x000000060c400000, 0x000000060c400000, 0x000000060c800000|  0%| F|  |TAMS 0x000000060c400000, 0x000000060c400000| Untracked 
|  43|0x000000060c800000, 0x000000060c800000, 0x000000060cc00000|  0%| F|  |TAMS 0x000000060c800000, 0x000000060c800000| Untracked 
|  44|0x000000060cc00000, 0x000000060cc00000, 0x000000060d000000|  0%| F|  |TAMS 0x000000060cc00000, 0x000000060cc00000| Untracked 
|  45|0x000000060d000000, 0x000000060d000000, 0x000000060d400000|  0%| F|  |TAMS 0x000000060d000000, 0x000000060d000000| Untracked 
|  46|0x000000060d400000, 0x000000060d400000, 0x000000060d800000|  0%| F|  |TAMS 0x000000060d400000, 0x000000060d400000| Untracked 
|  47|0x000000060d800000, 0x000000060d800000, 0x000000060dc00000|  0%| F|  |TAMS 0x000000060d800000, 0x000000060d800000| Untracked 
|  48|0x000000060dc00000, 0x000000060dc00000, 0x000000060e000000|  0%| F|  |TAMS 0x000000060dc00000, 0x000000060dc00000| Untracked 
|  49|0x000000060e000000, 0x000000060e000000, 0x000000060e400000|  0%| F|  |TAMS 0x000000060e000000, 0x000000060e000000| Untracked 
|  50|0x000000060e400000, 0x000000060e400000, 0x000000060e800000|  0%| F|  |TAMS 0x000000060e400000, 0x000000060e400000| Untracked 
|  51|0x000000060e800000, 0x000000060e800000, 0x000000060ec00000|  0%| F|  |TAMS 0x000000060e800000, 0x000000060e800000| Untracked 
|  52|0x000000060ec00000, 0x000000060ec00000, 0x000000060f000000|  0%| F|  |TAMS 0x000000060ec00000, 0x000000060ec00000| Untracked 
|  53|0x000000060f000000, 0x000000060f000000, 0x000000060f400000|  0%| F|  |TAMS 0x000000060f000000, 0x000000060f000000| Untracked 
|  54|0x000000060f400000, 0x000000060f400000, 0x000000060f800000|  0%| F|  |TAMS 0x000000060f400000, 0x000000060f400000| Untracked 
|  55|0x000000060f800000, 0x000000060f800000, 0x000000060fc00000|  0%| F|  |TAMS 0x000000060f800000, 0x000000060f800000| Untracked 
|  56|0x000000060fc00000, 0x000000060fc00000, 0x0000000610000000|  0%| F|  |TAMS 0x000000060fc00000, 0x000000060fc00000| Untracked 
|  57|0x0000000610000000, 0x0000000610000000, 0x0000000610400000|  0%| F|  |TAMS 0x0000000610000000, 0x0000000610000000| Untracked 
|  58|0x0000000610400000, 0x0000000610400000, 0x0000000610800000|  0%| F|  |TAMS 0x0000000610400000, 0x0000000610400000| Untracked 
|  59|0x0000000610800000, 0x0000000610800000, 0x0000000610c00000|  0%| F|  |TAMS 0x0000000610800000, 0x0000000610800000| Untracked 
|  60|0x0000000610c00000, 0x0000000610c00000, 0x0000000611000000|  0%| F|  |TAMS 0x0000000610c00000, 0x0000000610c00000| Untracked 
|  61|0x0000000611000000, 0x0000000611000000, 0x0000000611400000|  0%| F|  |TAMS 0x0000000611000000, 0x0000000611000000| Untracked 
|  62|0x0000000611400000, 0x0000000611400000, 0x0000000611800000|  0%| F|  |TAMS 0x0000000611400000, 0x0000000611400000| Untracked 
|  63|0x0000000611800000, 0x0000000611800000, 0x0000000611c00000|  0%| F|  |TAMS 0x0000000611800000, 0x0000000611800000| Untracked 
|  64|0x0000000611c00000, 0x0000000611c00000, 0x0000000612000000|  0%| F|  |TAMS 0x0000000611c00000, 0x0000000611c00000| Untracked 
|  65|0x0000000612000000, 0x0000000612000000, 0x0000000612400000|  0%| F|  |TAMS 0x0000000612000000, 0x0000000612000000| Untracked 
|  66|0x0000000612400000, 0x0000000612400000, 0x0000000612800000|  0%| F|  |TAMS 0x0000000612400000, 0x0000000612400000| Untracked 
|  67|0x0000000612800000, 0x0000000612800000, 0x0000000612c00000|  0%| F|  |TAMS 0x0000000612800000, 0x0000000612800000| Untracked 
|  68|0x0000000612c00000, 0x0000000612c00000, 0x0000000613000000|  0%| F|  |TAMS 0x0000000612c00000, 0x0000000612c00000| Untracked 
|  69|0x0000000613000000, 0x0000000613000000, 0x0000000613400000|  0%| F|  |TAMS 0x0000000613000000, 0x0000000613000000| Untracked 
|  70|0x0000000613400000, 0x0000000613400000, 0x0000000613800000|  0%| F|  |TAMS 0x0000000613400000, 0x0000000613400000| Untracked 
|  71|0x0000000613800000, 0x0000000613800000, 0x0000000613c00000|  0%| F|  |TAMS 0x0000000613800000, 0x0000000613800000| Untracked 
|  72|0x0000000613c00000, 0x0000000613c00000, 0x0000000614000000|  0%| F|  |TAMS 0x0000000613c00000, 0x0000000613c00000| Untracked 
|  73|0x0000000614000000, 0x0000000614000000, 0x0000000614400000|  0%| F|  |TAMS 0x0000000614000000, 0x0000000614000000| Untracked 
|  74|0x0000000614400000, 0x0000000614400000, 0x0000000614800000|  0%| F|  |TAMS 0x0000000614400000, 0x0000000614400000| Untracked 
|  75|0x0000000614800000, 0x0000000614800000, 0x0000000614c00000|  0%| F|  |TAMS 0x0000000614800000, 0x0000000614800000| Untracked 
|  76|0x0000000614c00000, 0x0000000614c00000, 0x0000000615000000|  0%| F|  |TAMS 0x0000000614c00000, 0x0000000614c00000| Untracked 
|  77|0x0000000615000000, 0x0000000615000000, 0x0000000615400000|  0%| F|  |TAMS 0x0000000615000000, 0x0000000615000000| Untracked 
|  78|0x0000000615400000, 0x0000000615400000, 0x0000000615800000|  0%| F|  |TAMS 0x0000000615400000, 0x0000000615400000| Untracked 
|  79|0x0000000615800000, 0x0000000615800000, 0x0000000615c00000|  0%| F|  |TAMS 0x0000000615800000, 0x0000000615800000| Untracked 
|  80|0x0000000615c00000, 0x0000000615c00000, 0x0000000616000000|  0%| F|  |TAMS 0x0000000615c00000, 0x0000000615c00000| Untracked 
|  81|0x0000000616000000, 0x0000000616000000, 0x0000000616400000|  0%| F|  |TAMS 0x0000000616000000, 0x0000000616000000| Untracked 
|  82|0x0000000616400000, 0x0000000616400000, 0x0000000616800000|  0%| F|  |TAMS 0x0000000616400000, 0x0000000616400000| Untracked 
|  83|0x0000000616800000, 0x0000000616800000, 0x0000000616c00000|  0%| F|  |TAMS 0x0000000616800000, 0x0000000616800000| Untracked 
|  84|0x0000000616c00000, 0x0000000616c00000, 0x0000000617000000|  0%| F|  |TAMS 0x0000000616c00000, 0x0000000616c00000| Untracked 
|  85|0x0000000617000000, 0x0000000617000000, 0x0000000617400000|  0%| F|  |TAMS 0x0000000617000000, 0x0000000617000000| Untracked 
|  86|0x0000000617400000, 0x0000000617400000, 0x0000000617800000|  0%| F|  |TAMS 0x0000000617400000, 0x0000000617400000| Untracked 
|  87|0x0000000617800000, 0x0000000617800000, 0x0000000617c00000|  0%| F|  |TAMS 0x0000000617800000, 0x0000000617800000| Untracked 
|  88|0x0000000617c00000, 0x0000000617c00000, 0x0000000618000000|  0%| F|  |TAMS 0x0000000617c00000, 0x0000000617c00000| Untracked 
|  89|0x0000000618000000, 0x0000000618000000, 0x0000000618400000|  0%| F|  |TAMS 0x0000000618000000, 0x0000000618000000| Untracked 
|  90|0x0000000618400000, 0x0000000618400000, 0x0000000618800000|  0%| F|  |TAMS 0x0000000618400000, 0x0000000618400000| Untracked 
|  91|0x0000000618800000, 0x0000000618800000, 0x0000000618c00000|  0%| F|  |TAMS 0x0000000618800000, 0x0000000618800000| Untracked 
|  92|0x0000000618c00000, 0x0000000618c00000, 0x0000000619000000|  0%| F|  |TAMS 0x0000000618c00000, 0x0000000618c00000| Untracked 
|  93|0x0000000619000000, 0x0000000619000000, 0x0000000619400000|  0%| F|  |TAMS 0x0000000619000000, 0x0000000619000000| Untracked 
|  94|0x0000000619400000, 0x0000000619400000, 0x0000000619800000|  0%| F|  |TAMS 0x0000000619400000, 0x0000000619400000| Untracked 
|  95|0x0000000619800000, 0x0000000619800000, 0x0000000619c00000|  0%| F|  |TAMS 0x0000000619800000, 0x0000000619800000| Untracked 
|  96|0x0000000619c00000, 0x0000000619c00000, 0x000000061a000000|  0%| F|  |TAMS 0x0000000619c00000, 0x0000000619c00000| Untracked 
|  97|0x000000061a000000, 0x000000061a000000, 0x000000061a400000|  0%| F|  |TAMS 0x000000061a000000, 0x000000061a000000| Untracked 
|  98|0x000000061a400000, 0x000000061a400000, 0x000000061a800000|  0%| F|  |TAMS 0x000000061a400000, 0x000000061a400000| Untracked 
|  99|0x000000061a800000, 0x000000061a800000, 0x000000061ac00000|  0%| F|  |TAMS 0x000000061a800000, 0x000000061a800000| Untracked 
| 100|0x000000061ac00000, 0x000000061ac00000, 0x000000061b000000|  0%| F|  |TAMS 0x000000061ac00000, 0x000000061ac00000| Untracked 
| 101|0x000000061b000000, 0x000000061b000000, 0x000000061b400000|  0%| F|  |TAMS 0x000000061b000000, 0x000000061b000000| Untracked 
| 102|0x000000061b400000, 0x000000061b400000, 0x000000061b800000|  0%| F|  |TAMS 0x000000061b400000, 0x000000061b400000| Untracked 
| 103|0x000000061b800000, 0x000000061b800000, 0x000000061bc00000|  0%| F|  |TAMS 0x000000061b800000, 0x000000061b800000| Untracked 
| 104|0x000000061bc00000, 0x000000061bc00000, 0x000000061c000000|  0%| F|  |TAMS 0x000000061bc00000, 0x000000061bc00000| Untracked 
| 105|0x000000061c000000, 0x000000061c000000, 0x000000061c400000|  0%| F|  |TAMS 0x000000061c000000, 0x000000061c000000| Untracked 
| 106|0x000000061c400000, 0x000000061c400000, 0x000000061c800000|  0%| F|  |TAMS 0x000000061c400000, 0x000000061c400000| Untracked 
| 107|0x000000061c800000, 0x000000061c800000, 0x000000061cc00000|  0%| F|  |TAMS 0x000000061c800000, 0x000000061c800000| Untracked 
| 108|0x000000061cc00000, 0x000000061cc00000, 0x000000061d000000|  0%| F|  |TAMS 0x000000061cc00000, 0x000000061cc00000| Untracked 
| 109|0x000000061d000000, 0x000000061d000000, 0x000000061d400000|  0%| F|  |TAMS 0x000000061d000000, 0x000000061d000000| Untracked 
| 110|0x000000061d400000, 0x000000061d400000, 0x000000061d800000|  0%| F|  |TAMS 0x000000061d400000, 0x000000061d400000| Untracked 
| 111|0x000000061d800000, 0x000000061d800000, 0x000000061dc00000|  0%| F|  |TAMS 0x000000061d800000, 0x000000061d800000| Untracked 
| 112|0x000000061dc00000, 0x000000061dc00000, 0x000000061e000000|  0%| F|  |TAMS 0x000000061dc00000, 0x000000061dc00000| Untracked 
| 113|0x000000061e000000, 0x000000061e000000, 0x000000061e400000|  0%| F|  |TAMS 0x000000061e000000, 0x000000061e000000| Untracked 
| 114|0x000000061e400000, 0x000000061e400000, 0x000000061e800000|  0%| F|  |TAMS 0x000000061e400000, 0x000000061e400000| Untracked 
| 115|0x000000061e800000, 0x000000061e800000, 0x000000061ec00000|  0%| F|  |TAMS 0x000000061e800000, 0x000000061e800000| Untracked 
| 116|0x000000061ec00000, 0x000000061ec00000, 0x000000061f000000|  0%| F|  |TAMS 0x000000061ec00000, 0x000000061ec00000| Untracked 
| 117|0x000000061f000000, 0x000000061f000000, 0x000000061f400000|  0%| F|  |TAMS 0x000000061f000000, 0x000000061f000000| Untracked 
| 118|0x000000061f400000, 0x000000061f400000, 0x000000061f800000|  0%| F|  |TAMS 0x000000061f400000, 0x000000061f400000| Untracked 
| 119|0x000000061f800000, 0x000000061f800000, 0x000000061fc00000|  0%| F|  |TAMS 0x000000061f800000, 0x000000061f800000| Untracked 
| 120|0x000000061fc00000, 0x000000061fc00000, 0x0000000620000000|  0%| F|  |TAMS 0x000000061fc00000, 0x000000061fc00000| Untracked 
| 121|0x0000000620000000, 0x0000000620400000, 0x0000000620400000|100%| S|CS|TAMS 0x0000000620000000, 0x0000000620000000| Complete 
| 122|0x0000000620400000, 0x0000000620400000, 0x0000000620800000|  0%| F|  |TAMS 0x0000000620400000, 0x0000000620400000| Untracked 
| 123|0x0000000620800000, 0x0000000620800000, 0x0000000620c00000|  0%| F|  |TAMS 0x0000000620800000, 0x0000000620800000| Untracked 
| 124|0x0000000620c00000, 0x0000000620c00000, 0x0000000621000000|  0%| F|  |TAMS 0x0000000620c00000, 0x0000000620c00000| Untracked 
| 125|0x0000000621000000, 0x0000000621000000, 0x0000000621400000|  0%| F|  |TAMS 0x0000000621000000, 0x0000000621000000| Untracked 
| 126|0x0000000621400000, 0x0000000621400000, 0x0000000621800000|  0%| F|  |TAMS 0x0000000621400000, 0x0000000621400000| Untracked 
| 127|0x0000000621800000, 0x0000000621bdec80, 0x0000000621c00000| 96%| E|  |TAMS 0x0000000621800000, 0x0000000621800000| Complete 

Card table byte_map: [0x000001f570790000,0x000001f571790000] _byte_map_base: 0x000001f56d782000

Marking Bits (Prev, Next): (CMBitMap*) 0x000001f55aeefbc0, (CMBitMap*) 0x000001f55aeefc00
 Prev Bits: [0x000001f572790000, 0x000001f57a720000)
 Next Bits: [0x000001f500000000, 0x000001f507f90000)

Polling page: 0x000001f55af00000

Metaspace:

Usage:
  Non-class:     10.91 MB used.
      Class:      1.37 MB used.
       Both:     12.28 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,      11.00 MB ( 17%) committed,  1 nodes.
      Class space:        1.00 GB reserved,       1.50 MB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,      12.50 MB (  1%) committed. 

Chunk freelists:
   Non-Class:  4.23 MB
       Class:  14.42 MB
        Both:  18.66 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 21.00 MB
CDS: off
MetaspaceReclaimPolicy: balanced
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - new_chunks_are_fully_committed: 0.
 - uncommit_free_chunks: 1.
 - use_allocation_guard: 0.
 - handle_deallocations: 1.


Internal statistics:

num_allocs_failed_limit: 0.
num_arena_births: 62.
num_arena_deaths: 0.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 200.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 0.
num_chunks_taken_from_freelist: 214.
num_chunk_merges: 0.
num_chunk_splits: 136.
num_chunks_enlarged: 111.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=382Kb max_used=382Kb free=119617Kb
 bounds [0x000001f5675c0000, 0x000001f567830000, 0x000001f56eaf0000]
CodeHeap 'profiled nmethods': size=120000Kb used=1729Kb max_used=1729Kb free=118270Kb
 bounds [0x000001f55faf0000, 0x000001f55fd60000, 0x000001f567020000]
CodeHeap 'non-nmethods': size=5760Kb used=1139Kb max_used=1154Kb free=4621Kb
 bounds [0x000001f567020000, 0x000001f567290000, 0x000001f5675c0000]
 total_blobs=1366 nmethods=941 adapters=338
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 0.546 Thread 0x000001f57d53e720 nmethod 930 0x000001f56761de90 code [0x000001f56761e000, 0x000001f56761e078]
Event: 0.547 Thread 0x000001f57d540510  931       3       org.gradle.launcher.cli.converter.LayoutToPropertiesConverter$1::isSatisfiedBy (9 bytes)
Event: 0.548 Thread 0x000001f57d540510 nmethod 931 0x000001f55fc9d610 code [0x000001f55fc9d7e0, 0x000001f55fc9dd98]
Event: 0.548 Thread 0x000001f57d540510  932       3       org.gradle.launcher.cli.converter.LayoutToPropertiesConverter$1::isSatisfiedBy (30 bytes)
Event: 0.548 Thread 0x000001f57d540510 nmethod 932 0x000001f55fc9df90 code [0x000001f55fc9e160, 0x000001f55fc9e598]
Event: 0.548 Thread 0x000001f57d540510  933       1       java.util.concurrent.ConcurrentHashMap$MapEntry::getKey (5 bytes)
Event: 0.548 Thread 0x000001f57d540510 nmethod 933 0x000001f56761e190 code [0x000001f56761e320, 0x000001f56761e3f8]
Event: 0.548 Thread 0x000001f57d540510  934       1       java.util.concurrent.ConcurrentHashMap$MapEntry::getValue (5 bytes)
Event: 0.548 Thread 0x000001f57d540510 nmethod 934 0x000001f56761e490 code [0x000001f56761e620, 0x000001f56761e6f8]
Event: 0.548 Thread 0x000001f57d540510  935       3       java.util.jar.Manifest$FastInputStream::peek (38 bytes)
Event: 0.549 Thread 0x000001f57d540510 nmethod 935 0x000001f55fc9e710 code [0x000001f55fc9e8c0, 0x000001f55fc9eae8]
Event: 0.551 Thread 0x000001f50c1d80a0  936       4       java.lang.CharacterDataLatin1::toUpperCase (53 bytes)
Event: 0.552 Thread 0x000001f50c1d80a0 nmethod 936 0x000001f56761e790 code [0x000001f56761e900, 0x000001f56761e9f8]
Event: 0.553 Thread 0x000001f57d540510  938       3       java.util.HashMap::getNode (150 bytes)
Event: 0.554 Thread 0x000001f57d540510 nmethod 938 0x000001f55fc9ec10 code [0x000001f55fc9ee40, 0x000001f55fc9fa38]
Event: 0.554 Thread 0x000001f57d540510  939       3       java.util.Arrays$ArrayItr::hasNext (18 bytes)
Event: 0.554 Thread 0x000001f57d540510 nmethod 939 0x000001f55fc9fd10 code [0x000001f55fc9fea0, 0x000001f55fca0018]
Event: 0.554 Thread 0x000001f57d540510  940       3       java.util.HashMap::get (19 bytes)
Event: 0.554 Thread 0x000001f57d540510 nmethod 940 0x000001f55fca0110 code [0x000001f55fca02c0, 0x000001f55fca0488]
Event: 0.559 Thread 0x000001f57d53e720  941       4       java.util.concurrent.ConcurrentHashMap::get (162 bytes)

GC Heap History (2 events):
Event: 0.504 GC heap before
{Heap before GC invocations=0 (full 0):
 garbage-first heap   total 524288K, used 28672K [0x0000000601c00000, 0x0000000800000000)
  region size 4096K, 6 young (24576K), 0 survivors (0K)
 Metaspace       used 11648K, committed 11776K, reserved 1114112K
  class space    used 1244K, committed 1280K, reserved 1048576K
}
Event: 0.507 GC heap after
{Heap after GC invocations=1 (full 0):
 garbage-first heap   total 524288K, used 10173K [0x0000000601c00000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 11648K, committed 11776K, reserved 1114112K
  class space    used 1244K, committed 1280K, reserved 1048576K
}

Dll operation events (2 events):
Event: 0.003 Loaded shared library C:\Program Files\Android\Android Studio\jbr\bin\java.dll
Event: 0.078 Loaded shared library C:\Program Files\Android\Android Studio\jbr\bin\verify.dll

Deoptimization events (20 events):
Event: 0.444 Thread 0x000001f55ae42b40 Uncommon trap: trap_request=0xffffffde fr.pc=0x000001f5675dc33c relative=0x00000000000003dc
Event: 0.444 Thread 0x000001f55ae42b40 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000001f5675dc33c method=java.util.regex.Matcher.match(II)Z @ 121 c2
Event: 0.444 Thread 0x000001f55ae42b40 DEOPT PACKING pc=0x000001f5675dc33c sp=0x00000017da6fd1e0
Event: 0.444 Thread 0x000001f55ae42b40 DEOPT UNPACKING pc=0x000001f5670769a3 sp=0x00000017da6fd188 mode 2
Event: 0.444 Thread 0x000001f55ae42b40 Uncommon trap: trap_request=0xffffffde fr.pc=0x000001f5675dc33c relative=0x00000000000003dc
Event: 0.444 Thread 0x000001f55ae42b40 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000001f5675dc33c method=java.util.regex.Matcher.match(II)Z @ 121 c2
Event: 0.444 Thread 0x000001f55ae42b40 DEOPT PACKING pc=0x000001f5675dc33c sp=0x00000017da6fd1e0
Event: 0.444 Thread 0x000001f55ae42b40 DEOPT UNPACKING pc=0x000001f5670769a3 sp=0x00000017da6fd188 mode 2
Event: 0.493 Thread 0x000001f55ae42b40 DEOPT PACKING pc=0x000001f55fbd428b sp=0x00000017da6f7840
Event: 0.493 Thread 0x000001f55ae42b40 DEOPT UNPACKING pc=0x000001f567077143 sp=0x00000017da6f6ca0 mode 0
Event: 0.530 Thread 0x000001f55ae42b40 DEOPT PACKING pc=0x000001f55fb551d6 sp=0x00000017da6f7d30
Event: 0.530 Thread 0x000001f55ae42b40 DEOPT UNPACKING pc=0x000001f567077143 sp=0x00000017da6f71b8 mode 0
Event: 0.553 Thread 0x000001f55ae42b40 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000001f5676096a0 relative=0x00000000000005e0
Event: 0.553 Thread 0x000001f55ae42b40 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000001f5676096a0 method=java.util.HashMap.hash(Ljava/lang/Object;)I @ 1 c2
Event: 0.553 Thread 0x000001f55ae42b40 DEOPT PACKING pc=0x000001f5676096a0 sp=0x00000017da6fccd0
Event: 0.553 Thread 0x000001f55ae42b40 DEOPT UNPACKING pc=0x000001f5670769a3 sp=0x00000017da6fcb78 mode 2
Event: 0.553 Thread 0x000001f55ae42b40 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000001f5675fdb9c relative=0x00000000000005dc
Event: 0.553 Thread 0x000001f55ae42b40 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000001f5675fdb9c method=java.util.HashMap.hash(Ljava/lang/Object;)I @ 1 c2
Event: 0.553 Thread 0x000001f55ae42b40 DEOPT PACKING pc=0x000001f5675fdb9c sp=0x00000017da6fcc60
Event: 0.553 Thread 0x000001f55ae42b40 DEOPT UNPACKING pc=0x000001f5670769a3 sp=0x00000017da6fcb78 mode 2

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (13 events):
Event: 0.165 Thread 0x000001f55ae42b40 Exception <a 'java/lang/NoSuchMethodError'{0x0000000621b90b90}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object)'> (0x0000000621b90b90) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 771]
Event: 0.448 Thread 0x000001f55ae42b40 Exception <a 'java/lang/NoSuchMethodError'{0x00000006204fdc00}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object)'> (0x00000006204fdc00) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 771]
Event: 0.461 Thread 0x000001f55ae42b40 Exception <a 'java/lang/NoClassDefFoundError'{0x0000000620583cf8}: org/slf4j/impl/StaticMarkerBinder> (0x0000000620583cf8) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 245]
Event: 0.471 Thread 0x000001f55ae42b40 Exception <a 'java/lang/NoSuchMethodError'{0x000000062061ece0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000062061ece0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 771]
Event: 0.473 Thread 0x000001f55ae42b40 Exception <a 'java/lang/NoSuchMethodError'{0x0000000620630260}: 'java.lang.Object java.lang.invoke.Invokers$Holder.invoker(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000620630260) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 771]
Event: 0.474 Thread 0x000001f55ae42b40 Exception <a 'java/lang/NoSuchMethodError'{0x0000000620639308}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, int)'> (0x0000000620639308) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 771]
Event: 0.476 Thread 0x000001f55ae42b40 Exception <a 'java/lang/NoSuchMethodError'{0x0000000620644ff8}: 'void java.lang.invoke.DelegatingMethodHandle$Holder.delegate(java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x0000000620644ff8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 771]
Event: 0.476 Thread 0x000001f55ae42b40 Exception <a 'java/lang/NoSuchMethodError'{0x0000000620649140}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000620649140) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 771]
Event: 0.478 Thread 0x000001f55ae42b40 Exception <a 'java/lang/NoSuchMethodError'{0x000000062065c2f8}: 'java.lang.Object java.lang.invoke.Invokers$Holder.invokeExact_MT(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000062065c2f8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 771]
Event: 0.542 Thread 0x000001f55ae42b40 Exception <a 'java/lang/NoSuchMethodError'{0x0000000621a87450}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeInterface(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000621a87450) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 771]
Event: 0.544 Thread 0x000001f55ae42b40 Exception <a 'java/lang/NoSuchMethodError'{0x0000000621aa8fd8}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000621aa8fd8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 771]
Event: 0.553 Thread 0x000001f55ae42b40 Implicit null exception at 0x000001f567609110 to 0x000001f567609680
Event: 0.553 Thread 0x000001f55ae42b40 Implicit null exception at 0x000001f5675fd610 to 0x000001f5675fdb7c

VM Operations (8 events):
Event: 0.075 Executing VM operation: HandshakeAllThreads
Event: 0.075 Executing VM operation: HandshakeAllThreads done
Event: 0.390 Executing VM operation: HandshakeAllThreads
Event: 0.390 Executing VM operation: HandshakeAllThreads done
Event: 0.474 Executing VM operation: HandshakeAllThreads
Event: 0.474 Executing VM operation: HandshakeAllThreads done
Event: 0.504 Executing VM operation: G1CollectForAllocation
Event: 0.507 Executing VM operation: G1CollectForAllocation done

Events (20 events):
Event: 0.534 loading class java/util/concurrent/CopyOnWriteArraySet
Event: 0.534 loading class java/util/concurrent/CopyOnWriteArraySet done
Event: 0.539 loading class java/util/concurrent/SynchronousQueue
Event: 0.539 loading class java/util/concurrent/SynchronousQueue done
Event: 0.539 loading class java/util/concurrent/SynchronousQueue$TransferStack
Event: 0.539 loading class java/util/concurrent/SynchronousQueue$Transferer
Event: 0.539 loading class java/util/concurrent/SynchronousQueue$Transferer done
Event: 0.539 loading class java/util/concurrent/SynchronousQueue$TransferStack done
Event: 0.539 loading class java/util/concurrent/SynchronousQueue$TransferStack$SNode
Event: 0.539 loading class java/util/concurrent/SynchronousQueue$TransferStack$SNode done
Event: 0.544 loading class java/nio/file/NoSuchFileException
Event: 0.544 loading class java/nio/file/FileSystemException
Event: 0.544 loading class java/nio/file/FileSystemException done
Event: 0.544 loading class java/nio/file/NoSuchFileException done
Event: 0.555 loading class java/io/InvalidObjectException
Event: 0.555 loading class java/io/ObjectStreamException
Event: 0.555 loading class java/io/ObjectStreamException done
Event: 0.555 loading class java/io/InvalidObjectException done
Event: 0.557 loading class java/math/RoundingMode
Event: 0.557 loading class java/math/RoundingMode done


Dynamic libraries:
0x00007ff70d890000 - 0x00007ff70d89a000 	C:\Program Files\Android\Android Studio\jbr\bin\java.exe
0x00007ffa85d70000 - 0x00007ffa85f68000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ffa84050000 - 0x00007ffa84112000 	C:\Windows\System32\KERNEL32.DLL
0x00007ffa83660000 - 0x00007ffa83956000 	C:\Windows\System32\KERNELBASE.dll
0x00007ffa83a60000 - 0x00007ffa83b60000 	C:\Windows\System32\ucrtbase.dll
0x00007ffa75e60000 - 0x00007ffa75e77000 	C:\Program Files\Android\Android Studio\jbr\bin\jli.dll
0x00007ffa5ca10000 - 0x00007ffa5ca2b000 	C:\Program Files\Android\Android Studio\jbr\bin\VCRUNTIME140.dll
0x00007ffa84250000 - 0x00007ffa843ed000 	C:\Windows\System32\USER32.dll
0x00007ffa66450000 - 0x00007ffa666ea000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5915_none_60b4b9d171f9c7c7\COMCTL32.dll
0x00007ffa83a30000 - 0x00007ffa83a52000 	C:\Windows\System32\win32u.dll
0x00007ffa83e80000 - 0x00007ffa83f1e000 	C:\Windows\System32\msvcrt.dll
0x00007ffa83d90000 - 0x00007ffa83dbb000 	C:\Windows\System32\GDI32.dll
0x00007ffa83bf0000 - 0x00007ffa83d09000 	C:\Windows\System32\gdi32full.dll
0x00007ffa83960000 - 0x00007ffa839fd000 	C:\Windows\System32\msvcp_win.dll
0x00007ffa85290000 - 0x00007ffa852bf000 	C:\Windows\System32\IMM32.DLL
0x00007ffa5ca00000 - 0x00007ffa5ca0c000 	C:\Program Files\Android\Android Studio\jbr\bin\vcruntime140_1.dll
0x00007ffa27c90000 - 0x00007ffa27d1d000 	C:\Program Files\Android\Android Studio\jbr\bin\msvcp140.dll
0x00007ff9dfb30000 - 0x00007ff9e07b3000 	C:\Program Files\Android\Android Studio\jbr\bin\server\jvm.dll
0x00007ffa85bc0000 - 0x00007ffa85c71000 	C:\Windows\System32\ADVAPI32.dll
0x00007ffa83de0000 - 0x00007ffa83e7f000 	C:\Windows\System32\sechost.dll
0x00007ffa84720000 - 0x00007ffa84843000 	C:\Windows\System32\RPCRT4.dll
0x00007ffa83a00000 - 0x00007ffa83a27000 	C:\Windows\System32\bcrypt.dll
0x00007ffa82900000 - 0x00007ffa8294b000 	C:\Windows\SYSTEM32\POWRPROF.dll
0x00007ffa77fc0000 - 0x00007ffa77fe7000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ff992760000 - 0x00007ff992769000 	C:\Windows\SYSTEM32\WSOCK32.dll
0x00007ffa848d0000 - 0x00007ffa8493b000 	C:\Windows\System32\WS2_32.dll
0x00007ffa80210000 - 0x00007ffa8021a000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ffa827c0000 - 0x00007ffa827d2000 	C:\Windows\SYSTEM32\UMPDC.dll
0x00007ffa81c00000 - 0x00007ffa81c12000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007ffa5c790000 - 0x00007ffa5c79a000 	C:\Program Files\Android\Android Studio\jbr\bin\jimage.dll
0x00007ffa819a0000 - 0x00007ffa81ba1000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ffa7b5f0000 - 0x00007ffa7b624000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007ffa83b60000 - 0x00007ffa83be2000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007ffa2e1b0000 - 0x00007ffa2e1d5000 	C:\Program Files\Android\Android Studio\jbr\bin\java.dll
0x00007ffa2e730000 - 0x00007ffa2e748000 	C:\Program Files\Android\Android Studio\jbr\bin\zip.dll
0x00007ffa85320000 - 0x00007ffa85a8e000 	C:\Windows\System32\SHELL32.dll
0x00007ffa81120000 - 0x00007ffa818c4000 	C:\Windows\SYSTEM32\windows.storage.dll
0x00007ffa84950000 - 0x00007ffa84ca3000 	C:\Windows\System32\combase.dll
0x00007ffa82d50000 - 0x00007ffa82d7b000 	C:\Windows\SYSTEM32\Wldp.dll
0x00007ffa85af0000 - 0x00007ffa85bbd000 	C:\Windows\System32\OLEAUT32.dll
0x00007ffa845b0000 - 0x00007ffa8465d000 	C:\Windows\System32\SHCORE.dll
0x00007ffa84660000 - 0x00007ffa846bb000 	C:\Windows\System32\shlwapi.dll
0x00007ffa83330000 - 0x00007ffa83355000 	C:\Windows\SYSTEM32\profapi.dll
0x00007ffa5c6c0000 - 0x00007ffa5c6d9000 	C:\Program Files\Android\Android Studio\jbr\bin\net.dll
0x00007ffa7d510000 - 0x00007ffa7d61a000 	C:\Windows\SYSTEM32\WINHTTP.dll
0x00007ffa82b30000 - 0x00007ffa82b9a000 	C:\Windows\system32\mswsock.dll
0x00007ffa2e190000 - 0x00007ffa2e1a6000 	C:\Program Files\Android\Android Studio\jbr\bin\nio.dll
0x00007ffa31d40000 - 0x00007ffa31d50000 	C:\Program Files\Android\Android Studio\jbr\bin\verify.dll
0x00007ffa2de20000 - 0x00007ffa2de47000 	C:\Users\<USER>\.gradle\native\e1d6ef7f7dcc3fd88c89a11ec53ec762bb8ba0a96d01ffa2cd45eb1d1d8dd5c5\windows-amd64\native-platform.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Program Files\Android\Android Studio\jbr\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5915_none_60b4b9d171f9c7c7;C:\Program Files\Android\Android Studio\jbr\bin\server;C:\Users\<USER>\.gradle\native\e1d6ef7f7dcc3fd88c89a11ec53ec762bb8ba0a96d01ffa2cd45eb1d1d8dd5c5\windows-amd64

VM Arguments:
jvm_args: -Dorg.gradle.appname=gradlew 
java_command: org.gradle.wrapper.GradleWrapperMain -q -Ptarget-platform=android-x64 -Ptarget=C:\Users\<USER>\Documents\GitHub\Ecommerce-App\lib\main.dart -Pbase-application-name=android.app.Application -Pdart-defines=RkxVVFRFUl9XRUJfQ0FOVkFTS0lUX1VSTD1odHRwczovL3d3dy5nc3RhdGljLmNvbS9mbHV0dGVyLWNhbnZhc2tpdC9mNjM0NGI3NWRjZjg2MWQ4YmYxZjEzMjI3ODBiODgxMWY5ODJlMzFhLw== -Pdart-obfuscation=false -Ptrack-widget-creation=true -Ptree-shake-icons=false -Pfilesystem-scheme=org-dartlang-root assembleDebug
java_class_path (initial): C:\Users\<USER>\Documents\GitHub\Ecommerce-App\android\\gradle\wrapper\gradle-wrapper.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 10                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 4194304                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 536870912                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 8560574464                                {product} {ergonomic}
   size_t MaxNewSize                               = 5133828096                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 4194304                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 8560574464                             {manageable} {ergonomic}
     bool UseCompressedClassPointers               = true                           {product lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
JAVA_HOME=C:\Program Files\Android\Android Studio\jbr
CLASSPATH=C:\Users\<USER>\Documents\GitHub\Ecommerce-App\android\\gradle\wrapper\gradle-wrapper.jar
PATH=C:\Program Files\Android\Android Studio\jbr\bin;C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin;C:\Program Files\Microsoft\jdk-*********-hotspot\bin;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\Microsoft SQL Server\150\Tools\Binn\;C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\170\Tools\Binn\;C:\Program Files\NVIDIA Corporation\NVIDIA NvDLISR;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files (x86)\Microsoft SQL Server\160\Tools\Binn\;C:\Program Files\Microsoft SQL Server\160\Tools\Binn\;C:\Program Files\Microsoft SQL Server\160\DTS\Binn\;C:\Program Files (x86)\Microsoft SQL Server\160\DTS\Binn\;C:\Program Files\nodejs\;C:\Program Files\dotnet\;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;C:\Program Files\Git\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\flutter\3.22\flutter\bin;C:\Users\<USER>\.dotnet\tools;C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin;
USERNAME=Pal
OS=Windows_NT
PROCESSOR_IDENTIFIER=AMD64 Family 25 Model 33 Stepping 0, AuthenticAMD
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp



Periodic native trim disabled

JNI global refs:
JNI global refs: 8, weak refs: 0

JNI global refs memory usage: 843, weak refs: 841

OOME stack traces (most recent first):
Classloader memory used:
Loader bootstrap                                                                       : 1230K
Loader org.gradle.internal.classloader.VisitableURLClassLoader                         : 298K
Loader java.net.URLClassLoader                                                         : 25819B
Loader jdk.internal.loader.ClassLoaders$AppClassLoader                                 : 18431B

Classes loaded by more than one classloader:
Class org.gradle.internal.installation.GradleInstallation                             : loaded 2 times (x 73B)
Class org.gradle.cli.ParsedCommandLine                                                : loaded 2 times (x 78B)
Class org.gradle.api.Action                                                           : loaded 2 times (x 68B)
Class org.gradle.cli.CommandLineParser$OptionString                                   : loaded 2 times (x 71B)
Class org.gradle.internal.installation.GradleInstallation$1                           : loaded 2 times (x 73B)
Class org.gradle.cli.CommandLineParser$AfterOptions                                   : loaded 2 times (x 75B)
Class org.gradle.cli.CommandLineParser$AfterFirstSubCommand                           : loaded 2 times (x 75B)
Class org.gradle.internal.service.UnknownServiceException                             : loaded 2 times (x 81B)
Class org.gradle.api.GradleException                                                  : loaded 2 times (x 80B)
Class org.gradle.cli.CommandLineParser$KnownOptionParserState                         : loaded 2 times (x 74B)
Class org.gradle.cli.AbstractPropertiesCommandLineConverter                           : loaded 2 times (x 81B)
Class org.gradle.api.specs.Spec                                                       : loaded 2 times (x 68B)
Class org.gradle.cli.SystemPropertiesCommandLineConverter                             : loaded 2 times (x 81B)
Class org.gradle.internal.IoActions                                                   : loaded 2 times (x 69B)
Class org.gradle.internal.installation.CurrentGradleInstallation                      : loaded 2 times (x 71B)
Class org.gradle.internal.installation.CurrentGradleInstallationLocator               : loaded 2 times (x 69B)
Class org.gradle.api.Transformer                                                      : loaded 2 times (x 68B)
Class org.gradle.cli.CommandLineParser                                                : loaded 2 times (x 77B)
Class org.gradle.cli.CommandLineParser$BeforeFirstSubCommand                          : loaded 2 times (x 75B)
Class org.gradle.cli.CommandLineOption                                                : loaded 2 times (x 84B)
Class org.gradle.cli.CommandLineParser$ParserState                                    : loaded 2 times (x 74B)
Class org.gradle.cli.CommandLineParser$MissingOptionArgState                          : loaded 2 times (x 75B)
Class org.gradle.api.UncheckedIOException                                             : loaded 2 times (x 80B)
Class org.gradle.cli.ParsedCommandLineOption                                          : loaded 2 times (x 74B)
Class org.gradle.internal.classloader.ClasspathUtil                                   : loaded 2 times (x 69B)
Class org.gradle.cli.CommandLineParser$UnknownOptionParserState                       : loaded 2 times (x 74B)
Class org.gradle.internal.service.ServiceLookupException                              : loaded 2 times (x 80B)
Class org.gradle.cli.CommandLineParser$OptionAwareParserState                         : loaded 2 times (x 75B)
Class org.gradle.cli.CommandLineParser$OptionParserState                              : loaded 2 times (x 73B)
Class org.gradle.internal.Cast                                                        : loaded 2 times (x 69B)
Class org.gradle.cli.CommandLineConverter                                             : loaded 2 times (x 68B)
Class org.gradle.cli.AbstractCommandLineConverter                                     : loaded 2 times (x 77B)
Class org.gradle.cli.CommandLineArgumentException                                     : loaded 2 times (x 80B)


---------------  S Y S T E M  ---------------

OS:
 Windows 10 , 64 bit Build 19041 (10.0.19041.5915)
OS uptime: 16 days 2:53 hours
Hyper-V role detected

CPU: total 12 (initial active 12) (12 cores per cpu, 2 threads per core) family 25 model 33 stepping 0 microcode 0x0, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4a, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, sha, fma, vzeroupper, clflush, clflushopt, hv
Processor Information for all 12 processors :
  Max Mhz: 3701, Current Mhz: 3701, Mhz Limit: 3701

Memory: 4k page, system-wide physical 32648M (5742M free)
TotalPageFile size 92250M (AvailPageFile size 4M)
current process WorkingSet (physical memory assigned to process): 94M, peak: 94M
current process commit charge ("private bytes"): 631M, peak: 632M

vm_info: OpenJDK 64-Bit Server VM (17.0.10+0--11572160) for windows-amd64 JRE (17.0.10+0--11572160), built on Mar 13 2024 23:56:38 by "androidbuild" with MS VC++ 16.10 / 16.11 (VS2019)

END.
