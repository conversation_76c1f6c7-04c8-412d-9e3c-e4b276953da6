import 'package:alderishop/components/common_snckbar.dart';
import 'package:alderishop/constants/constants.dart';
import 'package:alderishop/controllers/cart_controller.dart';
import 'package:alderishop/controllers/coupon_controller.dart';
import 'package:alderishop/data/model/coupon_model.dart';
import 'package:alderishop/services/helper.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class CouponCodeWidget extends StatefulWidget {
  final Function(int couponId) onClick;
  const CouponCodeWidget({super.key, required this.onClick});

  @override
  State<CouponCodeWidget> createState() => _CouponCodeWidgetState();
}

class _CouponCodeWidgetState extends State<CouponCodeWidget> {
  final TextEditingController _couponController = TextEditingController();
  String? _couponError;
  bool _isCouponValid = false;

  CouponModel? model;

  @override
  Widget build(BuildContext context) {
    var cartmodel = Provider.of<CartController>(context).cartItems;

    double totalPrice = cartmodel.fold(0, (total, item) {
      return total +
          (item.cartItems?.fold(0, (subtotal, cartItem) {
                return subtotal! +
                    (cartItem.itemPrice ?? 0) * (cartItem.quantity ?? 0);
              }) ??
              0);
    });
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '${T("Total Price")} :',
                style:
                    const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
              ),
              Text(
                totalPrice.toStringAsFixed(2),
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 17,
                  color: _isCouponValid ? Colors.grey.shade400 : Colors.red,
                  decoration: _isCouponValid
                      ? TextDecoration.lineThrough
                      : TextDecoration.none,
                ),
              ),
              _isCouponValid
                  ? Text(
                      model?.discountType == DiscountType.Percentage
                          ? "\$${(totalPrice - (totalPrice * ((model?.discount ?? 0) / 100))).toStringAsFixed(2)}"
                          : "\$${(totalPrice - (model?.discount ?? 0)).toStringAsFixed(2)}",
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                        color: Colors.red,
                      ),
                    )
                  : const SizedBox()
            ],
          ),
          const SizedBox(
            height: 5,
          ),
          Text(
            T('Enter Coupon Code'),
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: AppColors.PRIMARY_COLOR,
            ),
          ),
          const SizedBox(height: 5),
          TextField(
            controller: _couponController,
            decoration: InputDecoration(
              hintText: T('Coupon Code'),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              errorText: _couponError,
              suffixIcon: Icon(
                Icons.check_circle,
                color: _isCouponValid ? Colors.green : Colors.grey,
              ),
            ),
            onChanged: (value) async {
              if (value.length == 10) {
                var result =
                    await Provider.of<CouponController>(context, listen: false)
                        .checkCouponIsvalidate(code: value);

                if (result != null) {
                  setState(() {
                    _isCouponValid = true;
                    model = result;
                  });

                  widget.onClick(result.id ?? 0);
                } else {
                  setState(() {
                    _isCouponValid = false;
                  });

                  // ignore: use_build_context_synchronously
                  errorMsg(
                      // ignore: use_build_context_synchronously
                      context: context,
                      title: T("The code is invalid for use"));
                }
              } else {
                widget.onClick(0);
              }
            },
          ),
          if (_isCouponValid)
            Padding(
              padding: const EdgeInsets.only(top: 5),
              child: Column(
                children: [
                  Row(
                    children: [
                      Text(
                        T('Coupon Applied Successfully!'),
                        style:
                            const TextStyle(color: Colors.green, fontSize: 12),
                      ),
                    ],
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        T('You got a discount of :'),
                        style:
                            const TextStyle(color: Colors.green, fontSize: 12),
                      ),
                      Text(
                        model?.discountType == DiscountType.Percentage
                            ? " ${model?.discount.toString()}%"
                            : " ${model?.discount.toString()}IQD",
                        style:
                            const TextStyle(color: Colors.green, fontSize: 14),
                      ),
                    ],
                  )
                ],
              ),
            ),
        ],
      ),
    );
  }
}
