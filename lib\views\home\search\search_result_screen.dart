import 'package:alderishop/components/common_snckbar.dart';
import 'package:alderishop/constants/constants.dart';
import 'package:alderishop/controllers/products_controller.dart';
import 'package:alderishop/data/model/Products/productModel/productListModel.dart';
import 'package:alderishop/data/model/search/search_model.dart';
import 'package:alderishop/services/helper.dart';
import 'package:alderishop/views/categories/items/items_Details.dart';
import 'package:alderishop/views/categories/items/widgets/filterWidget.dart';
import 'package:alderishop/views/home/<USER>/SearchPage.dart';
import 'package:alderishop/views/home/<USER>/category/custom_categories_item.dart';
import 'package:flutter/material.dart';
import 'package:alderishop/components/layout.dart';
import 'package:alderishop/views/categories/items/widgets/headerWidget.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

class SearchResultScreen extends StatefulWidget {
  final ProductSearchFilter searchFilter;
  const SearchResultScreen({
    Key? key,
    required this.searchFilter,
  }) : super(key: key);

  @override
  State<SearchResultScreen> createState() => _SearchResultScreenState();
}

class _SearchResultScreenState extends State<SearchResultScreen> {
  AnimationController? animationController;
  final ScrollController _scrollController = ScrollController();
  final RefreshController _refreshController =
      RefreshController(initialRefresh: false);

  void _onRefresh() async {
    var categoryId = Provider.of<ProductsController>(context, listen: false)
            .searchFilter
            .categoryId ??
        0;
    await Provider.of<ProductsController>(context, listen: false)
        .getProductCategories(categoryId: categoryId);
    _refreshController.refreshCompleted();
    setState(() {});
  }

  void _onLoading() async {
    var categoryId = Provider.of<ProductsController>(context, listen: false)
            .searchFilter
            .categoryId ??
        0;
    await Provider.of<ProductsController>(context, listen: false)
        .getProductCategories(categoryId: categoryId);
    if (mounted) {
      setState(() {});
    }
    _refreshController.loadComplete();
  }
bool isGrid = true; 
String _sortBy = 'none'; // Options: 'none', 'price', 'rating'
void _applySort(List<ProductListModel> products) {
  if (_sortBy == 'price') {
    products.sort((a, b) => (a.price ?? 0).compareTo(b.price ?? 0));
  } else if (_sortBy == 'price_desc') {
    products.sort((a, b) => (b.price ?? 0).compareTo(a.price ?? 0));
  }
}
  final sortOptions = [
    SortOption(
    value: 'price',
    label: 'السعر من الأقل إلى الأعلى',
    icon: Icons.arrow_upward,
    iconColor: AppColors.PRIMARY_COLOR,
  ),
  SortOption(
    value: 'price_desc',
    label:'السعر من الأعلى إلى الأقل',
    icon: Icons.arrow_downward,
    iconColor: AppColors.PRIMARY_COLOR,
  ),
  SortOption(
    value: 'rating',
    label:"الترتيب حسب التقييم",
    icon: Icons.star,
    iconColor: AppColors.brown,
  ),
];
  @override
  Widget build(BuildContext context) {
    var provider = Provider.of<ProductsController>(context);

    return ApplicationLayout(
      content: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          children: <Widget>[
            HeaderWidget(
                onTap: () {
                  Navigator.of(context).pushReplacement(MaterialPageRoute(
                      builder: (context) => const SearchPage()));
                },
                text: widget.searchFilter.search),
                TopFilterBar( isGrid: isGrid,
  onToggleView: () {
    setState(() {
      isGrid = !isGrid;
    });
  },onSort: (String value) {
  setState(() {
    _sortBy = value; 
    _applySort(provider
   .productData);
  });
},

 onFilter: () { 
      Navigator.push(
                    context,
                    MaterialPageRoute(builder: (context) => const SearchPage()),
                  );
   }, sortOptions: sortOptions,),
            Expanded(
              child: NestedScrollView(
                controller: _scrollController,
                headerSliverBuilder:
                    (BuildContext context, bool innerBoxIsScrolled) {
                  return <Widget>[];
                },
                body: Column(
                  children: [
                    SizedBox(
                      height: widget.searchFilter.attrFilter != null &&
                              widget.searchFilter.attrFilter!.isNotEmpty
                          ? 35
                          : 0,
                      child: ListView.builder(
                        scrollDirection: Axis.horizontal,
                        itemCount: widget.searchFilter.attrFilter?.length,
                        itemBuilder: (BuildContext context, int index) {
                          return Container(
                            margin:
                                const EdgeInsets.symmetric(horizontal: 5),
                            padding: const EdgeInsets.symmetric(
                                horizontal: 15, vertical: 5),
                            decoration: BoxDecoration(
                              color: AppColors.PRIMARY_COLOR,
                              borderRadius: BorderRadius.circular(10),
                            ),
                            child: Center(
                              child: Row(
                                children: [
                                  Text(
                                    "${widget.searchFilter.attrFilter?[index].name ?? ""} : ${widget.searchFilter.attrFilter?[index].value ?? ""}",
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontWeight: FontWeight.w600,
                                      fontSize: 12,
                                    ),
                                  ),
                                  const SizedBox(
                                    width: 5,
                                  ),
                                  InkWell(
                                    onTap: () {
                                      pleaseWaitDialog(
                                          context: context,
                                          isShown: true);
                                      provider.productData.removeWhere(
                                          (element) =>
                                              element.id ==
                                              provider
                                                  .productData[index].id);
                                      _onRefresh();
                                      pleaseWaitDialog(
                                          context: context,
                                          isShown: false);
                                    },
                                    child: const Icon(
                                      Icons.close,
                                      color: Colors.white,
                                    ),
                                  )
                                ],
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                    const SizedBox(
                      height: 20,
                    ),
                    provider.isLoading
                        ? const Center(child: CircularProgressIndicator())
                        : Expanded(
                            child: Container(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 5),
                              color: Colors.white,
                              child: SmartRefresher(
                                  enablePullDown: true,
                                  enablePullUp: true,
                                  header: const WaterDropHeader(),
                                  footer: CustomFooter(
                                    builder: (context, mode) {
                                      Widget body;
                                      if (mode == LoadStatus.idle) {
                                        body = Text(T("Pull up to load"));
                                      } else if (mode ==
                                          LoadStatus.failed) {
                                        body = Text(
                                            T("Load Failed! Click retry!"));
                                      } else if (mode ==
                                          LoadStatus.canLoading) {
                                        body =
                                            Text(T("Release to load more"));
                                      } else {
                                        body = Text(T("No more data"));
                                      }
                                      return SizedBox(
                                        height: 55.0,
                                        child: Center(child: body),
                                      );
                                    },
                                  ),
                                  controller: _refreshController,
                                  onRefresh: _onRefresh,
                                  onLoading: _onLoading,
                                  child: Column(
                                    children: [
                                      provider.isLoading == false
                                          ? Expanded(
                                              child: SizedBox(
                                                child:
                                                    AlignedGridView.count(
                                                  crossAxisCount: isGrid?2:1,
                                                  shrinkWrap: true,
                                                  mainAxisSpacing: 10.0,
                                                  crossAxisSpacing: 10.0,
                                                  itemCount: provider
                                                      .productData.length,
                                                  itemBuilder:
                                                      (BuildContext context,
                                                          int index) {
                                                    return InkWell(
                                                      onTap: () {
                                                        Navigator.of(
                                                                context)
                                                            .push(
                                                                MaterialPageRoute(
                                                          builder:
                                                              (context) =>
                                                                  ItemDetails(
                                                            item: provider
                                                                .productData[
                                                                    index]
                                                                .id,
                                                          ),
                                                        ));
                                                      },
                                                      child:
                                                          CustomCategoryItem(
                                                      
                                                        data: provider
                                                                .productData[
                                                            index],
                                                      ),
                                                    );
                                                  },
                                                ),
                                              ),
                                            )
                                          : const Center(
                                              child:
                                                  CircularProgressIndicator()),
                                    ],
                                  )),
                            ),
                          ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
      selectedBottomNavbarItem: BottomNavbarItems.none,
    );
  }
}
