import 'package:alderishop/components/common_snckbar.dart';
import 'package:alderishop/constants/constants.dart';
import 'package:alderishop/controllers/products_controller.dart';
import 'package:alderishop/data/model/Products/productModel/productListModel.dart';
import 'package:alderishop/data/model/search/search_model.dart';
import 'package:alderishop/services/helper.dart';
import 'package:alderishop/views/categories/items/items_Details.dart';
import 'package:alderishop/views/categories/items/widgets/filterWidget.dart';
import 'package:alderishop/views/home/<USER>/SearchPage.dart';
import 'package:alderishop/views/home/<USER>/custom_categories_item.dart';
import 'package:flutter/material.dart';
import 'package:alderishop/components/layout.dart';
import 'package:alderishop/views/categories/items/widgets/headerWidget.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

extension CopyProductSearchFilter on ProductSearchFilter {
  ProductSearchFilter copy() {
    return ProductSearchFilter(
      search: this.search,
      attrFilter: this.attrFilter != null
          ? List<AttrFilter>.from(this.attrFilter!.map((e) => e.copy()))
          : null,
      // Add other fields copy if exist, e.g.:
      // categoryId: this.categoryId,
      // priceRange: this.priceRange,
      // etc.
    );
  }
}

extension CopyAttrFilter on AttrFilter {
  AttrFilter copy() {
    return AttrFilter(
      name: this.name,
      value: this.value,
      // copy other fields if exist
    );
  }
}

class SearchResultScreen extends StatefulWidget {
  final ProductSearchFilter searchFilter;
  const SearchResultScreen({
    Key? key,
    required this.searchFilter,
  }) : super(key: key);

  @override
  State<SearchResultScreen> createState() => _SearchResultScreenState();
}

class _SearchResultScreenState extends State<SearchResultScreen> {
  final RefreshController _refreshController =
      RefreshController(initialRefresh: false);
  bool isGrid = true;
  String _sortBy = 'none';
  late ProductSearchFilter _activeFilter;

  final sortOptions = [
    SortOption(
      value: 'price',
      label: 'السعر من الأقل إلى الأعلى',
      icon: Icons.arrow_upward,
      iconColor: AppColors.PRIMARY_COLOR,
    ),
    SortOption(
      value: 'price_desc',
      label: 'السعر من الأعلى إلى الأقل',
      icon: Icons.arrow_downward,
      iconColor: AppColors.PRIMARY_COLOR,
    ),
    SortOption(
      value: 'rating',
      label: "الترتيب حسب التقييم",
      icon: Icons.star,
      iconColor: AppColors.brown,
    ),
  ];

  void _applySort(List<ProductListModel> products) {
    if (_sortBy == 'price') {
      products.sort((a, b) => (a.price ?? 0).compareTo(b.price ?? 0));
    } else if (_sortBy == 'price_desc') {
      products.sort((a, b) => (b.price ?? 0).compareTo(a.price ?? 0));
    }
    // You can add rating sorting if needed here.
  }

  Future<void> _onRefresh() async {
    await Provider.of<ProductsController>(context, listen: false).getProducts(
      resetAndRefresh: true,
      seachModel: _activeFilter,
    );
    _refreshController.refreshCompleted();
    setState(() {});
  }

  Future<void> _onLoading() async {
    await Provider.of<ProductsController>(context, listen: false).getProducts(
      resetAndRefresh: false,
      seachModel: _activeFilter,
    );
    _refreshController.loadComplete();
    setState(() {});
  }

  @override
  void initState() {
    super.initState();
    _activeFilter = widget.searchFilter.copy();
  }

  @override
  void dispose() {
    _refreshController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    var provider = Provider.of<ProductsController>(context);

    return ApplicationLayout(
      content: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          children: <Widget>[
            HeaderWidget(
              onTap: () {
                Navigator.of(context).pushReplacement(MaterialPageRoute(
                    builder: (context) => const SearchPage()));
              },
              text: _activeFilter.search,
            ),
            TopFilterBar(
              isGrid: isGrid,
              onToggleView: () {
                setState(() {
                  isGrid = !isGrid;
                });
              },
              onSort: (String value) {
                setState(() {
                  _sortBy = value;
                  _applySort(provider.productData);
                });
              },
              onFilter: () {
                Navigator.push(
                    context,
                    MaterialPageRoute(
                        builder: (context) => const SearchPage()));
              },
              sortOptions: sortOptions,
            ),
            const SizedBox(height: 10),
            Expanded(
              child: provider.isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : SmartRefresher(
                      controller: _refreshController,
                      enablePullDown: true,
                      enablePullUp: true,
                      onRefresh: _onRefresh,
                      onLoading: _onLoading,
                      header: const WaterDropHeader(),
                      footer: CustomFooter(
                        builder: (context, mode) {
                          Widget body;
                          if (mode == LoadStatus.idle) {
                            body = Text(T("Pull up to load"));
                          } else if (mode == LoadStatus.failed) {
                            body = Text(T("Load Failed! Click retry!"));
                          } else if (mode == LoadStatus.canLoading) {
                            body = Text(T("Release to load more"));
                          } else {
                            body = Text(T("No more data"));
                          }
                          return SizedBox(
                              height: 55.0, child: Center(child: body));
                        },
                      ),
                      child: AlignedGridView.count(
                        crossAxisCount: isGrid ? 2 : 1,
                        mainAxisSpacing: 10.0,
                        crossAxisSpacing: 10.0,
                        itemCount: provider.productData.length,
                        physics: const AlwaysScrollableScrollPhysics(),
                        itemBuilder: (context, index) {
                          return InkWell(
                            onTap: () {
                              Navigator.of(context).push(MaterialPageRoute(
                                builder: (context) => ItemDetails(
                                    item: provider.productData[index].id),
                              ));
                            },
                            child: CustomCategoryItem(
                                data: provider.productData[index]),
                          );
                        },
                      ),
                    ),
            ),
          ],
        ),
      ),
      selectedBottomNavbarItem: BottomNavbarItems.none,
    );
  }
}
