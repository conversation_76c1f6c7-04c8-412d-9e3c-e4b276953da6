import 'package:alderishop/constants/constants.dart';
import 'package:alderishop/controllers/app_controller.dart';
import 'package:alderishop/controllers/products_controller.dart';
import 'package:alderishop/services/helper.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

class Search extends StatefulWidget {
  final Function(String) onSearchSubmitted;
  const Search({super.key, required this.onSearchSubmitted});

  @override
  State<Search> createState() => _SearchState();
}

TextEditingController _etSearch = TextEditingController();
String search = '';
List<String> searchHistory = [];

class _SearchState extends State<Search> {
  @override
  void initState() {
    super.initState();

    // selectedRating = provider.rating ?? 0.0;
    _loadSearchHistory();
  }

  void _loadSearchHistory() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    setState(() {
      searchHistory = prefs.getStringList('searchHistory') ?? [];
    });
  }

  void _saveSearchHistory(String searchText) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    if (searchText.isNotEmpty && !searchHistory.contains(searchText)) {
      searchHistory.insert(0, searchText);
      prefs.setStringList('searchHistory', searchHistory);
    }
  }

  void _removeFromSearchHistory(String searchText) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    if (searchHistory.contains(searchText)) {
      searchHistory.remove(searchText);
      prefs.setStringList('searchHistory', searchHistory);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
 TextField(
  controller: _etSearch,
  autofocus: true,
  textInputAction: TextInputAction.done,
  onChanged: (textValue) {
    setState(() {
      search = textValue;
      widget.onSearchSubmitted(textValue.trim());
    });
  },
  onSubmitted: (value) {
    if (value.trim().isNotEmpty) {
      setState(() {
        search = value;
        _saveSearchHistory(search);
        widget.onSearchSubmitted(value);
      });
    }
  },
  maxLines: 1,
  style: TextStyle(
    fontSize: 13,
    color: AppColors.BLACK_COLOR,
  ),
  decoration: InputDecoration(
    prefixIcon: const Icon(
      Icons.search,
      color: Colors.transparent,
    ),
    suffixIcon: (_etSearch.text == '')
        ? null
        : GestureDetector(
            onTap: () {
              setState(() {
                _etSearch.clear();
                widget.onSearchSubmitted('');
              });
            },
            child: Icon(
              Icons.close,
              color: AppColors.BLACK_COLOR.withOpacity(0.5),
              size: 18,
            ),
          ),
    contentPadding: const EdgeInsets.all(0),
    isDense: true,
    border: const OutlineInputBorder(
      borderSide: BorderSide.none,
      borderRadius: BorderRadius.all(
        Radius.circular(10),
      ),
    ),
    filled: true,
    fillColor: Colors.grey.withOpacity(0.5),
    hintText: T('Search here'),
  ),
),

        const SizedBox(height: 18),
        //-------------------Search History------------------------------------
        if (searchHistory.isNotEmpty)
          Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Text(
                    T("Last Search "),
                    textAlign: TextAlign.start,
                    style: TextStyle(
                      fontSize: 12,
                      color: AppColors.BLACK_COLOR,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 5),
              Wrap(
                direction: Axis.vertical,
                alignment: WrapAlignment.start,
                spacing: 8.0,
                runSpacing: 8.0,
                children: List.generate(
                  searchHistory.length > 4 ? 4 : searchHistory.length,
                  (index) {
                    return InkWell(
                      onTap: () {
                        setState(() {
                          search = searchHistory[index];
                          _etSearch.text = search;
                          Provider.of<ProductsController>(context,
                                  listen: false)
                              .searchFilter
                              .search = search;
                        });
                      },
                      child: SizedBox(
                        width: AppController.W - 40,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Row(
                              children: [
                                const Icon(
                                  Icons.history_outlined,
                                  size: 17,
                                ),
                                const SizedBox(width: 5),
                                Text(
                                  searchHistory[index],
                                  style: const TextStyle(fontSize: 12),
                                ),
                              ],
                            ),
                            InkWell(
                                onTap: () {
                                  setState(() {
                                    _removeFromSearchHistory(
                                        searchHistory[index]);
                                    searchHistory.removeAt(index);
                                  });
                                },
                                child:
                                    Image.asset("assets/img/new/cancel.png")),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
      ],
    );
  }
}
