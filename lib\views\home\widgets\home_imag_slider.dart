import 'package:alderishop/controllers/home_slider_controller.dart';
import 'package:alderishop/data/model/categories/categroy_model.dart';
import 'package:alderishop/views/categories/items/category_item.dart';
import 'package:alderishop/views/categories/items/items_Details.dart';
import 'package:alderishop/views/home/<USER>/ReusableBannerSlider.dart';
import 'package:provider/provider.dart';
import 'package:flutter/material.dart';
class HomeBanner extends StatefulWidget {
  final bool autoPlay;

  const HomeBanner({
    Key? key,
    required this.autoPlay,
  }) : super(key: key);

  @override
  State<HomeBanner> createState() => _HomeBannerState();
}

class _HomeBannerState extends State<HomeBanner> {


  @override
  Widget build(BuildContext context) {
    final dataProvider = Provider.of<HomeSliderController>(context);
    final slider = dataProvider.sliderItem;

    return slider.isEmpty
        ? const Center(child: CircularProgressIndicator())
        : ReusableBannerSlider(
  items: slider,
  autoPlay: true,
  onTap: (item) {
    if (item.sliderType == "1") {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => CategoryItem(
            category: CategoryModel(id: item.referenceId),
          ),
        ),
      );
    } else if (item.sliderType == "0") {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => ItemDetails(item: item.referenceId),
        ),
      );
    }
  },
)
;
  }
}
