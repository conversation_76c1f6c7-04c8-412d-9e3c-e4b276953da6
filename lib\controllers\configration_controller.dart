import 'package:alderishop/data/api/api.dart';
import 'package:alderishop/data/model/configration/configrationModel.dart';
import 'package:alderishop/data/model/language/language_model.dart';
import 'package:flutter/widgets.dart';

import '../data/model/Privacy Policy/Topic_model.dart';

class ConfigrationController with ChangeNotifier {
  List<ComboBoxDataModel> deliveryType = [];

  List<LanguageModel> languages = [];
  TopicsModel? privacyPolicy;
  TopicsModel? termsToUse;

  Future<void> getDeliveryType() async {
    try {
      deliveryType.clear();
      var url = 'odata/v1/deliverytypes';
      var result = await Api.getOne(
        action: url,
      );
      deliveryType.clear();
      if (result.data != null) {
        for (var element in result.data) {
          deliveryType.add(ComboBoxDataModel(element["Id"], element["Type"]));
        }
      }
      notifyListeners();
    } catch (e) {
      print(e);
    }
  }

  //--------------------------------------------------------
  Future<void> getLanguages() async {
    try {
      languages.clear();
      var url = 'odata/v1/languages';

      //api/A_Languages/GetPaginatedStaticTranslate?skip=0&take=100
      var result = await Api.getOne(
        action: url,
      );
      languages.clear();
      if (result.data["value"] != null) {
        for (var element in result.data['value']) {
          var lang = LanguageModel.fromJson(element);
          if (lang.published == true) {
            languages.add(lang);
          }
        }
      }
      notifyListeners();
    } catch (e) {
      print(e);
    }
  }

  //--------------------------------------------------------
  Future<void> getPrivacyPolicy({
    bool resetAndRefresh = true,
  }) async {
    try {
      var url = 'api/v1/TopicRest/GetPrivacyInfo';
      var result = await Api.getOne(
        action: url,
      );

      if (result.data != null) {
        privacyPolicy = TopicsModel.fromJson(result.data);
      }
    } catch (e) {
      // Handle errors appropriately
      print(e);
    }
  }

//------------------------------------------------------------------------------
  Future<void> getTermsToUse({
    bool resetAndRefresh = true,
  }) async {
    try {
      var url = 'api/v1/TopicRest/GetConditionsOfUseText';
      var result = await Api.getOne(
        action: url,
      );
      if (result.data != null) {
        termsToUse = TopicsModel.fromJson(result.data);
      }
    } catch (e) {
      // ignore: avoid_print
      print(e);
    }
  }
}
