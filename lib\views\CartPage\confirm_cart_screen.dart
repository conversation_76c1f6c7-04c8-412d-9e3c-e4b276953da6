import 'package:alderishop/components/common_snckbar.dart';
import 'package:alderishop/components/common_text_field_with_length.dart';
import 'package:alderishop/components/common_worning_info_widget.dart';
import 'package:alderishop/components/layout.dart';
import 'package:alderishop/constants/constants.dart';
import 'package:alderishop/controllers/app_controller.dart';
import 'package:alderishop/controllers/auth_Controller.dart';
import 'package:alderishop/controllers/cart_controller.dart';
import 'package:alderishop/controllers/order_controller.dart';
import 'package:alderishop/data/model/addresses_model.dart';
import 'package:alderishop/data/model/auth_model.dart';
import 'package:alderishop/data/model/order/order_model.dart';
import 'package:alderishop/services/helper.dart';
import 'package:alderishop/views/CartPage/cart_product_list.dart';
import 'package:alderishop/views/CartPage/widgets/coupon_code_widget.dart';
import 'package:alderishop/views/CartPage/widgets/order_delivery_type.dart';
import 'package:alderishop/views/CartPage/widgets/payment_type_widget.dart';
import 'package:alderishop/views/categories/widgets/Header.dart';
import 'package:alderishop/views/orders/orders_screen.dart';
import 'package:alderishop/views/profile/widgets/customContanier.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class ConfirmCartScreen extends StatefulWidget {
  const ConfirmCartScreen({super.key});

  @override
  State<ConfirmCartScreen> createState() => _ConfirmCartScreenState();
}

class _ConfirmCartScreenState extends State<ConfirmCartScreen> {
  final TextEditingController _noteController = TextEditingController();
  bool _isExpanded = false;
  DeliveryType? _selectedDeliveryType;
  int? _selectedAddressId;
  int? couponId;
  int? selectedPaymentType;
  double discountPrice = 0;

  @override
  Widget build(BuildContext context) {
    
    return ApplicationLayout(
      selectedBottomNavbarItem: BottomNavbarItems.shoppingcard,
      content: SingleChildScrollView(
        child: Column(
          children: [
            CategoriesHeader(text: T('Confirm Cart')),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 5),
              child: Column(
                children: [
                  const SizedBox(height: 10),
                  AppController.isAuth == false
                      ? CommonWorningInfoWidget(
                          text: T(
                              "After request the price our team will contact you"),
                        )
                      : AuthController.getUserType() ==
                              UserTypes.businessCustomer
                          ? CommonWorningInfoWidget(
                              text: T(
                                  "After request the price our team will contact you"),
                            )
                          : const SizedBox.shrink(),

                  const SizedBox(height: 10),
                  //---------------Products-------------------------------------
                  Container(
                    decoration: BoxDecoration(
                      border: Border.all(color: AppColors.PRIMARY_COLOR),
                      color: _isExpanded
                          ? AppColors.PRIMARY_COLOR.withOpacity(0.02)
                          : AppColors.PRIMARY_COLOR,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: ExpansionTile(
                      trailing: Icon(
                        _isExpanded
                            ? Icons.keyboard_arrow_up
                            : Icons.keyboard_arrow_down,
                        color: _isExpanded
                            ? AppColors.PRIMARY_COLOR
                            : Colors.white,
                      ),
                      collapsedIconColor:
                          _isExpanded ? AppColors.PRIMARY_COLOR : Colors.white,
                      title: Text(
                        T('Products'),
                        style: TextStyle(
                            color: _isExpanded
                                ? AppColors.PRIMARY_COLOR
                                : Colors.white,
                            fontWeight: FontWeight.bold),
                      ),
                      initiallyExpanded: false,
                      onExpansionChanged: (expanded) {
                        setState(() {
                          _isExpanded = expanded;
                        });
                      },
                      children: const [
                        CartProductsListShow(
                          confirmscreen: false,
                        )
                      ],
                    ),
                  ),

                  //---------------CouponCode-----------------------------------
                  CouponCodeWidget(
                    onClick: (value) {
                      if (value == 0) {
                        couponId = null;
                        return;
                      }
                      setState(() {
                        couponId = value;
                      });
                    },
                  ),
                  //---------------OrderDeliveryType----------------------------
                  OrderDeliveryType(
                    onclick: (deliveryType) {
                      setState(() {
                        _selectedDeliveryType = deliveryType;
                      });
                    },
                    onAddressSelected: (
                      int? selectedAddressId,
                    ) {
                      setState(() {
                        _selectedAddressId = selectedAddressId;
                      });
                    },
                  ),
                  //---------------PaymentType---------------------------------
                  PaymentTypeWidget(
                    selectedPaymentTypeVal: (value) {
                      setState(() {
                        selectedPaymentType = value;
                      });
                    },
                  ),
                  const SizedBox(
                    height: 5,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 10),
                        child: Text(
                          T('Note'),
                          style: TextStyle(
                              color: AppColors.PRIMARY_COLOR,
                              fontWeight: FontWeight.bold),
                        ),
                      ),
                    ],
                  ),
                  //---------------Note------------------------------------------
                  Padding(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                    child: CommonInputWithMaxLength(
                      controller: _noteController,
                      initialVal: "",
                      maxLines: 3,
                      hintText: T("Note"),
                      borderColor: AppColors.BLACK_GREY.withOpacity(0.5),
                    ),
                  ),
                  //---------------ConfirmOrder---------------------------------
                  InkWell(
  onTap: () async {
    final shouldProceed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(T("Confirm Order")),
        content: _selectedDeliveryType == DeliveryType.FromPlace
            ? Text(T("Do you want to place the order? The barcode will be active for 24 hours."))
            : Text(T("Do you want to place the order?")),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(T("Cancel")),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: Text(T("OK")),
          ),
        ],
      ),
    );

    if (shouldProceed != true) return;

    pleaseWaitDialog(context: context, isShown: true);

    final cartController = Provider.of<CartController>(context, listen: false);

    List<OrderItem> orderItems = cartController.cartItems
        .expand((cartItem) => cartItem.cartItems ?? [])
        .map((cartItem) => OrderItem(
              productId: cartItem.itemId,
              quantity: cartItem.quantity ?? 0,
              productPrice: cartItem.itemPrice,
              productName: cartItem.itemName,
              id: 0,
              orderId: 0,
              productCombinationId: 0,
              selectedProductAttributeOptionIds:
                  cartItem.selectedOptions?.map((o) => o.id).toList().cast<int>(),
              productCombinationName: cartItem.itemName,
            ))
        .toList();

    final result = await Provider.of<OrdersController>(context, listen: false).createOrder(
      Orders(
        orderItem: orderItems,
        customerNote: _noteController.text,
        couponId: couponId ?? 0,
        deliveryType: _selectedDeliveryType?.index ?? 1,
        addressId: _selectedAddressId,
        total: 0,
        totalDiscount: 0,
        orderStatus: OrderStatus.values[selectedPaymentType ?? 0].index,
        paymentType: selectedPaymentType ?? 0,
        address: _selectedAddressId != null
            ? AddressesModel(id: _selectedAddressId!)
            : null,
        approvedById: 0,
        approvedByName: "",
        customerId: 0,
        customerName: "",
        invoiceRefrenceCodeERP: "",
        invoiceRefrenceIdERP: "",
        orderApproveStatus: OrderApproveStatus.Unknown.index,
      ),
    );

    if (!context.mounted) return;
    pleaseWaitDialog(context: context, isShown: false);

    if (result.isSuccess == true) {
  if (!context.mounted) return;

  successMsg(
    context: context,
    title: T("Success"),
    msg: T("Successfully Saved"),
  );

  await Future.delayed(const Duration(seconds: 2)); // <-- delay here

  if (!context.mounted) return;

  Navigator.of(context).pushReplacement(
    MaterialPageRoute(builder: (_) => const MyOrderesScreen()),
  );
}
else {
      errorMsg(
        context: context,
        title: "",
        msg: result.errors?[0] ?? T("فشل في تأكيد الطلب"),
      );
    }
  },
  child: Padding(
    padding: const EdgeInsets.symmetric(horizontal: 25, vertical: 10),
    child: CustomContainer(
      color: AppColors.brown,
      text: AuthController.getUserType() == UserTypes.businessCustomer
          ? T("Request price")
          : T("Confirm Order"),
      width: AppController.W / 1.12,
    ),
  ),
),

                  const SizedBox(
                    height: 30,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
