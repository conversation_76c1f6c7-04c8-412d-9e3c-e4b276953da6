import 'package:alderishop/constants/constants.dart';
import 'package:alderishop/controllers/app_controller.dart';
import 'package:alderishop/controllers/products_controller.dart';
import 'package:alderishop/data/model/Products/productModel/productModel.dart';
import 'package:alderishop/services/helper.dart';
import 'package:alderishop/views/categories/items/widgets/detailsPageWigets/attributes_list_widget.dart';
import 'package:alderishop/views/categories/items/widgets/detailsPageWigets/description_Tabs_widgets.dart';
import 'package:alderishop/views/home/<USER>/CustomText.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

class DetailsDiscrptionWidget extends StatefulWidget {
  final ProductModel item;
  final Function(Options, List<Options>) onChoseAtter;

  const DetailsDiscrptionWidget({
    super.key,
    required this.item,
    required this.onChoseAtter,
  });

  @override
  State<DetailsDiscrptionWidget> createState() =>
      _DetailsDiscrptionWidgetState();
}

class _DetailsDiscrptionWidgetState extends State<DetailsDiscrptionWidget> {
  int selectedId = 1;
  int? sizeId;
  int? colorId;
  late ProductModel _item;
  int? reviewId;

  List<Options> selectedAtter = [];
  @override
  void initState() {
    super.initState();
    _item = widget.item;
  }

  @override
  Widget build(BuildContext context) {
    bool hasDetails =
        _item.productAttribute != null && _item.productAttribute!.isNotEmpty;
    // ignore: non_constant_identifier_names
    bool Hasdescription =
        _item.shortDescription != null && _item.shortDescription!.isNotEmpty;

    return Container(
      color: AppColors.WHITE_COLOR,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                if (hasDetails)
                  GestureDetector(
                    onTap: () {
                      setState(() {
                        selectedId = 0;
                      });
                    },
                    child: SizedBox(
                      width: AppController.W / 3.8,
                      height: AppController.h * 0.04,
                      child: Center(
                        child: CustomText(
                          text: T("Details"),
                          fontSize: 11.sp,
                          fontWeight: FontWeight.w600,
                          color: selectedId == 0
                              ? AppColors.PRIMARY_COLOR
                            : AppColors.SOFT_GREY,
                        ),
                      ),
                    ),
                  ),
                if (hasDetails)
                  Container(
                    height: 30,
                    width: 1,
                    color: AppColors.PRIMARY_COLOR,
                  ),
                GestureDetector(
                  onTap: () {
                    setState(() {
                      selectedId = 1;
                    });
                  },
                  child: SizedBox(
                    width: AppController.W / 3.8,
                    height: AppController.h * 0.04,
                    child: Center(
                      child: CustomText(
                        text: T("Description"),
                        fontSize: 11.sp,
                        fontWeight: FontWeight.w600,
                        color: selectedId == 1
                                  ? AppColors.PRIMARY_COLOR
                            : AppColors.SOFT_GREY,
                      ),
                    ),
                  ),
                ),
                Container(
                  height: 30,
                  width: 1,
                  color: AppColors.PRIMARY_COLOR,
                ),
                GestureDetector(
                  onTap: () {
                    setState(() {
                      selectedId = 2;
                    });
                  },
                  child: SizedBox(
                    width: AppController.W / 3.8,
                    height: AppController.h * 0.04,
                    child: Center(
                      child: CustomText(
                        text: T("Reviews"),
                        fontSize: 11.sp,
                        fontWeight: FontWeight.w600,
                        color: selectedId == 2
                            ? AppColors.PRIMARY_COLOR
                            : AppColors.SOFT_GREY,
                      ),
                    ),
                  ),
                ),
              ],
            ),
            selectedId == 0 && hasDetails
                ? AttrinutesListWidget(
                    onChose: (Options selectedOption, List<Options> value,
                        int order, List<int?> availableIdsSameType) async {
                      selectedAtter = value;
                      await Provider.of<ProductsController>(context,
                              listen: false)
                          .getProductAttributeBySelectedValues(
                              widget.item.id ?? 0,
                              value
                                  .map(
                                    (e) => e.id ?? 0,
                                  )
                                  .toList(),
                              order,
                              availableIdsSameType);

                      widget.onChoseAtter(selectedOption, selectedAtter);
                    },
                  )
                : selectedId == 1 && Hasdescription
                    ? DescriptionSection(
                        fullDescription: _item.shortDescription)
                    : selectedId == 2
                        ? ReviewsSection(
                            item: _item.id,
                          )
                        : const SizedBox(),
          ],
        ),
      ),
    );
  }
}
