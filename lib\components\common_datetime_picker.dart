import 'dart:io';
import 'package:alderishop/constants/constants.dart';
import 'package:alderishop/controllers/app_controller.dart';
import 'package:alderishop/services/helper.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

// ignore: depend_on_referenced_packages
import 'package:intl/intl.dart';

class MyDateTimePicker extends StatefulWidget {
  const MyDateTimePicker(
      {Key? key,
      required this.initialVal,
      required this.onSave,
      required this.caption,
      required this.backColor,
      this.fontSize,
      this.onChange})
      : super(key: key);
  final DateTime initialVal;
  final Function onSave;
  final Function? onChange;
  final Color backColor;
  final double? fontSize;
  final String caption;
  @override
  // ignore: library_private_types_in_public_api
  _MyDateTimePickerState createState() => _MyDateTimePickerState();
}

class _MyDateTimePickerState extends State<MyDateTimePicker> {
  // DateTime selectedDate =widget.initialVal;
  TextEditingController _dateController = TextEditingController();
  bool _isPressed = false;

  @override
  void didChangeDependencies() {
    _dateController = TextEditingController(
        text: DateFormat('yyyy-MM-dd hh:mm a', 'en').format(widget.initialVal));
    super.didChangeDependencies();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        _selectDate(context);
      },
      onTapDown: (_) => setState(() => _isPressed = true),
      onTapUp: (_) => setState(() => _isPressed = false),
      onTapCancel: () => setState(() => _isPressed = false),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 150),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10),
          color: widget.backColor,
          border: Border.all(
            color: _isPressed ? AppColors.PRIMARY_COLOR : AppColors.SOFT_GREY,
            width: _isPressed ? 2 : 1,
          ),
          boxShadow: _isPressed
              ? [
                  BoxShadow(
                    color: AppColors.PRIMARY_COLOR.withOpacity(0.3),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  )
                ]
              : null,
        ),
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        child: Row(
          children: [
            Expanded(
              child: Text(
                widget.caption,
                style: TextStyle(
                  fontSize: widget.fontSize ?? 16,
                  fontWeight: FontWeight.normal,
                  color: AppColors.BLACK_COLOR,
                ),
              ),
            ),
            Icon(
              Icons.event_note_rounded,
              color: AppColors.PRIMARY_COLOR,
              size: 20,
            ),
          ],
        ),
      ),
    );
  }

  void _showDialog(Widget child) {
    showCupertinoModalPopup<void>(
      context: context,
      builder: (BuildContext context) => Container(
        height: MediaQuery.of(context).size.height * .40,
        padding: const EdgeInsets.only(top: 6.0),
        margin: EdgeInsets.only(
          bottom: MediaQuery.of(context).viewInsets.bottom,
        ),
        decoration: BoxDecoration(
          color: CupertinoColors.systemBackground.resolveFrom(context),
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.2),
              blurRadius: 10,
              offset: const Offset(0, -5),
            ),
          ],
        ),
        child: SafeArea(
          top: false,
          child: Column(
            children: [
              Container(
                height: 5,
                width: 40,
                margin: const EdgeInsets.symmetric(vertical: 8),
                decoration: BoxDecoration(
                  color: Colors.grey.withOpacity(0.3),
                  borderRadius: BorderRadius.circular(2.5),
                ),
              ),
              Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      T('Select Date & Time'),
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: AppColors.PRIMARY_COLOR,
                      ),
                    ),
                    CupertinoButton(
                      padding: EdgeInsets.zero,
                      child: Text(
                        T('Done'),
                        style: TextStyle(
                          color: AppColors.PRIMARY_COLOR,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      onPressed: () => Navigator.of(context).pop(),
                    ),
                  ],
                ),
              ),
              Expanded(child: child),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _selectDate(BuildContext context) async {
    DateTime minDate = DateTime.now().add(const Duration(hours: 23));
    DateTime maxDate = DateTime.now().add(const Duration(days: 60));
    if (Platform.isIOS) {
      // IOS
      _showDialog(CupertinoDatePicker(
        initialDateTime: DateTime.now().add(const Duration(days: 2)),
        minimumDate: minDate,
        maximumDate: maxDate,
        onDateTimeChanged: (value) {
          setState(() {
            _dateController.text =
                DateFormat('yyyy-MM-dd hh:mm a', 'en').format(value);
          });
          if (widget.onChange != null) widget.onChange!(value);
        },
      ));
    } else {
      // Android
      final DateTime? picked = await showDatePicker(
        context: context,
        initialDate: DateTime.parse(_dateController.text),
        initialDatePickerMode: DatePickerMode.day,
        firstDate: DateTime(2015),
        lastDate: DateTime(2101),
      );
      if (picked != null) {
        setState(() {
          _dateController.text =
              DateFormat('yyyy-MM-dd hh:mm a', 'en').format(picked);
        });
      }
      if (widget.onChange != null) widget.onChange!(picked);
    }
  }
}

//================//================//================//================//================
//================//================//================//================//================

class MyDatePicker extends StatefulWidget {
  const MyDatePicker(
      {Key? key,
      required this.initialVal,
      required this.backColor,
      required this.onSave,
      this.caption,
      this.fontSize,
      this.isDisabled,
      this.onChange,
      this.showLabel,
      this.width})
      : super(key: key);
  final DateTime initialVal;
  final Function onSave;
  final Function? onChange;
  final Color backColor;
  final String? caption;
  final double? fontSize;
  final bool? isDisabled;
  final double? width;
  final bool? showLabel;
  @override
  // ignore: library_private_types_in_public_api
  _MyDatePickerState createState() => _MyDatePickerState();
}

class _MyDatePickerState extends State<MyDatePicker> {
  // DateTime selectedDate =widget.initialVal;
  TextEditingController _dateController = TextEditingController();
  bool _isPressed = false;

  @override
  void didChangeDependencies() {
    _dateController = TextEditingController(
        text: DateFormat('yyyy-MM-dd', 'en').format(widget.initialVal));
    super.didChangeDependencies();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.showLabel ?? false)
          Padding(
            padding: const EdgeInsets.only(left: 4, bottom: 4),
            child: Text(
              T("Date of Birth"),
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: AppColors.PRIMARY_COLOR,
              ),
            ),
          ),
        GestureDetector(
          onTap: () {
            if (widget.isDisabled == true) {
              return;
            }
            _selectDate(context);
          },
          onTapDown: (_) => setState(() => _isPressed = true),
          onTapUp: (_) => setState(() => _isPressed = false),
          onTapCancel: () => setState(() => _isPressed = false),
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 150),
            width: widget.width,
            decoration: BoxDecoration(
              color: widget.backColor,
              borderRadius: BorderRadius.circular(10),
              border: Border.all(
                color:
                    _isPressed ? AppColors.PRIMARY_COLOR : AppColors.SOFT_GREY,
                width: _isPressed ? 2 : 1,
              ),
              boxShadow: _isPressed
                  ? [
                      BoxShadow(
                        color: AppColors.PRIMARY_COLOR.withOpacity(0.3),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      )
                    ]
                  : null,
            ),
            padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    widget.caption!,
                    style: TextStyle(
                      fontSize: widget.fontSize ?? 16,
                      fontWeight: FontWeight.normal,
                      color: widget.caption == T("Select your Birthday")
                          ? AppColors.BLACK_COLOR.withOpacity(0.5)
                          : AppColors.BLACK_COLOR,
                    ),
                  ),
                ),
                Icon(
                  Icons.calendar_today_rounded,
                  color: AppColors.PRIMARY_COLOR,
                  size: 20,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  void _showDialog(Widget child) {
    showCupertinoModalPopup<void>(
      context: context,
      builder: (BuildContext context) => Container(
        height: MediaQuery.of(context).size.height * .40,
        padding: const EdgeInsets.only(top: 6.0),
        margin: EdgeInsets.only(
          bottom: MediaQuery.of(context).viewInsets.bottom,
        ),
        decoration: BoxDecoration(
          color: CupertinoColors.systemBackground.resolveFrom(context),
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.2),
              blurRadius: 10,
              offset: const Offset(0, -5),
            ),
          ],
        ),
        child: SafeArea(
          top: false,
          child: Column(
            children: [
              Container(
                height: 5,
                width: 40,
                margin: const EdgeInsets.symmetric(vertical: 8),
                decoration: BoxDecoration(
                  color: Colors.grey.withOpacity(0.3),
                  borderRadius: BorderRadius.circular(2.5),
                ),
              ),
              Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      T('Select Date of Birth'),
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: AppColors.PRIMARY_COLOR,
                      ),
                    ),
                    CupertinoButton(
                      padding: EdgeInsets.zero,
                      child: Text(
                        T('Done'),
                        style: TextStyle(
                          color: AppColors.PRIMARY_COLOR,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      onPressed: () => Navigator.of(context).pop(),
                    ),
                  ],
                ),
              ),
              Expanded(child: child),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _selectDate(BuildContext context) async {
    if (Platform.isIOS) {
      // iOS
      DateTime selectedDate =
          widget.initialVal; // Initialize with the initial value
      _showDialog(
        Column(
          children: [
            Expanded(
              child: CupertinoDatePicker(
                initialDateTime: widget.initialVal,
                mode: CupertinoDatePickerMode.date,
                onDateTimeChanged: (value) {
                  selectedDate = value; // Update selectedDate when changed
                },
              ),
            ),
            CupertinoButton(
              child: Text(T('Done'),
                  style: TextStyle(color: AppColors.PRIMARY_COLOR)),
              onPressed: () {
                setState(() {
                  _dateController.text =
                      DateFormat('yyyy-MM-dd', 'en').format(selectedDate);
                });
                widget.onSave(selectedDate); // Save the selected date
                if (widget.onChange != null) widget.onChange!(selectedDate);
                Navigator.of(context).pop(); // Close the dialog
              },
            ),
          ],
        ),
      );
    } else {
      // Android
      final DateTime? picked = await showDatePicker(
        context: context,
        initialDate: DateTime.parse(_dateController.text),
        initialDatePickerMode: DatePickerMode.day,
        firstDate: DateTime(1900),
        lastDate: DateTime(2101),
        builder: (context, child) {
          return Theme(
            data: Theme.of(context).copyWith(
              colorScheme: ColorScheme.light(
                primary: AppColors.SECOUND_COLOR, // <-- SEE HERE
                onPrimary: AppColors.PRIMARY_COLOR, // <-- SEE HERE
              ),
              textButtonTheme: TextButtonThemeData(
                style: TextButton.styleFrom(
                  backgroundColor: AppColors.PRIMARY_COLOR, // button text color
                ),
              ),
            ),
            child: child!,
          );
        },
      );
      if (picked != null) {
        setState(() {
          widget.onSave(picked);
          _dateController.text = DateFormat('yyyy-MM-dd', 'en').format(picked);
        });
        if (widget.onChange != null) widget.onChange!(picked);
      }
    }
  }
}

class MyTimePicker extends StatefulWidget {
  const MyTimePicker(
      {Key? key,
      required this.initialVal,
      required this.backColor,
      required this.onSave,
      this.isDisabled,
      required this.caption,
      this.fontSize,
      this.onChange})
      : super(key: key);
  final TimeOfDay initialVal;
  final Function onSave;
  final Function? onChange;
  final Color backColor;
  final String caption;
  final double? fontSize;
  final bool? isDisabled;

  @override
  State<MyTimePicker> createState() => _MyTimePickerState();
}

class _MyTimePickerState extends State<MyTimePicker> {
  TextEditingController _dateController = TextEditingController();
  bool _isPressed = false;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        if (widget.isDisabled == true) {
          return;
        }
        _selectTime(context);
      },
      onTapDown: (_) => setState(() => _isPressed = true),
      onTapUp: (_) => setState(() => _isPressed = false),
      onTapCancel: () => setState(() => _isPressed = false),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 150),
        width: AppController.W - 20,
        decoration: BoxDecoration(
          color: widget.backColor,
          borderRadius: BorderRadius.circular(10),
          border: Border.all(
            color: _isPressed ? AppColors.PRIMARY_COLOR : AppColors.SOFT_GREY,
            width: _isPressed ? 2 : 1,
          ),
          boxShadow: _isPressed
              ? [
                  BoxShadow(
                    color: AppColors.PRIMARY_COLOR.withOpacity(0.3),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  )
                ]
              : null,
        ),
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        child: Row(
          children: [
            Expanded(
              child: Text(
                widget.caption,
                style: TextStyle(
                  fontSize: widget.fontSize ?? 16,
                  fontWeight: FontWeight.normal,
                  color: AppColors.BLACK_COLOR,
                ),
              ),
            ),
            Icon(
              Icons.access_time_rounded,
              color: AppColors.PRIMARY_COLOR,
              size: 20,
            ),
          ],
        ),
      ),
    );
  }

  void _showDialog(Widget child) {
    showCupertinoModalPopup<void>(
      context: context,
      builder: (BuildContext context) => Container(
        height: MediaQuery.of(context).size.height * .40,
        padding: const EdgeInsets.only(top: 6.0),
        margin: EdgeInsets.only(
          bottom: MediaQuery.of(context).viewInsets.bottom,
        ),
        decoration: BoxDecoration(
          color: CupertinoColors.systemBackground.resolveFrom(context),
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.2),
              blurRadius: 10,
              offset: const Offset(0, -5),
            ),
          ],
        ),
        child: SafeArea(
          top: false,
          child: Column(
            children: [
              Container(
                height: 5,
                width: 40,
                margin: const EdgeInsets.symmetric(vertical: 8),
                decoration: BoxDecoration(
                  color: Colors.grey.withOpacity(0.3),
                  borderRadius: BorderRadius.circular(2.5),
                ),
              ),
              Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      T('Select Time'),
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: AppColors.PRIMARY_COLOR,
                      ),
                    ),
                    CupertinoButton(
                      padding: EdgeInsets.zero,
                      child: Text(
                        T('Done'),
                        style: TextStyle(
                          color: AppColors.PRIMARY_COLOR,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      onPressed: () => Navigator.of(context).pop(),
                    ),
                  ],
                ),
              ),
              Expanded(child: child),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _selectTime(BuildContext context) async {
    DateTime minDate = DateTime.now().add(const Duration(hours: 23));
    DateTime maxDate = DateTime.now().add(const Duration(days: 60));
    if (Platform.isIOS) {
      // IOS
      _showDialog(CupertinoDatePicker(
        initialDateTime: DateTime.now().add(const Duration(days: 2)),
        mode: CupertinoDatePickerMode.time,
        use24hFormat: true,
        minimumDate: minDate,
        maximumDate: maxDate,
        onDateTimeChanged: (value) {
          setState(() {
            TimeOfDay picked =
                TimeOfDay(hour: value.hour, minute: value.minute);
            _dateController.text = picked.format(context);
            widget.onSave(picked);
          });
          if (widget.onChange != null) widget.onChange!(value);
        },
      ));
    } else {
      // Android

      final TimeOfDay? picked = await showTimePicker(
        context: context,
        initialTime: widget.initialVal,
        builder: (context, child) {
          return Theme(
            data: Theme.of(context).copyWith(
              colorScheme: ColorScheme.light(
                primary: AppColors.PRIMARY_COLOR, // <-- SEE HERE
                onPrimary: AppColors.WHITE_COLOR, // <-- SEE HERE
              ),
              textButtonTheme: TextButtonThemeData(
                style: TextButton.styleFrom(
                  backgroundColor: AppColors.PRIMARY_COLOR, // button text color
                ),
              ),
            ),
            child: child!,
          );
        },
      );
      if (picked != null) {
        setState(() {
          widget.onSave(picked);
          _dateController.text = picked.format(context);
        });
      }
      if (widget.onChange != null) widget.onChange!(picked);
    }
  }
}
