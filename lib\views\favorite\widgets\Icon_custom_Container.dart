import 'package:flutter/material.dart';

// ignore: must_be_immutable
class IconCustomContainerWidget extends StatelessWidget {
  final String image;
  Color color;

  IconCustomContainerWidget(
      {super.key, required this.image, required this.color});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 20,
      width: 20,
      decoration:
          BoxDecoration(color: color, borderRadius: BorderRadius.circular(5)),
      child: Padding(
        padding: const EdgeInsets.all(4),
        child: Image(
          fit: BoxFit.contain,
          image: AssetImage(
            image,
          ),
        ),
      ),
    );
  }
}
