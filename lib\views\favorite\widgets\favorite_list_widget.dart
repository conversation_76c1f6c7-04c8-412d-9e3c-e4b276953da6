import 'package:alderishop/controllers/app_controller.dart';
import 'package:alderishop/data/model/favorite_model.dart';
import 'package:alderishop/views/categories/items/items_Details.dart';
import 'package:alderishop/views/favorite/widgets/favorite_list_items_widget.dart';
import 'package:flutter/material.dart';
import 'package:page_transition/page_transition.dart';

class FavoriteListWidget extends StatelessWidget {
  final List<favoriteModel> favorites;
  final String searchTerm;

  const FavoriteListWidget({
    Key? key,
    required this.favorites,
    required this.searchTerm,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final filteredFavorites = favorites.where((item) {
      return item.itemName!.toLowerCase().contains(searchTerm.toLowerCase());
    }).toList();

    return GridView.builder(

  shrinkWrap: true,
  physics: const NeverScrollableScrollPhysics(),
  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
    crossAxisCount: 2, 
    mainAxisSpacing: 5, 
    crossAxisSpacing: 0, 
    childAspectRatio: 0.75, 
  ),
  itemCount: filteredFavorites.length,
  itemBuilder: (BuildContext context, int index) {
    return InkWell(
      onTap: () {
        Navigator.push(
                context,
                PageTransition(
                  type: AppController.currentLangId == 2
                      ? PageTransitionType.leftToRight
                      : PageTransitionType.rightToLeftWithFade,
                  child: ItemDetails(
                    item:favorites[index].productId??0,
                  ),
                ));
      },
      child: FavoriteListItemsWidget(
        model: filteredFavorites[index],
        onDelete: () {
      
        },
      ),
    );
  },
);

  }
}
