import 'package:alderishop/components/common_cache_image.dart';
import 'package:alderishop/controllers/offer_controller.dart';
import 'package:alderishop/data/api/api.dart';
import 'package:alderishop/data/model/categories/categroy_model.dart';
import 'package:alderishop/views/catalog/category_item.dart';
import 'package:alderishop/views/catalog/items_Details.dart';
import 'package:alderishop/views/home/<USER>';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class OffersWidget extends StatefulWidget {
  const OffersWidget({super.key});

  @override
  State<OffersWidget> createState() => _OffersWidgetState();
}

class _OffersWidgetState extends State<OffersWidget> {
  @override
  Widget build(BuildContext context) {
    var data = Provider.of<OffersController>(context).offers;

    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: data.length,
      itemBuilder: (context, index) {
        return Column(
          children: [
            InkWell(
              onTap: () {
                if (data[index].offerType?.index == 1) {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => CategoryItem(
                          category: CategoryModel(
                        id: data[index].referenceId,
                      )),
                    ),
                  );
                  print(
                    data[index].referenceId,
                  );
                } else if (data[index].offerType?.index == 0) {
                  print(
                      "Navigating to Item Details with referenceId: ${data[index].referenceId}");
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) =>
                          ItemDetails(item: data[index].referenceId),
                    ),
                  );
                } else {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const HomePage(),
                    ),
                  );
                }
              },
              child: CachedImage(
                imageUrl: ("$baseUrl1/${data[index].imageUrl ?? ""}"),
                fit: BoxFit.contain,
              ),
            ),
          ],
        );
      },
    );
  }
}
