import 'package:alderishop/data/api/api.dart';
import 'package:alderishop/data/model/favorite_model.dart';
import 'package:flutter/widgets.dart';

class FavoritesController with ChangeNotifier {
  static List<int> favoritesProductsIds = [];
  List<favoriteModel> favoritesList = [];

  static Future<void> getFavoritesItems() async {
    try {
      favoritesProductsIds.clear();
      var url = 'api/Wishlist/GetWishlistByUserId';
      var result = await Api.getOne(
        action: url,
      );
      if (result.data["data"] != null) {
        for (var element in result.data["data"]) {
          favoritesProductsIds.add(element["Product"]["Id"]);
        }
      }
      // ignore: empty_catches
    } catch (e) {}
  }

//--------------------------------------------------------
  Future<List<favoriteModel>> getFavorites() async {
    try {
      favoritesList.clear();
      var url = 'api/Wishlist/GetWishlistByUserId';
      var result = await Api.getOne(action: url);

      if (result.data["data"] != null) {
        for (var element in result.data["data"]) {
          favoritesList.add(favoriteModel.fromJson(element));
        }
      }
    } catch (e) {
      print(e);
    }

    return favoritesList;
  }

//------------------------------------------------------------------------------
  Future<bool> addItemToFavorites(int id) async {
    var url = 'api/Wishlist/AddItemToWishlist';
    await Api.post(
      action: url,
      body: {"ProductId": id, "userId": 0},
    );
    favoritesProductsIds.add(id);

    return true;
  }

//------------------------------------------------------------------------------
  Future<bool> removeItemFromFavorites(
    int id,
  ) async {
    var url = 'api/Wishlist/RemoveItemFromWishlist';
    await Api.post(
      action: url,
      body: {"ProductId": id, "userId": 1016},
    );
    // if (favoritesList.any((element) => element.product!.id == id)) {
    //   favoritesList.removeWhere((element) => element.product!.id == id);
    // }
    // if (favoritesProductsIds.any((element) => element == id)) {
    //   favoritesProductsIds.remove(id);
    // }
    notifyListeners();
    return true;
  }

//------------------------------------------------------------------------------
  static bool checkIfFromFavorites(int id) {
    return favoritesProductsIds.any((element) => element == id);
  }
}
