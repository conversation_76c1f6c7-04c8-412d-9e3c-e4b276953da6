import 'package:alderishop/constants/constants.dart';
import 'package:alderishop/views/home/<USER>/CustomText.dart';
import 'package:flutter/material.dart';

// ignore: must_be_immutable
class CustomContainer extends StatelessWidget {
  String text;
  Color color;
  bool icon;
  double width;
  CustomContainer(
      {super.key,
      this.icon=false,
      required this.text,
      required this.color,
      required this.width});

  @override
  Widget build(BuildContext context) {
    return Container(
        width: width,
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10), color: color),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            if(icon)
const Padding(
  padding:  EdgeInsets.symmetric(horizontal: 2),
  child: Icon(Icons.add,color: Colors.white,size: 20,),
),
            CustomText(
                text: text,
                fontSize: 14,
                fontWeight: FontWeight.normal,
                color: AppColors.WHITE_COLOR),
          ],
        ));
  }
}
