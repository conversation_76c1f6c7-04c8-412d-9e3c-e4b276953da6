import 'dart:convert';

import 'package:alderishop/components/common_snckbar.dart';
import 'package:alderishop/constants/constants.dart';
import 'package:alderishop/controllers/app_controller.dart';
import 'package:alderishop/controllers/products_controller.dart';
import 'package:alderishop/data/model/review_model.dart';
import 'package:alderishop/services/helper.dart';
import 'package:alderishop/views/categories/items/widgets/review/reviews_screen.dart';
import 'package:alderishop/views/home/<USER>/CustomText.dart';
import 'package:alderishop/views/profile/widgets/customContanier.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:webview_flutter/webview_flutter.dart';

// ignore: must_be_immutable
class ReviewsSection extends StatefulWidget {
  final int? item;
  final ReviewModel? review;
  const ReviewsSection({
    super.key,
    required this.item,
    this.review,
  });

  @override
  State<ReviewsSection> createState() => _ReviewsSectionState();
}

class _ReviewsSectionState extends State<ReviewsSection> {
  final TextEditingController _commentController = TextEditingController();
  int _rating = 0;

  void _submitReview() {
    String comment = _commentController.text;

    if (_rating == 0 || comment.isEmpty) {
      errorMsg(context: context, title: "Please provide a rating and comment");

      return;
    }
    Provider.of<ProductsController>(context, listen: false).createProductReview(
        CreateReview(comment: comment, rate: _rating, productId: widget.item));
    _commentController.clear();
    setState(() {
      _rating = 0;
      Provider.of<ProductsController>(context, listen: false)
          .getProductReviews(itemId: widget.item ?? 0);
    });
    successMsg(context: context, title: "Review submitted successfully!");
  }

  Widget _buildStar(int index) {
    return SizedBox(
      width: 25,
      child: IconButton(
        icon: Icon(
          index <= _rating ? Icons.star : Icons.star_border,
          color: Colors.amber,
          size: 20,
        ),
        onPressed: () {
          setState(() {
            _rating = index;
          });
        },
      ),
    );
  }

  String _formatDate(String? dateTimeStr) {
    if (dateTimeStr == null || dateTimeStr.isEmpty) return "";

    try {
      DateTime dateTime = DateTime.parse(dateTimeStr);
      return "${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')}";
    } catch (e) {
      return dateTimeStr;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        const SizedBox(
          height: 15,
        ),
        Row(
          children: [
            Text(T("add your Comment"),
                style: TextStyle(color: AppColors.PRIMARY_COLOR, fontSize: 15)),
          ],
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: List.generate(5, (index) => _buildStar(index + 1)),
        ),
        TextField(
          controller: _commentController,
          decoration: InputDecoration(
            labelText: "Comment",
            labelStyle: TextStyle(color: Colors.grey.shade400),
            border: const OutlineInputBorder(),
          ),
          maxLines: 2,
        ),
        const SizedBox(height: 10),
        InkWell(
          onTap: _submitReview,
          child: CustomContainer(
            width: AppController.W,
            color: AppColors.PRIMARY_COLOR,
            text: T('Add new Comment'),
          ),
        ),
        const SizedBox(height: 10),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(T("All Comments"),
                style: TextStyle(color: AppColors.PRIMARY_COLOR, fontSize: 13)),
          ],
        ),
        SizedBox(
            width: double.infinity,
            child: FutureBuilder(
                future: Provider.of<ProductsController>(context, listen: false)
                    .getProductReviews(itemId: widget.item ?? 0),
                builder: (
                  context,
                  snapshot,
                ) {
                  if (snapshot.connectionState != ConnectionState.done) {
                    return const Center(child: CircularProgressIndicator());
                  }
                  final data =
                      Provider.of<ProductsController>(context, listen: false)
                          .reviews;
                  return ListView.builder(
                    scrollDirection: Axis.vertical,
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: data.length > 3 ? 3 : data.length,
                    itemBuilder: (context, index) {
                      return Container(
                        margin: const EdgeInsets.symmetric(
                            horizontal: 10, vertical: 10),
                        width: double.infinity,
                        decoration: BoxDecoration(
                          color: AppColors.WHITE_COLOR,
                          boxShadow: [
                            BoxShadow(
                              color: Colors.grey.withOpacity(0.1),
                              spreadRadius: 5,
                              blurRadius: 1,
                              offset: const Offset(0, 3),
                            ),
                          ],
                          borderRadius: BorderRadius.circular(5),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 10, vertical: 5),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              SizedBox(
                                height: 15,
                                child: ListView.builder(
                                  scrollDirection: Axis.horizontal,
                                  itemCount: data[index].rate,
                                  itemBuilder:
                                      (BuildContext context, int index) {
                                    return Image.asset(
                                      'assets/img/star.png',
                                      height: 5,
                                    );
                                  },
                                ),
                              ),
                              const SizedBox(height: 5),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.start,
                                children: [
                                  CustomText(
                                    text:
                                        "${data[index].customerName ?? ""} | ",
                                    fontSize: 11,
                                    fontWeight: FontWeight.w400,
                                    color: AppColors.SOFT_GREY,
                                  ),
                                  CustomText(
                                    text: _formatDate(data[index].createdAt),
                                    fontSize: 10,
                                    fontWeight: FontWeight.w400,
                                    color: AppColors.SOFT_GREY,
                                  ),
                                ],
                              ),
                              const SizedBox(height: 5),
                              CustomText(
                                  text: data[index].comment ?? "",
                                  fontSize: 12,
                                  fontWeight: FontWeight.w700,
                                  color: AppColors.SOFT_GREY),
                              const SizedBox(height: 5),
                              CustomText(
                                text:
                                    "Product : ${data[index].productName ?? ""}",
                                fontSize: 11,
                                fontWeight: FontWeight.w400,
                                color: AppColors.SOFT_GREY,
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                  );
                })),
        InkWell(
          onTap: () {
            Navigator.of(context).push(MaterialPageRoute(
                builder: (context) => const AllReviewsScreen()));
          },
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8),
            child: Container(
              width: AppController.W,
              padding: const EdgeInsets.symmetric(vertical: 10),
              decoration: BoxDecoration(
                  color: AppColors.SECOUND_COLOR,
                  borderRadius: const BorderRadius.only(
                      bottomLeft: Radius.circular(10),
                      bottomRight: Radius.circular(10))),
              child: Text(
                  textAlign: TextAlign.center,
                  T("See more"),
                  style: TextStyle(
                      color: AppColors.BLACK_COLOR,
                      fontSize: 12,
                      fontWeight: FontWeight.bold)),
            ),
          ),
        ),
      ],
    );
  }
}

class DescriptionSection extends StatefulWidget {
  final String? fullDescription;
  const DescriptionSection({super.key, this.fullDescription});

  @override
  State<DescriptionSection> createState() => _DescriptionSectionState();
}

class _DescriptionSectionState extends State<DescriptionSection> {
  bool isFinished = false;
  bool isExpanded = false;
  int maxLines = 3;

  @override
  Widget build(BuildContext context) {
    double fontSize = 30;
    final String content = """
    <html>
    <head>
    <style>
      body {
        font-size: ${fontSize}px;
        text-align: ${AppController.currentLangId == 2 ? 'right' : 'left'}; 
      }
    </style>
    </head>
    <body>
      ${widget.fullDescription ?? ""}
    </body>
    </html>
    """;

    // // Define the threshold for showing the "See More" link.
    // bool shouldShowSeeMore = widget.fullDescription != null &&
    //     widget.fullDescription!.length > maxLines * 50;

    return Column(
      children: [
        SizedBox(
          height: isExpanded ? null : 70,
          child: Directionality(
            textDirection: TextDirection.rtl,
            child: WebView(
              zoomEnabled: true,
              backgroundColor: Colors.white,
              initialUrl: Uri.dataFromString(
                content,
                mimeType: 'text/html',
                encoding: Encoding.getByName('utf-8'),
              ).toString(),
              javascriptMode: JavascriptMode.unrestricted,
            ),
          ),
        ),
      ],
    );
  }
}
