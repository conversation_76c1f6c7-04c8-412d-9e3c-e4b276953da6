import 'package:alderishop/controllers/auth_Controller.dart';
import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';

// ignore: must_be_immutable
class CachedImage extends StatelessWidget { 
  final String imageUrl;

  double? height;
  double? width;
  BoxFit? fit;
  BoxDecoration? decoration;
  BorderRadius? borderRadius;

  CachedImage({
    Key? key,
    required this.imageUrl,
    this.height,
    this.width,
    this.fit,
    this.decoration,
    this.borderRadius,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: borderRadius ?? BorderRadius.zero,
      child: CachedNetworkImage(
          imageUrl: imageUrl,
          height: height,
          width: width,
          fit: fit,
    
          httpHeaders: AuthController.getHeader(),
          errorWidget: (context, url, error) {
            // ignore: avoid_print, unnecessary_brace_in_string_interps
            print("this is image error ${error}");

            return Image.network(
              "https://www.alderishop.com/media/0/default-image.png?size=256",
              height: height,
              width: width,
              fit: fit,
            );
          }),
    );
  }
}
