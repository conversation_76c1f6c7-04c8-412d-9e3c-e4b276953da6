import 'package:alderishop/components/layout.dart';
import 'package:alderishop/constants/constants.dart';
import 'package:alderishop/controllers/app_controller.dart';
import 'package:alderishop/controllers/auth_Controller.dart';
import 'package:alderishop/controllers/coupon_controller.dart';
import 'package:alderishop/services/helper.dart';
import 'package:alderishop/views/home/<USER>/%C4%B0magebgWidget.dart';

import 'package:alderishop/views/home/<USER>/couponItemW;dget/couponItemWidget.dart';
import 'package:alderishop/views/home/<USER>/CustomText.dart';
import 'package:alderishop/views/profile/widgets/profit_points_widget.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class MyCouponsScreen extends StatefulWidget {
  const MyCouponsScreen({
    super.key,
  });

  @override
  State<MyCouponsScreen> createState() => _MyCouponsScreenState();
}

class _MyCouponsScreenState extends State<MyCouponsScreen> {
  Future<double> getProfitPoints() async {
    return await AuthController.getProfitPointOnBuy();
  }

  Widget build(BuildContext context) {

    var data = 
    AppController.isAuth?
    Provider.of<CouponController>(context).myCoupons:Provider.of<CouponController>(context).myCoupons;
    return ApplicationLayout(
      content: SingleChildScrollView(
        child: Column(
          children: [
            ImageBgWidget(),
            SizedBox(
              height: MediaQuery.of(context).size.height * 0.05,
            ),
            if(AppController.isAuth)
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      T("My points"),
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: AppColors.BLACK_COLOR,
                        fontSize: 18,
                      ),
                    ),
                    SizedBox(
                      height: MediaQuery.of(context).size.height * 0.005,
                    ),
                    SizedBox(
                      width: 160,
                      child: Text(
                        T("You can purchase the following coupons using your points balance according to the value"),
                        style: TextStyle(
                          height: 1.2,
                          color: AppColors.BLACK_COLOR,
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ],
                ),
                 const ProfitPointsWidget(),
              ],
            ),
            const SizedBox(
              height: 20,
            ),
            Text(
              T("My Coupons"),
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: AppColors.BLACK_COLOR,
                fontSize: 22,
              ),
            ),
            const SizedBox(
              height: 5,
            ),
        data.isNotEmpty
    ? ListView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        scrollDirection: Axis.vertical,
        itemCount: data.length,
        itemBuilder: (context, index) {
          return Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 7),
                child: CouponItemWidget(
                  isMyCoupon: true,
                  coupons: data[index],
                ),
              ),
            ],
          );
        },
      )
    : Padding(
        padding: const EdgeInsets.symmetric(vertical: 40),
        child: 
        
       CustomText(color: AppColors.PRIMARY_COLOR,fontSize: 20,fontWeight: FontWeight.bold,text: "لايوجد لديك كوبونات",)
      ),

            SizedBox(
              height: MediaQuery.of(context).size.height * 0.03,
            ),
          ],
        ),
      ),
      selectedBottomNavbarItem: BottomNavbarItems.home,
    );
  }
}
