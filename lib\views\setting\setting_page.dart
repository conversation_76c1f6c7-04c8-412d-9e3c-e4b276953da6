import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:provider/provider.dart';
import 'package:alderishop/controllers/app_controller.dart';
import 'package:alderishop/controllers/configration_controller.dart';
import 'package:alderishop/constants/constants.dart';
import 'package:alderishop/components/layout.dart';

class SettingPage extends StatefulWidget {
  const SettingPage({super.key});

  @override
  State<SettingPage> createState() => _SettingPageState();
}

class _SettingPageState extends State<SettingPage> {
  int? _selectedLangId;

  @override
  void initState() {
    super.initState();
    _loadSelectedLanguage();
  }

  Future<void> _loadSelectedLanguage() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      _selectedLangId = prefs.getInt('selectedLangId');
    });
  }

  Future<void> _saveSelectedLanguage(int langId) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt('selectedLangId', langId);
  }

  @override
  Widget build(BuildContext context) {
    var configController = Provider.of<ConfigrationController>(context);
    var appController = Provider.of<AppController>(context, listen: false);

    return ApplicationLayout(
      content: ListView.builder(
        padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 10),
        itemCount: configController.languages.length,
        itemBuilder: (context, index) {
          var e = configController.languages[index];
          return InkWell(
            onTap: () {
              setState(() {
                _selectedLangId = e.id;
              });
              appController.changeLanguage(e.uniqueSeoCode ?? "en", context);
              _saveSelectedLanguage(e.id ?? 0);
            },
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 5),
              child: Row(
                children: [
                  Radio<int>(
                    value: e.id ?? 0,
                    groupValue: _selectedLangId,
                    onChanged: (int? value) {
                      setState(() {
                        _selectedLangId = value;
                      });
                      appController.changeLanguage(
                          e.uniqueSeoCode ?? "en", context);
                      _saveSelectedLanguage(value ?? 0);
                    },
                    activeColor: AppColors.brown,
                  ),
                  Text(
                    e.name ?? "",
                    style: TextStyle(
                      fontSize: 13,
                      color: _selectedLangId == e.id
                          ? AppColors.PRIMARY_COLOR
                          : AppColors.BLACK_COLOR,
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
      selectedBottomNavbarItem: BottomNavbarItems.home,
    );
  }
}
