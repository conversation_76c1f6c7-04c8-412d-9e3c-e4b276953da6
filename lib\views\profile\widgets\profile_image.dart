import 'package:alderishop/constants/constants.dart';
import 'package:alderishop/controllers/app_controller.dart';
import 'package:flutter/material.dart';

class ProfileImageWidget extends StatelessWidget {
  const ProfileImageWidget({super.key});

  Future<void> _openGallery() async {
    // print('Image path: ${pickedImage?.path}');
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        InkWell(
          onTap: _openGallery,
          child: CircleAvatar(
            radius: 60,
            backgroundColor: AppColors.Theard_COLOR.withOpacity(0.5),
          ),
        ),
        Positioned(
          bottom: 25,
          right: 35,
          child: Column(
            children: [
              SizedBox(
                  height: 50,
                  width: 52,
                  child: Image.asset('assets/img/prof.png')),
              SizedBox(
                height: AppController.h * 0.02,
              ),
            ],
          ),
        ),
      ],
    );
  }
}
