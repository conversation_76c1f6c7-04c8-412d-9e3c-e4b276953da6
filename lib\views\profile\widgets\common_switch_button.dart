import 'package:alderishop/constants/constants.dart';
import 'package:flutter/material.dart';

class CommonSwitchButton extends StatefulWidget {
  final ValueChanged<bool>?
      onChanged; // Callback function for switch state change

  const CommonSwitchButton({Key? key, this.onChanged}) : super(key: key);

  @override
  _CommonSwitchButtonState createState() => _CommonSwitchButtonState();
}

class _CommonSwitchButtonState extends State<CommonSwitchButton> {
  // bool _isNotificationEnabled = false;

  @override
  Widget build(BuildContext context) {
    return Transform.scale(
      scale: 0.6,
      child: Switch(
        //  value: _isNotificationEnabled,
        onChanged: (value) {
          setState(() {
            //  _isNotificationEnabled = value;
          });
          // Execute the callback function if provided
          if (widget.onChanged != null) {
            widget.onChanged!(value);
          }
        },
        activeColor: AppColors.WHITE_COLOR,
        inactiveThumbColor: AppColors.WHITE_COLOR,
        activeTrackColor: AppColors.PRIMARY_COLOR,
        inactiveTrackColor: AppColors.SOFT_GREY,
        splashRadius: 0, value: false,
      ),
    );
  }
}
