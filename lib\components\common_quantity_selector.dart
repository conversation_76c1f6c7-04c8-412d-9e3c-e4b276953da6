import 'package:alderishop/constants/constants.dart';
import 'package:alderishop/views/home/<USER>/CustomText.dart';
import 'package:flutter/material.dart';

class QuantitySelector extends StatefulWidget {
  final int initialValue;
  final Function(int) onChangeCount; // Ensure it accepts an int parameter

  const QuantitySelector({
    Key? key,
    required this.initialValue,
    required this.onChangeCount,
  }) : super(key: key);

  @override
  _QuantitySelectorState createState() => _QuantitySelectorState();
}

class _QuantitySelectorState extends State<QuantitySelector> {
  late int _counter;

  @override
  void initState() {
    super.initState();
    _counter = widget.initialValue;
  }

  void _incrementCounter() {
    setState(() {
      _counter++;
    });
    widget.onChangeCount(_counter); // Pass updated value
  }

  void _decrementCounter() {
    if (_counter > 1) {
      // Prevent negative values
      setState(() {
        _counter--;
      });
      widget.onChangeCount(_counter); // Pass updated value
    }
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        InkWell(
          onTap: _decrementCounter,
          child: Container(
            height: 25,
            width: 25,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(5),
              color: AppColors.PRIMARY_COLOR,
            ),
            child: const Icon(Icons.remove, size: 18, color: Colors.white),
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8),
          child: CustomText(
            text: '$_counter',
            fontSize: 14,
            fontWeight: FontWeight.w700,
            color: AppColors.BLACK_GREY,
          ),
        ),
        InkWell(
          onTap: _incrementCounter,
          child: Container(
            height: 25,
            width: 25,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(5),
              color: AppColors.PRIMARY_COLOR,
            ),
            child: const Icon(Icons.add, size: 18, color: Colors.white),
          ),
        ),
      ],
    );
  }
}
