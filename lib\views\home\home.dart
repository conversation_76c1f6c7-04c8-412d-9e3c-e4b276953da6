import 'package:alderishop/components/layout.dart';
import 'package:alderishop/constants/constants.dart';
import 'package:alderishop/controllers/app_controller.dart';
import 'package:alderishop/services/helper.dart';
import 'package:alderishop/views/home/<USER>';
import 'package:alderishop/views/home/<USER>/all_coupons_page.dart';
import 'package:alderishop/views/home/<USER>/my_Coupons_Page.dart';
import 'package:alderishop/views/home/<USER>/home_header.dart';
import 'package:alderishop/views/home/<USER>/coupons_widget.dart';
import 'package:alderishop/views/home/<USER>/offers_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'category/featured_custom_categoriesList.dart';
import 'widgets/CustomText.dart';
import 'category/new_products_widget.dart';
import 'widgets/home_imag_slider.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  bool isGrid=true;
  @override
  Widget build(BuildContext context) {

    return ApplicationLayout(
      content: SingleChildScrollView(
        child: Column(
          children: [
            SizedBox(
              height: AppController.h * 0.015,
            ),
            //-------------------------------Search--------------------------
            const HomeHeader(),

            //-------------------------------SliderImage--------------------------
            SizedBox(
              height: AppController.h * 0.03,
            ),
            const HomeBanner(
              autoPlay: true,
            ),
            SizedBox(
              height: AppController.h * 0.015,
            ),
            //-------------------------------Categories --------------------------
            const CategoriesForHomePage(),
            SizedBox(
              height: AppController.h * 0.02,
            ),

            //------------------Featured Products-----------------------------
            Padding(
              padding: EdgeInsets.only(
                  left: AppController.currentLangId == 2 ? 7 : 18,
                  right: AppController.currentLangId != 2 ? 7 : 18),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  CustomText(
                    text: T('Featured Products'),
                    fontSize: 13.sp,
                    fontWeight: FontWeight.bold,
                    color: AppColors.brown,
                  ),
                  Container(
  decoration: BoxDecoration(
    borderRadius: BorderRadius.circular(10),
    color: Colors.grey.shade200,
  ),
  child: Row(
    children: [
      IconButton(
        icon: Image.asset(
          'assets/img/new/1.png',
          color: !isGrid ?AppColors.PRIMARY_COLOR : Colors.grey,
          width: 24,
          height: 24,
        ),
        onPressed: (){   setState(() {
      isGrid = !isGrid;
    });},
      ),
      IconButton(
        icon: Image.asset(
          'assets/img/new/2.png',
          color: isGrid ?AppColors.PRIMARY_COLOR : Colors.grey,
          width: 24,
          height: 24,
        ),
        onPressed: (){   setState(() {
      isGrid = !isGrid;
    });},
      ),
    ],
  ),
),
                ],
              ),
            ),
            SizedBox(
              height: AppController.h * 0.01,
            ),
            //------------------Featured ProductsList-----------------------------
            SizedBox(
             
              child: FeaturedCustomCategoriesList(
                // favoriteItems: const [],
              
                isGrid: isGrid,
              ),
            ),
            //-----------------------------------------------
            SizedBox(
              height: AppController.h * 0.01,
            ),
            
            const OffersWidget(),

            SizedBox(
              height: AppController.h * 0.03,
            ),
           
            //------------------ New Products -----------------------------
            SizedBox(
              height: AppController.h * 0.01,
            ),
            Padding(
              padding: EdgeInsets.only(
                  left: AppController.currentLangId == 2 ? 7 : 18,
                  right: AppController.currentLangId != 2 ? 7 : 18),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  CustomText(
                    text: T('New Products'),
                    fontSize: 13.sp,
                    fontWeight: FontWeight.bold,
                    color: AppColors.BLACK_COLOR,
                  ),
                  Container(
  decoration: BoxDecoration(
    borderRadius: BorderRadius.circular(10),
    color: Colors.grey.shade200,
  ),
  child: Row(
    children: [
      IconButton(
        icon: Image.asset(
          'assets/img/new/1.png',
          color: !isGrid ?AppColors.PRIMARY_COLOR : Colors.grey,
          width: 24,
          height: 24,
        ),
        onPressed: (){   setState(() {
      isGrid = !isGrid;
    });},
      ),
      IconButton(
        icon: Image.asset(
          'assets/img/new/2.png',
          color: isGrid ?AppColors.PRIMARY_COLOR : Colors.grey,
          width: 24,
          height: 24,
        ),
        onPressed: (){   setState(() {
      isGrid = !isGrid;
    });},
      ),
    ],
  ),
),
                ],
              ),
            ),
            SizedBox(
              height: AppController.h * 0.01,
            ),
            //------------------New ProductsList -----------------------------
             NewProductsWidget(isGrid: isGrid,),
            //----------------------------------------------------------------
            SizedBox(
              height: AppController.h * 0.03,
            ),
       
             Padding(
              padding: EdgeInsets.only(
                  left: AppController.currentLangId == 2 ? 7 : 18,
                  right: AppController.currentLangId != 2 ? 7 : 18),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  CustomText(
                    text: T('Coupons'),
                    fontSize: 13.sp,
                    fontWeight: FontWeight.bold,
                    color: AppColors.BLACK_COLOR,
                  ),
                  InkWell(
                    onTap: () {
                      Navigator.of(context).push(MaterialPageRoute(
                          builder: (context) => const AllCouponPage()));
                    },
                    child: CustomText(
                      text: T('See All'),
                      fontSize: 10.sp,
                      fontWeight: FontWeight.bold,
                      color: AppColors.Theard_COLOR,
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(
              height: AppController.h * 0.01,
            ),
            //------------------ MyCouponsWidget -----------------------------
            const CouponsWidget(),
              SizedBox(
              height: AppController.h * 0.1,
            ),
          ],
        ),
      ),
      selectedBottomNavbarItem: BottomNavbarItems.home,
    );
  }
}
