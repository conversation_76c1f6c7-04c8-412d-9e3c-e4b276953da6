#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 1914576 bytes for Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   The process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (arena.cpp:189), pid=36252, tid=24572
#
# JRE version: OpenJDK Runtime Environment (17.0.7) (build 17.0.7+0-b2043.56-10550314)
# Java VM: OpenJDK 64-Bit Server VM (17.0.7+0-b2043.56-10550314, mixed mode, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED -Xmx4G -Dfile.encoding=windows-1252 -Duser.country=US -Duser.language=en -Duser.variant org.gradle.launcher.daemon.bootstrap.GradleDaemon 7.5

Host: Intel(R) Core(TM) i7-10700T CPU @ 2.00GHz, 16 cores, 15G,  Windows 11 , 64 bit Build 26100 (10.0.26100.3775)
Time: Thu Apr 24 16:26:57 2025 Turkey Standard Time elapsed time: 13.279767 seconds (0d 0h 0m 13s)

---------------  T H R E A D  ---------------

Current thread (0x000001cb21a3a980):  JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=24572, stack(0x000000c3c4e00000,0x000000c3c4f00000)]


Current CompileTask:
C2:  13279 6366       4       java.lang.invoke.MethodType::makeImpl (109 bytes)

Stack: [0x000000c3c4e00000,0x000000c3c4f00000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x683bfa]
V  [jvm.dll+0x8430a4]
V  [jvm.dll+0x8449be]
V  [jvm.dll+0x845023]
V  [jvm.dll+0x24ad2f]
V  [jvm.dll+0xac25c]
V  [jvm.dll+0xac82c]
V  [jvm.dll+0x36a132]
V  [jvm.dll+0x334821]
V  [jvm.dll+0x333cca]
V  [jvm.dll+0x21d251]
V  [jvm.dll+0x21c671]
V  [jvm.dll+0x1a605d]
V  [jvm.dll+0x22c148]
V  [jvm.dll+0x22a209]
V  [jvm.dll+0x7f858c]
V  [jvm.dll+0x7f2aea]
V  [jvm.dll+0x682a35]
C  [ucrtbase.dll+0x37b0]
C  [KERNEL32.DLL+0x2e8d7]
C  [ntdll.dll+0xb14fc]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000001cb27c339f0, length=71, elements={
0x000001cb01b80860, 0x000001cb20da9010, 0x000001cb20da99d0, 0x000001cb20dae3d0,
0x000001cb21a35ff0, 0x000001cb21a368b0, 0x000001cb21a39920, 0x000001cb21a3a980,
0x000001cb21a3ba20, 0x000001cb21a46660, 0x000001cb21bd41c0, 0x000001cb21bf2560,
0x000001cb26f34c40, 0x000001cb26d641d0, 0x000001cb26d9d390, 0x000001cb282fc210,
0x000001cb27c77080, 0x000001cb27d4b750, 0x000001cb27f92230, 0x000001cb27ec1560,
0x000001cb27c2e5b0, 0x000001cb27c31100, 0x000001cb27c2ef50, 0x000001cb27c2e0e0,
0x000001cb27c315d0, 0x000001cb27c2ea80, 0x000001cb27c2f420, 0x000001cb27c2fdc0,
0x000001cb27c2f8f0, 0x000001cb27c30290, 0x000001cb27c30760, 0x000001cb27c31aa0,
0x000001cb27c30c30, 0x000001cb27241fe0, 0x000001cb27240300, 0x000001cb27241b10,
0x000001cb27241170, 0x000001cb27241640, 0x000001cb27242e50, 0x000001cb2723fe30,
0x000001cb2c1e4470, 0x000001cb2c1e7960, 0x000001cb2c1e7e30, 0x000001cb2c1e7490,
0x000001cb2c1e57b0, 0x000001cb2c1e6150, 0x000001cb2c1e4940, 0x000001cb2c1e6fc0,
0x000001cb2c1e6620, 0x000001cb2c1e6af0, 0x000001cb2c1e52e0, 0x000001cb2c1e5c80,
0x000001cb2c33b0b0, 0x000001cb2c33abe0, 0x000001cb2c3393d0, 0x000001cb2c3398a0,
0x000001cb2c33ba50, 0x000001cb2c339d70, 0x000001cb2c338f00, 0x000001cb2c33a240,
0x000001cb2c33b580, 0x000001cb2c338090, 0x000001cb2c33bf20, 0x000001cb2c338560,
0x000001cb2c33c3f0, 0x000001cb2c33a710, 0x000001cb2c338a30, 0x000001cb2c33c8c0,
0x000001cb2c33cd90, 0x000001cb2c33d260, 0x000001cb2c33d730
}

Java Threads: ( => current thread )
  0x000001cb01b80860 JavaThread "main" [_thread_blocked, id=28516, stack(0x000000c3c4100000,0x000000c3c4200000)]
  0x000001cb20da9010 JavaThread "Reference Handler" daemon [_thread_blocked, id=38216, stack(0x000000c3c4800000,0x000000c3c4900000)]
  0x000001cb20da99d0 JavaThread "Finalizer" daemon [_thread_blocked, id=28120, stack(0x000000c3c4900000,0x000000c3c4a00000)]
  0x000001cb20dae3d0 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=21876, stack(0x000000c3c4a00000,0x000000c3c4b00000)]
  0x000001cb21a35ff0 JavaThread "Attach Listener" daemon [_thread_blocked, id=35664, stack(0x000000c3c4b00000,0x000000c3c4c00000)]
  0x000001cb21a368b0 JavaThread "Service Thread" daemon [_thread_blocked, id=9472, stack(0x000000c3c4c00000,0x000000c3c4d00000)]
  0x000001cb21a39920 JavaThread "Monitor Deflation Thread" daemon [_thread_blocked, id=8452, stack(0x000000c3c4d00000,0x000000c3c4e00000)]
=>0x000001cb21a3a980 JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=24572, stack(0x000000c3c4e00000,0x000000c3c4f00000)]
  0x000001cb21a3ba20 JavaThread "C1 CompilerThread0" daemon [_thread_blocked, id=37816, stack(0x000000c3c4f00000,0x000000c3c5000000)]
  0x000001cb21a46660 JavaThread "Sweeper thread" daemon [_thread_blocked, id=29388, stack(0x000000c3c5000000,0x000000c3c5100000)]
  0x000001cb21bd41c0 JavaThread "Common-Cleaner" daemon [_thread_blocked, id=16516, stack(0x000000c3c5300000,0x000000c3c5400000)]
  0x000001cb21bf2560 JavaThread "Notification Thread" daemon [_thread_blocked, id=7344, stack(0x000000c3c5400000,0x000000c3c5500000)]
  0x000001cb26f34c40 JavaThread "C1 CompilerThread1" daemon [_thread_blocked, id=29176, stack(0x000000c3c5100000,0x000000c3c5200000)]
  0x000001cb26d641d0 JavaThread "C1 CompilerThread2" daemon [_thread_blocked, id=35504, stack(0x000000c3c5200000,0x000000c3c5300000)]
  0x000001cb26d9d390 JavaThread "C1 CompilerThread3" daemon [_thread_blocked, id=31452, stack(0x000000c3c5800000,0x000000c3c5900000)]
  0x000001cb282fc210 JavaThread "Daemon health stats" [_thread_blocked, id=33764, stack(0x000000c3c5e00000,0x000000c3c5f00000)]
  0x000001cb27c77080 JavaThread "Incoming local TCP Connector on port 61655" [_thread_in_native, id=39112, stack(0x000000c3c5f00000,0x000000c3c6000000)]
  0x000001cb27d4b750 JavaThread "Daemon periodic checks" [_thread_blocked, id=39032, stack(0x000000c3c6000000,0x000000c3c6100000)]
  0x000001cb27f92230 JavaThread "Daemon" [_thread_blocked, id=19748, stack(0x000000c3c6100000,0x000000c3c6200000)]
  0x000001cb27ec1560 JavaThread "Handler for socket connection from /127.0.0.1:61655 to /127.0.0.1:61656" [_thread_in_native, id=2724, stack(0x000000c3c6200000,0x000000c3c6300000)]
  0x000001cb27c2e5b0 JavaThread "Cancel handler" [_thread_blocked, id=2696, stack(0x000000c3c6300000,0x000000c3c6400000)]
  0x000001cb27c31100 JavaThread "Daemon worker" [_thread_in_Java, id=15424, stack(0x000000c3c6400000,0x000000c3c6500000)]
  0x000001cb27c2ef50 JavaThread "Asynchronous log dispatcher for DefaultDaemonConnection: socket connection from /127.0.0.1:61655 to /127.0.0.1:61656" [_thread_blocked, id=11672, stack(0x000000c3c6500000,0x000000c3c6600000)]
  0x000001cb27c2e0e0 JavaThread "Stdin handler" [_thread_blocked, id=41332, stack(0x000000c3c6600000,0x000000c3c6700000)]
  0x000001cb27c315d0 JavaThread "Daemon client event forwarder" [_thread_blocked, id=12032, stack(0x000000c3c6700000,0x000000c3c6800000)]
  0x000001cb27c2ea80 JavaThread "Cache worker for journal cache (C:\Users\<USER>\.gradle\caches\journal-1)" [_thread_blocked, id=33920, stack(0x000000c3c5700000,0x000000c3c5800000)]
  0x000001cb27c2f420 JavaThread "File lock request listener" [_thread_in_native, id=20856, stack(0x000000c3c6800000,0x000000c3c6900000)]
  0x000001cb27c2fdc0 JavaThread "Cache worker for file hash cache (C:\Users\<USER>\.gradle\caches\7.5\fileHashes)" [_thread_blocked, id=15672, stack(0x000000c3c7200000,0x000000c3c7300000)]
  0x000001cb27c2f8f0 JavaThread "File watcher server" daemon [_thread_in_native, id=25040, stack(0x000000c3c7500000,0x000000c3c7600000)]
  0x000001cb27c30290 JavaThread "File watcher consumer" daemon [_thread_blocked, id=25024, stack(0x000000c3c7600000,0x000000c3c7700000)]
  0x000001cb27c30760 JavaThread "Cache worker for checksums cache (D:\Ecommerce-App\android\.gradle\7.5\checksums)" [_thread_blocked, id=32760, stack(0x000000c3c7700000,0x000000c3c7800000)]
  0x000001cb27c31aa0 JavaThread "Cache worker for cache directory md-rule (C:\Users\<USER>\.gradle\caches\7.5\md-rule)" [_thread_blocked, id=11232, stack(0x000000c3c7800000,0x000000c3c7900000)]
  0x000001cb27c30c30 JavaThread "Cache worker for file content cache (C:\Users\<USER>\.gradle\caches\7.5\fileContent)" [_thread_blocked, id=33868, stack(0x000000c3c7900000,0x000000c3c7a00000)]
  0x000001cb27241fe0 JavaThread "Cache worker for file hash cache (D:\Ecommerce-App\android\.gradle\7.5\fileHashes)" [_thread_blocked, id=4688, stack(0x000000c3c7a00000,0x000000c3c7b00000)]
  0x000001cb27240300 JavaThread "Cache worker for cache directory md-supplier (C:\Users\<USER>\.gradle\caches\7.5\md-supplier)" [_thread_blocked, id=26496, stack(0x000000c3c7b00000,0x000000c3c7c00000)]
  0x000001cb27241b10 JavaThread "Cache worker for execution history cache (C:\Users\<USER>\.gradle\caches\7.5\executionHistory)" [_thread_blocked, id=464, stack(0x000000c3c7c00000,0x000000c3c7d00000)]
  0x000001cb27241170 JavaThread "jar transforms" [_thread_blocked, id=41500, stack(0x000000c3c7d00000,0x000000c3c7e00000)]
  0x000001cb27241640 JavaThread "Cache worker for kotlin-dsl (C:\Users\<USER>\.gradle\caches\7.5\kotlin-dsl)" [_thread_blocked, id=32740, stack(0x000000c3c7e00000,0x000000c3c7f00000)]
  0x000001cb27242e50 JavaThread "jar transforms Thread 2" [_thread_blocked, id=32448, stack(0x000000c3c7f00000,0x000000c3c8000000)]
  0x000001cb2723fe30 JavaThread "Cache worker for dependencies-accessors (D:\Ecommerce-App\android\.gradle\7.5\dependencies-accessors)" [_thread_blocked, id=24888, stack(0x000000c3c8000000,0x000000c3c8100000)]
  0x000001cb2c1e4470 JavaThread "Cache worker for Build Output Cleanup Cache (C:\flutter\packages\flutter_tools\gradle\.gradle\buildOutputCleanup)" [_thread_blocked, id=31940, stack(0x000000c3c8100000,0x000000c3c8200000)]
  0x000001cb2c1e7960 JavaThread "jar transforms Thread 3" [_thread_blocked, id=29560, stack(0x000000c3c8200000,0x000000c3c8300000)]
  0x000001cb2c1e7e30 JavaThread "Memory manager" [_thread_blocked, id=6572, stack(0x000000c3c8300000,0x000000c3c8400000)]
  0x000001cb2c1e7490 JavaThread "jar transforms Thread 4" [_thread_blocked, id=18572, stack(0x000000c3c8400000,0x000000c3c8500000)]
  0x000001cb2c1e57b0 JavaThread "included builds" [_thread_blocked, id=29060, stack(0x000000c3c7400000,0x000000c3c7500000)]
  0x000001cb2c1e6150 JavaThread "Execution worker" [_thread_blocked, id=40600, stack(0x000000c3c8500000,0x000000c3c8600000)]
  0x000001cb2c1e4940 JavaThread "Execution worker Thread 2" [_thread_blocked, id=28048, stack(0x000000c3c8600000,0x000000c3c8700000)]
  0x000001cb2c1e6fc0 JavaThread "Execution worker Thread 3" [_thread_blocked, id=5324, stack(0x000000c3c8700000,0x000000c3c8800000)]
  0x000001cb2c1e6620 JavaThread "Execution worker Thread 4" [_thread_blocked, id=32304, stack(0x000000c3c8800000,0x000000c3c8900000)]
  0x000001cb2c1e6af0 JavaThread "Execution worker Thread 5" [_thread_blocked, id=7756, stack(0x000000c3c8900000,0x000000c3c8a00000)]
  0x000001cb2c1e52e0 JavaThread "Execution worker Thread 6" [_thread_blocked, id=27456, stack(0x000000c3c8a00000,0x000000c3c8b00000)]
  0x000001cb2c1e5c80 JavaThread "Execution worker Thread 7" [_thread_blocked, id=17264, stack(0x000000c3c8b00000,0x000000c3c8c00000)]
  0x000001cb2c33b0b0 JavaThread "Execution worker Thread 8" [_thread_blocked, id=38592, stack(0x000000c3c8c00000,0x000000c3c8d00000)]
  0x000001cb2c33abe0 JavaThread "Execution worker Thread 9" [_thread_blocked, id=27412, stack(0x000000c3c8d00000,0x000000c3c8e00000)]
  0x000001cb2c3393d0 JavaThread "Execution worker Thread 10" [_thread_blocked, id=33576, stack(0x000000c3c8e00000,0x000000c3c8f00000)]
  0x000001cb2c3398a0 JavaThread "Execution worker Thread 11" [_thread_blocked, id=25648, stack(0x000000c3c8f00000,0x000000c3c9000000)]
  0x000001cb2c33ba50 JavaThread "Execution worker Thread 12" [_thread_blocked, id=41312, stack(0x000000c3c9000000,0x000000c3c9100000)]
  0x000001cb2c339d70 JavaThread "Execution worker Thread 13" [_thread_blocked, id=35888, stack(0x000000c3c9100000,0x000000c3c9200000)]
  0x000001cb2c338f00 JavaThread "Execution worker Thread 14" [_thread_blocked, id=31548, stack(0x000000c3c9200000,0x000000c3c9300000)]
  0x000001cb2c33a240 JavaThread "Execution worker Thread 15" [_thread_blocked, id=25112, stack(0x000000c3c9300000,0x000000c3c9400000)]
  0x000001cb2c33b580 JavaThread "Cache worker for execution history cache (C:\flutter\packages\flutter_tools\gradle\.gradle\7.5\executionHistory)" [_thread_blocked, id=22288, stack(0x000000c3c9400000,0x000000c3c9500000)]
  0x000001cb2c338090 JavaThread "Unconstrained build operations" [_thread_blocked, id=30344, stack(0x000000c3c9500000,0x000000c3c9600000)]
  0x000001cb2c33bf20 JavaThread "Unconstrained build operations Thread 2" [_thread_blocked, id=40864, stack(0x000000c3c9600000,0x000000c3c9700000)]
  0x000001cb2c338560 JavaThread "Unconstrained build operations Thread 3" [_thread_blocked, id=39528, stack(0x000000c3c9700000,0x000000c3c9800000)]
  0x000001cb2c33c3f0 JavaThread "Unconstrained build operations Thread 4" [_thread_blocked, id=30732, stack(0x000000c3c9800000,0x000000c3c9900000)]
  0x000001cb2c33a710 JavaThread "Unconstrained build operations Thread 5" [_thread_blocked, id=30700, stack(0x000000c3c9900000,0x000000c3c9a00000)]
  0x000001cb2c338a30 JavaThread "Unconstrained build operations Thread 6" [_thread_blocked, id=6436, stack(0x000000c3c9a00000,0x000000c3c9b00000)]
  0x000001cb2c33c8c0 JavaThread "Unconstrained build operations Thread 7" [_thread_blocked, id=22148, stack(0x000000c3c9b00000,0x000000c3c9c00000)]
  0x000001cb2c33cd90 JavaThread "Unconstrained build operations Thread 8" [_thread_blocked, id=26160, stack(0x000000c3c9c00000,0x000000c3c9d00000)]
  0x000001cb2c33d260 JavaThread "Unconstrained build operations Thread 9" [_thread_blocked, id=32380, stack(0x000000c3c9d00000,0x000000c3c9e00000)]
  0x000001cb2c33d730 JavaThread "Unconstrained build operations Thread 10" [_thread_blocked, id=35352, stack(0x000000c3c9e00000,0x000000c3c9f00000)]

Other Threads:
  0x000001cb20d894e0 VMThread "VM Thread" [stack: 0x000000c3c4700000,0x000000c3c4800000] [id=28052]
  0x000001cb01c0d680 WatcherThread [stack: 0x000000c3c5500000,0x000000c3c5600000] [id=12344]
  0x000001cb01bf1e10 GCTaskThread "GC Thread#0" [stack: 0x000000c3c4200000,0x000000c3c4300000] [id=9244]
  0x000001cb27111f30 GCTaskThread "GC Thread#1" [stack: 0x000000c3c5900000,0x000000c3c5a00000] [id=35712]
  0x000001cb278c2810 GCTaskThread "GC Thread#2" [stack: 0x000000c3c5a00000,0x000000c3c5b00000] [id=19200]
  0x000001cb278c2ac0 GCTaskThread "GC Thread#3" [stack: 0x000000c3c5b00000,0x000000c3c5c00000] [id=20712]
  0x000001cb26aefaa0 GCTaskThread "GC Thread#4" [stack: 0x000000c3c5c00000,0x000000c3c5d00000] [id=27780]
  0x000001cb26bf9810 GCTaskThread "GC Thread#5" [stack: 0x000000c3c5d00000,0x000000c3c5e00000] [id=5436]
  0x000001cb282fa7b0 GCTaskThread "GC Thread#6" [stack: 0x000000c3c6900000,0x000000c3c6a00000] [id=29948]
  0x000001cb282f9790 GCTaskThread "GC Thread#7" [stack: 0x000000c3c6a00000,0x000000c3c6b00000] [id=41804]
  0x000001cb282fa250 GCTaskThread "GC Thread#8" [stack: 0x000000c3c6b00000,0x000000c3c6c00000] [id=34268]
  0x000001cb282f9a40 GCTaskThread "GC Thread#9" [stack: 0x000000c3c6c00000,0x000000c3c6d00000] [id=34360]
  0x000001cb282fa500 GCTaskThread "GC Thread#10" [stack: 0x000000c3c6d00000,0x000000c3c6e00000] [id=23456]
  0x000001cb282faa60 GCTaskThread "GC Thread#11" [stack: 0x000000c3c6e00000,0x000000c3c6f00000] [id=30740]
  0x000001cb282fad10 GCTaskThread "GC Thread#12" [stack: 0x000000c3c6f00000,0x000000c3c7000000] [id=19972]
  0x000001cb01c03930 ConcurrentGCThread "G1 Main Marker" [stack: 0x000000c3c4300000,0x000000c3c4400000] [id=41224]
  0x000001cb01c04250 ConcurrentGCThread "G1 Conc#0" [stack: 0x000000c3c4400000,0x000000c3c4500000] [id=21492]
  0x000001cb282f9fa0 ConcurrentGCThread "G1 Conc#1" [stack: 0x000000c3c7000000,0x000000c3c7100000] [id=41440]
  0x000001cb282f9cf0 ConcurrentGCThread "G1 Conc#2" [stack: 0x000000c3c7100000,0x000000c3c7200000] [id=22104]
  0x000001cb01c3d290 ConcurrentGCThread "G1 Refine#0" [stack: 0x000000c3c4500000,0x000000c3c4600000] [id=38516]
  0x000001cb01c3ea70 ConcurrentGCThread "G1 Service" [stack: 0x000000c3c4600000,0x000000c3c4700000] [id=34644]

Threads with active compile tasks:
C2 CompilerThread0    13306 6366       4       java.lang.invoke.MethodType::makeImpl (109 bytes)

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x0000000700000000, size: 4096 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) not mapped
Compressed class space mapped at: 0x0000000800000000-0x0000000840000000, reserved size: 1073741824
Narrow klass base: 0x0000000800000000, Narrow klass shift: 0, Narrow klass range: 0x40000000

GC Precious Log:
 CPUs: 16 total, 16 available
 Memory: 16195M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (Zero based)
 Heap Region Size: 2M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 254M
 Heap Max Capacity: 4G
 Pre-touch: Disabled
 Parallel Workers: 13
 Concurrent Workers: 3
 Concurrent Refinement Workers: 13
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 100352K, used 42557K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 4 young (8192K), 3 survivors (6144K)
 Metaspace       used 63585K, committed 64192K, reserved 1114112K
  class space    used 8752K, committed 9024K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, OA=open archive, CA=closed archive, TAMS=top-at-mark-start (previous, next)
|   0|0x0000000700000000, 0x0000000700200000, 0x0000000700200000|100%| O|  |TAMS 0x0000000700200000, 0x0000000700000000| Untracked 
|   1|0x0000000700200000, 0x0000000700400000, 0x0000000700400000|100%| O|  |TAMS 0x0000000700400000, 0x0000000700200000| Untracked 
|   2|0x0000000700400000, 0x0000000700600000, 0x0000000700600000|100%|HS|  |TAMS 0x0000000700600000, 0x0000000700400000| Complete 
|   3|0x0000000700600000, 0x0000000700800000, 0x0000000700800000|100%|HC|  |TAMS 0x0000000700800000, 0x0000000700600000| Complete 
|   4|0x0000000700800000, 0x0000000700a00000, 0x0000000700a00000|100%| O|  |TAMS 0x0000000700a00000, 0x0000000700800000| Untracked 
|   5|0x0000000700a00000, 0x0000000700c00000, 0x0000000700c00000|100%| O|  |TAMS 0x0000000700c00000, 0x0000000700a00000| Untracked 
|   6|0x0000000700c00000, 0x0000000700e00000, 0x0000000700e00000|100%| O|  |TAMS 0x0000000700e00000, 0x0000000700c00000| Untracked 
|   7|0x0000000700e00000, 0x0000000701000000, 0x0000000701000000|100%| O|  |TAMS 0x0000000701000000, 0x0000000700e00000| Untracked 
|   8|0x0000000701000000, 0x0000000701200000, 0x0000000701200000|100%| O|  |TAMS 0x0000000701200000, 0x0000000701000000| Untracked 
|   9|0x0000000701200000, 0x0000000701400000, 0x0000000701400000|100%| O|  |TAMS 0x0000000701400000, 0x0000000701200000| Untracked 
|  10|0x0000000701400000, 0x0000000701600000, 0x0000000701600000|100%| O|  |TAMS 0x0000000701600000, 0x0000000701400000| Untracked 
|  11|0x0000000701600000, 0x0000000701800000, 0x0000000701800000|100%| O|  |TAMS 0x0000000701800000, 0x0000000701600000| Untracked 
|  12|0x0000000701800000, 0x0000000701a00000, 0x0000000701a00000|100%| O|  |TAMS 0x0000000701a00000, 0x0000000701800000| Untracked 
|  13|0x0000000701a00000, 0x0000000701c00000, 0x0000000701c00000|100%| O|  |TAMS 0x0000000701c00000, 0x0000000701a00000| Untracked 
|  14|0x0000000701c00000, 0x0000000701e00000, 0x0000000701e00000|100%| O|  |TAMS 0x0000000701e00000, 0x0000000701c00000| Untracked 
|  15|0x0000000701e00000, 0x0000000702000000, 0x0000000702000000|100%| O|  |TAMS 0x0000000701ea2a00, 0x0000000701e00000| Untracked 
|  16|0x0000000702000000, 0x0000000702200000, 0x0000000702200000|100%| O|  |TAMS 0x0000000702000000, 0x0000000702000000| Untracked 
|  17|0x0000000702200000, 0x000000070238f400, 0x0000000702400000| 77%| O|  |TAMS 0x0000000702200000, 0x0000000702200000| Untracked 
|  18|0x0000000702400000, 0x0000000702400000, 0x0000000702600000|  0%| F|  |TAMS 0x0000000702400000, 0x0000000702400000| Untracked 
|  19|0x0000000702600000, 0x0000000702600000, 0x0000000702800000|  0%| F|  |TAMS 0x0000000702600000, 0x0000000702600000| Untracked 
|  20|0x0000000702800000, 0x0000000702800000, 0x0000000702a00000|  0%| F|  |TAMS 0x0000000702800000, 0x0000000702800000| Untracked 
|  21|0x0000000702a00000, 0x0000000702a00000, 0x0000000702c00000|  0%| F|  |TAMS 0x0000000702a00000, 0x0000000702a00000| Untracked 
|  22|0x0000000702c00000, 0x0000000702c00000, 0x0000000702e00000|  0%| F|  |TAMS 0x0000000702c00000, 0x0000000702c00000| Untracked 
|  23|0x0000000702e00000, 0x0000000702e00000, 0x0000000703000000|  0%| F|  |TAMS 0x0000000702e00000, 0x0000000702e00000| Untracked 
|  24|0x0000000703000000, 0x0000000703200000, 0x0000000703200000|100%| S|CS|TAMS 0x0000000703000000, 0x0000000703000000| Complete 
|  25|0x0000000703200000, 0x0000000703400000, 0x0000000703400000|100%| S|CS|TAMS 0x0000000703200000, 0x0000000703200000| Complete 
|  26|0x0000000703400000, 0x0000000703600000, 0x0000000703600000|100%| S|CS|TAMS 0x0000000703400000, 0x0000000703400000| Complete 
|  27|0x0000000703600000, 0x0000000703600000, 0x0000000703800000|  0%| F|  |TAMS 0x0000000703600000, 0x0000000703600000| Untracked 
|  28|0x0000000703800000, 0x0000000703800000, 0x0000000703a00000|  0%| F|  |TAMS 0x0000000703800000, 0x0000000703800000| Untracked 
|  29|0x0000000703a00000, 0x0000000703a00000, 0x0000000703c00000|  0%| F|  |TAMS 0x0000000703a00000, 0x0000000703a00000| Untracked 
|  30|0x0000000703c00000, 0x0000000703c00000, 0x0000000703e00000|  0%| F|  |TAMS 0x0000000703c00000, 0x0000000703c00000| Untracked 
|  31|0x0000000703e00000, 0x0000000703e00000, 0x0000000704000000|  0%| F|  |TAMS 0x0000000703e00000, 0x0000000703e00000| Untracked 
|  32|0x0000000704000000, 0x0000000704000000, 0x0000000704200000|  0%| F|  |TAMS 0x0000000704000000, 0x0000000704000000| Untracked 
|  33|0x0000000704200000, 0x0000000704200000, 0x0000000704400000|  0%| F|  |TAMS 0x0000000704200000, 0x0000000704200000| Untracked 
|  34|0x0000000704400000, 0x0000000704400000, 0x0000000704600000|  0%| F|  |TAMS 0x0000000704400000, 0x0000000704400000| Untracked 
|  35|0x0000000704600000, 0x0000000704600000, 0x0000000704800000|  0%| F|  |TAMS 0x0000000704600000, 0x0000000704600000| Untracked 
|  36|0x0000000704800000, 0x0000000704800000, 0x0000000704a00000|  0%| F|  |TAMS 0x0000000704800000, 0x0000000704800000| Untracked 
|  37|0x0000000704a00000, 0x0000000704a00000, 0x0000000704c00000|  0%| F|  |TAMS 0x0000000704a00000, 0x0000000704a00000| Untracked 
|  38|0x0000000704c00000, 0x0000000704c00000, 0x0000000704e00000|  0%| F|  |TAMS 0x0000000704c00000, 0x0000000704c00000| Untracked 
|  39|0x0000000704e00000, 0x0000000704e00000, 0x0000000705000000|  0%| F|  |TAMS 0x0000000704e00000, 0x0000000704e00000| Untracked 
|  40|0x0000000705000000, 0x0000000705000000, 0x0000000705200000|  0%| F|  |TAMS 0x0000000705000000, 0x0000000705000000| Untracked 
|  41|0x0000000705200000, 0x0000000705200000, 0x0000000705400000|  0%| F|  |TAMS 0x0000000705200000, 0x0000000705200000| Untracked 
|  42|0x0000000705400000, 0x0000000705400000, 0x0000000705600000|  0%| F|  |TAMS 0x0000000705400000, 0x0000000705400000| Untracked 
|  43|0x0000000705600000, 0x0000000705600000, 0x0000000705800000|  0%| F|  |TAMS 0x0000000705600000, 0x0000000705600000| Untracked 
|  96|0x000000070c000000, 0x000000070c000000, 0x000000070c200000|  0%| F|  |TAMS 0x000000070c000000, 0x000000070c000000| Untracked 
|  97|0x000000070c200000, 0x000000070c200000, 0x000000070c400000|  0%| F|  |TAMS 0x000000070c200000, 0x000000070c200000| Untracked 
|  98|0x000000070c400000, 0x000000070c400000, 0x000000070c600000|  0%| F|  |TAMS 0x000000070c400000, 0x000000070c400000| Untracked 
|  99|0x000000070c600000, 0x000000070c600000, 0x000000070c800000|  0%| F|  |TAMS 0x000000070c600000, 0x000000070c600000| Untracked 
| 126|0x000000070fc00000, 0x000000070fd5d548, 0x000000070fe00000| 68%| E|  |TAMS 0x000000070fc00000, 0x000000070fc00000| Complete 

Card table byte_map: [0x000001cb158c0000,0x000001cb160c0000] _byte_map_base: 0x000001cb120c0000

Marking Bits (Prev, Next): (CMBitMap*) 0x000001cb01bf2370, (CMBitMap*) 0x000001cb01bf2330
 Prev Bits: [0x000001cb1a8c0000, 0x000001cb1e8c0000)
 Next Bits: [0x000001cb168c0000, 0x000001cb1a8c0000)

Polling page: 0x000001cb01c40000

Metaspace:

Usage:
  Non-class:     53.55 MB used.
      Class:      8.55 MB used.
       Both:     62.10 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,      53.88 MB ( 84%) committed,  1 nodes.
      Class space:        1.00 GB reserved,       8.81 MB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,      62.69 MB (  6%) committed. 

Chunk freelists:
   Non-Class:  10.03 MB
       Class:  7.23 MB
        Both:  17.27 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 98.69 MB
CDS: off
MetaspaceReclaimPolicy: balanced
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - new_chunks_are_fully_committed: 0.
 - uncommit_free_chunks: 1.
 - use_allocation_guard: 0.
 - handle_deallocations: 1.


Internal statistics:

num_allocs_failed_limit: 9.
num_arena_births: 634.
num_arena_deaths: 0.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 1003.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 9.
num_chunks_taken_from_freelist: 2749.
num_chunk_merges: 6.
num_chunk_splits: 1847.
num_chunks_enlarged: 1288.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=119168Kb used=3527Kb max_used=3527Kb free=115640Kb
 bounds [0x000001cb0ccb0000, 0x000001cb0d030000, 0x000001cb14110000]
CodeHeap 'profiled nmethods': size=119104Kb used=11701Kb max_used=11701Kb free=107402Kb
 bounds [0x000001cb05110000, 0x000001cb05c80000, 0x000001cb0c560000]
CodeHeap 'non-nmethods': size=7488Kb used=4039Kb max_used=4139Kb free=3448Kb
 bounds [0x000001cb0c560000, 0x000001cb0c990000, 0x000001cb0ccb0000]
 total_blobs=6707 nmethods=5834 adapters=782
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 12.242 Thread 0x000001cb26f34c40 6559       1       org.gradle.api.internal.artifacts.ivyservice.resolveengine.graph.builder.SelectorState::getTargetModule (5 bytes)
Event: 12.242 Thread 0x000001cb26d641d0 nmethod 6557 0x000001cb05c6bf90 code [0x000001cb05c6c140, 0x000001cb05c6c598]
Event: 12.242 Thread 0x000001cb26f34c40 nmethod 6559 0x000001cb0d020210 code [0x000001cb0d0203a0, 0x000001cb0d020478]
Event: 12.242 Thread 0x000001cb26d641d0 6561       1       org.gradle.api.internal.artifacts.DefaultModuleVersionIdentifier::hashCode (5 bytes)
Event: 12.242 Thread 0x000001cb26f34c40 6562       1       org.gradle.api.internal.artifacts.ivyservice.resolveengine.graph.builder.ModuleResolveState::getId (5 bytes)
Event: 12.242 Thread 0x000001cb21a3ba20 nmethod 6558 0x000001cb05c6c690 code [0x000001cb05c6c880, 0x000001cb05c6d048]
Event: 12.242 Thread 0x000001cb26d641d0 nmethod 6561 0x000001cb0d020510 code [0x000001cb0d0206a0, 0x000001cb0d020778]
Event: 12.242 Thread 0x000001cb26f34c40 nmethod 6562 0x000001cb0d020810 code [0x000001cb0d0209a0, 0x000001cb0d020a78]
Event: 12.250 Thread 0x000001cb26f34c40 6563       1       org.gradle.api.internal.artifacts.ivyservice.resolveengine.graph.builder.SelectorState::getVersionConstraint (5 bytes)
Event: 12.250 Thread 0x000001cb26d9d390 nmethod 6560 0x000001cb05c6d210 code [0x000001cb05c6d460, 0x000001cb05c6db88]
Event: 12.250 Thread 0x000001cb21a3ba20 6564       1       org.gradle.api.internal.artifacts.ivyservice.resolveengine.graph.builder.EdgeState::getSelector (5 bytes)
Event: 12.250 Thread 0x000001cb21a3ba20 nmethod 6564 0x000001cb0d020b10 code [0x000001cb0d020ca0, 0x000001cb0d020d78]
Event: 12.250 Thread 0x000001cb26f34c40 nmethod 6563 0x000001cb0d020e10 code [0x000001cb0d020fa0, 0x000001cb0d021078]
Event: 12.253 Thread 0x000001cb26f34c40 6565       1       org.gradle.internal.component.external.model.DefaultModuleComponentIdentifier::hashCode (5 bytes)
Event: 12.254 Thread 0x000001cb26f34c40 nmethod 6565 0x000001cb0d021110 code [0x000001cb0d0212a0, 0x000001cb0d021378]
Event: 12.256 Thread 0x000001cb26d641d0 6566       1       org.gradle.api.internal.artifacts.DefaultModuleVersionIdentifier::getVersion (5 bytes)
Event: 12.256 Thread 0x000001cb26d641d0 nmethod 6566 0x000001cb0d021410 code [0x000001cb0d0215a0, 0x000001cb0d021678]
Event: 12.266 Thread 0x000001cb26d641d0 6567       3       org.gradle.api.internal.artifacts.ivyservice.resolveengine.result.DesugaredAttributeContainerSerializer::read (155 bytes)
Event: 12.267 Thread 0x000001cb26d641d0 nmethod 6567 0x000001cb05c6de90 code [0x000001cb05c6e1a0, 0x000001cb05c6f578]
Event: 12.268 Thread 0x000001cb26d9d390 6568       3       org.gradle.api.internal.artifacts.ivyservice.modulecache.ModuleMetadataSerializer$Reader::readCount (10 bytes)

GC Heap History (20 events):
Event: 5.196 GC heap before
{Heap before GC invocations=5 (full 0):
 garbage-first heap   total 69632K, used 22301K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 3 young (6144K), 2 survivors (4096K)
 Metaspace       used 26774K, committed 27072K, reserved 1114112K
  class space    used 3498K, committed 3648K, reserved 1048576K
}
Event: 5.200 GC heap after
{Heap after GC invocations=6 (full 0):
 garbage-first heap   total 69632K, used 20629K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 1 young (2048K), 1 survivors (2048K)
 Metaspace       used 26774K, committed 27072K, reserved 1114112K
  class space    used 3498K, committed 3648K, reserved 1048576K
}
Event: 5.823 GC heap before
{Heap before GC invocations=6 (full 0):
 garbage-first heap   total 69632K, used 47253K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 14 young (28672K), 1 survivors (2048K)
 Metaspace       used 30750K, committed 31104K, reserved 1114112K
  class space    used 4145K, committed 4288K, reserved 1048576K
}
Event: 5.825 GC heap after
{Heap after GC invocations=7 (full 0):
 garbage-first heap   total 69632K, used 21545K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 1 young (2048K), 1 survivors (2048K)
 Metaspace       used 30750K, committed 31104K, reserved 1114112K
  class space    used 4145K, committed 4288K, reserved 1048576K
}
Event: 6.337 GC heap before
{Heap before GC invocations=7 (full 0):
 garbage-first heap   total 69632K, used 44073K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 13 young (26624K), 1 survivors (2048K)
 Metaspace       used 35861K, committed 36160K, reserved 1114112K
  class space    used 4729K, committed 4864K, reserved 1048576K
}
Event: 6.341 GC heap after
{Heap after GC invocations=8 (full 0):
 garbage-first heap   total 69632K, used 23539K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 2 young (4096K), 2 survivors (4096K)
 Metaspace       used 35861K, committed 36160K, reserved 1114112K
  class space    used 4729K, committed 4864K, reserved 1048576K
}
Event: 6.755 GC heap before
{Heap before GC invocations=9 (full 0):
 garbage-first heap   total 69632K, used 50163K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 15 young (30720K), 2 survivors (4096K)
 Metaspace       used 38903K, committed 39232K, reserved 1114112K
  class space    used 5098K, committed 5248K, reserved 1048576K
}
Event: 6.760 GC heap after
{Heap after GC invocations=10 (full 0):
 garbage-first heap   total 69632K, used 26342K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 2 young (4096K), 2 survivors (4096K)
 Metaspace       used 38903K, committed 39232K, reserved 1114112K
  class space    used 5098K, committed 5248K, reserved 1048576K
}
Event: 7.277 GC heap before
{Heap before GC invocations=10 (full 0):
 garbage-first heap   total 69632K, used 50918K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 14 young (28672K), 2 survivors (4096K)
 Metaspace       used 41431K, committed 41792K, reserved 1114112K
  class space    used 5357K, committed 5504K, reserved 1048576K
}
Event: 7.286 GC heap after
{Heap after GC invocations=11 (full 0):
 garbage-first heap   total 100352K, used 28802K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 2 young (4096K), 2 survivors (4096K)
 Metaspace       used 41431K, committed 41792K, reserved 1114112K
  class space    used 5357K, committed 5504K, reserved 1048576K
}
Event: 8.133 GC heap before
{Heap before GC invocations=11 (full 0):
 garbage-first heap   total 100352K, used 71810K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 23 young (47104K), 2 survivors (4096K)
 Metaspace       used 45060K, committed 45504K, reserved 1114112K
  class space    used 5856K, committed 6080K, reserved 1048576K
}
Event: 8.136 GC heap after
{Heap after GC invocations=12 (full 0):
 garbage-first heap   total 100352K, used 31335K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 3 young (6144K), 3 survivors (6144K)
 Metaspace       used 45060K, committed 45504K, reserved 1114112K
  class space    used 5856K, committed 6080K, reserved 1048576K
}
Event: 9.297 GC heap before
{Heap before GC invocations=12 (full 0):
 garbage-first heap   total 100352K, used 74343K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 24 young (49152K), 3 survivors (6144K)
 Metaspace       used 51115K, committed 51584K, reserved 1114112K
  class space    used 6837K, committed 7040K, reserved 1048576K
}
Event: 9.302 GC heap after
{Heap after GC invocations=13 (full 0):
 garbage-first heap   total 100352K, used 33935K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 3 young (6144K), 3 survivors (6144K)
 Metaspace       used 51115K, committed 51584K, reserved 1114112K
  class space    used 6837K, committed 7040K, reserved 1048576K
}
Event: 10.385 GC heap before
{Heap before GC invocations=13 (full 0):
 garbage-first heap   total 100352K, used 76943K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 24 young (49152K), 3 survivors (6144K)
 Metaspace       used 56248K, committed 56704K, reserved 1114112K
  class space    used 7558K, committed 7744K, reserved 1048576K
}
Event: 10.391 GC heap after
{Heap after GC invocations=14 (full 0):
 garbage-first heap   total 100352K, used 36267K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 3 young (6144K), 3 survivors (6144K)
 Metaspace       used 56248K, committed 56704K, reserved 1114112K
  class space    used 7558K, committed 7744K, reserved 1048576K
}
Event: 11.224 GC heap before
{Heap before GC invocations=14 (full 0):
 garbage-first heap   total 100352K, used 64939K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 19 young (38912K), 3 survivors (6144K)
 Metaspace       used 59843K, committed 60416K, reserved 1114112K
  class space    used 8075K, committed 8320K, reserved 1048576K
}
Event: 11.229 GC heap after
{Heap after GC invocations=15 (full 0):
 garbage-first heap   total 100352K, used 38981K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 3 young (6144K), 3 survivors (6144K)
 Metaspace       used 59843K, committed 60416K, reserved 1114112K
  class space    used 8075K, committed 8320K, reserved 1048576K
}
Event: 12.242 GC heap before
{Heap before GC invocations=16 (full 0):
 garbage-first heap   total 100352K, used 77893K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 22 young (45056K), 3 survivors (6144K)
 Metaspace       used 63570K, committed 64128K, reserved 1114112K
  class space    used 8752K, committed 9024K, reserved 1048576K
}
Event: 12.250 GC heap after
{Heap after GC invocations=17 (full 0):
 garbage-first heap   total 100352K, used 42557K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 3 young (6144K), 3 survivors (6144K)
 Metaspace       used 63570K, committed 64128K, reserved 1114112K
  class space    used 8752K, committed 9024K, reserved 1048576K
}

Dll operation events (2 events):
Event: 0.009 Loaded shared library C:\Program Files\Android\Android Studio\jbr\bin\java.dll
Event: 1.692 Loaded shared library C:\Program Files\Android\Android Studio\jbr\bin\verify.dll

Deoptimization events (20 events):
Event: 12.130 Thread 0x000001cb2c1e6150 DEOPT PACKING pc=0x000001cb0cff0a6c sp=0x000000c3c85fac10
Event: 12.130 Thread 0x000001cb2c1e6150 DEOPT UNPACKING pc=0x000001cb0c5b69a3 sp=0x000000c3c85faba0 mode 2
Event: 12.176 Thread 0x000001cb2c1e6150 Uncommon trap: trap_request=0xffffffde fr.pc=0x000001cb0cdbf774 relative=0x00000000000002d4
Event: 12.176 Thread 0x000001cb2c1e6150 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000001cb0cdbf774 method=java.util.Collections$SetFromMap.remove(Ljava/lang/Object;)Z @ 5 c2
Event: 12.176 Thread 0x000001cb2c1e6150 DEOPT PACKING pc=0x000001cb0cdbf774 sp=0x000000c3c85fee40
Event: 12.176 Thread 0x000001cb2c1e6150 DEOPT UNPACKING pc=0x000001cb0c5b69a3 sp=0x000000c3c85fee28 mode 2
Event: 12.190 Thread 0x000001cb27c2ef50 DEOPT PACKING pc=0x000001cb0523b2f1 sp=0x000000c3c65fed30
Event: 12.190 Thread 0x000001cb27c2ef50 DEOPT UNPACKING pc=0x000001cb0c5b7143 sp=0x000000c3c65fe1d0 mode 0
Event: 12.194 Thread 0x000001cb2c1e6150 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000001cb0cd9bbb0 relative=0x0000000000000210
Event: 12.194 Thread 0x000001cb2c1e6150 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000001cb0cd9bbb0 method=java.util.StringTokenizer.skipDelimiters(I)I @ 64 c2
Event: 12.194 Thread 0x000001cb2c1e6150 DEOPT PACKING pc=0x000001cb0cd9bbb0 sp=0x000000c3c85fcc40
Event: 12.194 Thread 0x000001cb2c1e6150 DEOPT UNPACKING pc=0x000001cb0c5b69a3 sp=0x000000c3c85fcbd0 mode 2
Event: 12.194 Thread 0x000001cb2c1e6150 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000001cb0cd72864 relative=0x0000000000000244
Event: 12.194 Thread 0x000001cb2c1e6150 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000001cb0cd72864 method=java.util.StringTokenizer.scanToken(I)I @ 42 c2
Event: 12.194 Thread 0x000001cb2c1e6150 DEOPT PACKING pc=0x000001cb0cd72864 sp=0x000000c3c85fcc90
Event: 12.194 Thread 0x000001cb2c1e6150 DEOPT UNPACKING pc=0x000001cb0c5b69a3 sp=0x000000c3c85fcc20 mode 2
Event: 12.201 Thread 0x000001cb2c33b580 Uncommon trap: trap_request=0xffffffde fr.pc=0x000001cb0ce9d948 relative=0x00000000000008c8
Event: 12.201 Thread 0x000001cb2c33b580 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000001cb0ce9d948 method=java.io.BufferedInputStream.read1([BII)I @ 39 c2
Event: 12.201 Thread 0x000001cb2c33b580 DEOPT PACKING pc=0x000001cb0ce9d948 sp=0x000000c3c94fe080
Event: 12.201 Thread 0x000001cb2c33b580 DEOPT UNPACKING pc=0x000001cb0c5b69a3 sp=0x000000c3c94fdf98 mode 2

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 9.645 Thread 0x000001cb27c31100 Exception <a 'java/lang/ClassNotFoundException'{0x00000007055ae230}: org/gradle/api/plugins/internal/DefaultBasePluginConventionBeanInfo> (0x00000007055ae230) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 9.647 Thread 0x000001cb27c31100 Exception <a 'java/lang/ClassNotFoundException'{0x00000007055c63a0}: org/gradle/api/plugins/BasePluginConventionBeanInfo> (0x00000007055c63a0) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 9.650 Thread 0x000001cb27c31100 Exception <a 'java/lang/ClassNotFoundException'{0x00000007055de4d8}: org/gradle/api/plugins/BasePluginConventionCustomizer> (0x00000007055de4d8) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 9.654 Thread 0x000001cb27c31100 Exception <a 'java/lang/ClassNotFoundException'{0x0000000705201758}: org/gradle/api/plugins/internal/DefaultBasePluginConventionCustomizer> (0x0000000705201758) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 9.661 Thread 0x000001cb27c31100 Exception <a 'java/lang/ClassNotFoundException'{0x0000000705254898}: org/gradle/api/plugins/internal/DefaultJavaPluginConventionBeanInfo> (0x0000000705254898) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 9.662 Thread 0x000001cb27c31100 Exception <a 'java/lang/ClassNotFoundException'{0x000000070526ca08}: org/gradle/api/plugins/JavaPluginConventionBeanInfo> (0x000000070526ca08) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 9.664 Thread 0x000001cb27c31100 Exception <a 'java/lang/ClassNotFoundException'{0x0000000705284cf0}: org/gradle/api/plugins/JavaPluginConventionCustomizer> (0x0000000705284cf0) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 9.666 Thread 0x000001cb27c31100 Exception <a 'java/lang/ClassNotFoundException'{0x00000007052b1700}: org/gradle/api/plugins/internal/DefaultJavaPluginConventionCustomizer> (0x00000007052b1700) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 10.998 Thread 0x000001cb27c31100 Exception <a 'java/lang/IncompatibleClassChangeError'{0x0000000704db6ea0}: Found class java.lang.Object, but interface was expected> (0x0000000704db6ea0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 838]
Event: 10.998 Thread 0x000001cb27c31100 Exception <a 'java/lang/NoSuchMethodError'{0x0000000704dbe950}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeSpecialIFC(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000704dbe950) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 771]
Event: 11.146 Thread 0x000001cb27c31100 Exception <a 'java/lang/NoSuchMethodError'{0x00000007046711c0}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object)'> (0x00000007046711c0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 771]
Event: 11.146 Thread 0x000001cb27c31100 Exception <a 'java/lang/NoSuchMethodError'{0x00000007046755b0}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object)'> (0x00000007046755b0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 771]
Event: 11.168 Thread 0x000001cb27c31100 Exception <a 'java/lang/NoSuchMethodError'{0x00000007047a4f80}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x00000007047a4f80) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 771]
Event: 11.341 Thread 0x000001cb27c31100 Exception <a 'java/lang/NoSuchMethodError'{0x000000070c4190f0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, int)'> (0x000000070c4190f0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 771]
Event: 11.645 Thread 0x000001cb27c31100 Exception <a 'java/lang/NoSuchMethodError'{0x000000070517abc8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, int, java.lang.Object)'> (0x000000070517abc8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 771]
Event: 11.683 Thread 0x000001cb27c31100 Exception <a 'java/lang/NoSuchMethodError'{0x0000000704ecef58}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeInterface(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000704ecef58) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 771]
Event: 11.802 Thread 0x000001cb27c31100 Implicit null exception at 0x000001cb0ce93b60 to 0x000001cb0ce93b88
Event: 11.850 Thread 0x000001cb2c1e6150 Exception <a 'java/lang/NoSuchMethodError'{0x000000070466cf78}: 'java.lang.Object java.lang.invoke.Invokers$Holder.linkToTargetMethod(java.lang.Object, java.lang.Object, java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0x000000070466cf78) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 771]
Event: 11.915 Thread 0x000001cb2c1e6150 Exception <a 'java/lang/NoSuchMethodError'{0x00000007047e9088}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000007047e9088) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 771]
Event: 12.161 Thread 0x000001cb2c1e6150 Exception <a 'java/lang/NoSuchMethodError'{0x000000070364cbc0}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeInterface(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000070364cbc0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 771]

VM Operations (20 events):
Event: 11.285 Executing VM operation: G1Concurrent done
Event: 11.659 Executing VM operation: HandshakeAllThreads
Event: 11.659 Executing VM operation: HandshakeAllThreads done
Event: 11.676 Executing VM operation: HandshakeAllThreads
Event: 11.676 Executing VM operation: HandshakeAllThreads done
Event: 11.679 Executing VM operation: HandshakeAllThreads
Event: 11.679 Executing VM operation: HandshakeAllThreads done
Event: 11.701 Executing VM operation: ICBufferFull
Event: 11.702 Executing VM operation: ICBufferFull done
Event: 11.840 Executing VM operation: ICBufferFull
Event: 11.841 Executing VM operation: ICBufferFull done
Event: 11.913 Executing VM operation: HandshakeAllThreads
Event: 11.913 Executing VM operation: HandshakeAllThreads done
Event: 11.926 Executing VM operation: HandshakeAllThreads
Event: 11.926 Executing VM operation: HandshakeAllThreads done
Event: 12.196 Executing VM operation: ICBufferFull
Event: 12.196 Executing VM operation: ICBufferFull done
Event: 12.242 Executing VM operation: G1CollectForAllocation
Event: 12.250 Executing VM operation: G1CollectForAllocation done
Event: 13.261 Executing VM operation: Cleanup

Events (20 events):
Event: 11.835 Thread 0x000001cb2c33abe0 Thread added: 0x000001cb2c33abe0
Event: 11.835 Thread 0x000001cb2c3393d0 Thread added: 0x000001cb2c3393d0
Event: 11.835 Thread 0x000001cb2c3398a0 Thread added: 0x000001cb2c3398a0
Event: 11.836 Thread 0x000001cb2c33ba50 Thread added: 0x000001cb2c33ba50
Event: 11.836 Thread 0x000001cb2c339d70 Thread added: 0x000001cb2c339d70
Event: 11.836 Thread 0x000001cb2c338f00 Thread added: 0x000001cb2c338f00
Event: 11.836 Thread 0x000001cb2c33a240 Thread added: 0x000001cb2c33a240
Event: 11.865 Thread 0x000001cb2c33b580 Thread added: 0x000001cb2c33b580
Event: 12.090 Thread 0x000001cb2c338090 Thread added: 0x000001cb2c338090
Event: 12.090 Thread 0x000001cb2c33bf20 Thread added: 0x000001cb2c33bf20
Event: 12.090 Thread 0x000001cb2c338560 Thread added: 0x000001cb2c338560
Event: 12.090 Thread 0x000001cb2c33c3f0 Thread added: 0x000001cb2c33c3f0
Event: 12.090 Thread 0x000001cb2c33a710 Thread added: 0x000001cb2c33a710
Event: 12.090 Thread 0x000001cb2c338a30 Thread added: 0x000001cb2c338a30
Event: 12.091 Thread 0x000001cb2c33c8c0 Thread added: 0x000001cb2c33c8c0
Event: 12.091 Thread 0x000001cb2c33cd90 Thread added: 0x000001cb2c33cd90
Event: 12.091 Thread 0x000001cb2c33d260 Thread added: 0x000001cb2c33d260
Event: 12.091 Thread 0x000001cb2c33d730 Thread added: 0x000001cb2c33d730
Event: 12.226 loading class java/util/concurrent/ConcurrentLinkedQueue$Itr
Event: 12.226 loading class java/util/concurrent/ConcurrentLinkedQueue$Itr done


Dynamic libraries:
0x00007ff706710000 - 0x00007ff70671a000 	C:\Program Files\Android\Android Studio\jbr\bin\java.exe
0x00007ffb87780000 - 0x00007ffb879e0000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ffb863b0000 - 0x00007ffb86477000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ffb84bf0000 - 0x00007ffb84fba000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ffb84fc0000 - 0x00007ffb8510b000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ffb59540000 - 0x00007ffb59557000 	C:\Program Files\Android\Android Studio\jbr\bin\jli.dll
0x00007ffb86480000 - 0x00007ffb8664c000 	C:\WINDOWS\System32\USER32.dll
0x00007ffb59520000 - 0x00007ffb5953b000 	C:\Program Files\Android\Android Studio\jbr\bin\VCRUNTIME140.dll
0x00007ffb47d40000 - 0x00007ffb47fd7000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.3624_none_3e086962e3345f24\COMCTL32.dll
0x00007ffb85550000 - 0x00007ffb85577000 	C:\WINDOWS\System32\win32u.dll
0x00007ffb85580000 - 0x00007ffb855aa000 	C:\WINDOWS\System32\GDI32.dll
0x00007ffb86870000 - 0x00007ffb86919000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ffb84a00000 - 0x00007ffb84b31000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ffb84b40000 - 0x00007ffb84be3000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffb85b70000 - 0x00007ffb85b9f000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ffb6c850000 - 0x00007ffb6c85c000 	C:\Program Files\Android\Android Studio\jbr\bin\vcruntime140_1.dll
0x00007ffa69320000 - 0x00007ffa693ad000 	C:\Program Files\Android\Android Studio\jbr\bin\msvcp140.dll
0x00007ffa645d0000 - 0x00007ffa65250000 	C:\Program Files\Android\Android Studio\jbr\bin\server\jvm.dll
0x00007ffb86970000 - 0x00007ffb86a22000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ffb86660000 - 0x00007ffb86706000 	C:\WINDOWS\System32\sechost.dll
0x00007ffb86710000 - 0x00007ffb86826000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffb73320000 - 0x00007ffb73356000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ffb6cfa0000 - 0x00007ffb6cfaa000 	C:\WINDOWS\SYSTEM32\WSOCK32.dll
0x00007ffb78740000 - 0x00007ffb7874b000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ffb861d0000 - 0x00007ffb86244000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ffb83870000 - 0x00007ffb8388a000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ffb0e950000 - 0x00007ffb0e95a000 	C:\Program Files\Android\Android Studio\jbr\bin\jimage.dll
0x00007ffb82a40000 - 0x00007ffb82c81000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ffb87240000 - 0x00007ffb875c2000 	C:\WINDOWS\System32\combase.dll
0x00007ffb87160000 - 0x00007ffb87236000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ffb73250000 - 0x00007ffb73289000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ffb84960000 - 0x00007ffb849f9000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ffb0c5d0000 - 0x00007ffb0c5f5000 	C:\Program Files\Android\Android Studio\jbr\bin\java.dll
0x00007ffb0c5b0000 - 0x00007ffb0c5c8000 	C:\Program Files\Android\Android Studio\jbr\bin\zip.dll
0x00007ffb86a30000 - 0x00007ffb87159000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ffb85290000 - 0x00007ffb853f8000 	C:\WINDOWS\System32\wintypes.dll
0x00007ffb82110000 - 0x00007ffb82962000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ffb86250000 - 0x00007ffb8633d000 	C:\WINDOWS\System32\SHCORE.dll
0x00007ffb876d0000 - 0x00007ffb87734000 	C:\WINDOWS\System32\shlwapi.dll
0x00007ffb84870000 - 0x00007ffb8489f000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ffb0c590000 - 0x00007ffb0c5a9000 	C:\Program Files\Android\Android Studio\jbr\bin\net.dll
0x00007ffb7e270000 - 0x00007ffb7e38d000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007ffb83db0000 - 0x00007ffb83e1a000 	C:\WINDOWS\system32\mswsock.dll
0x00007ffa932f0000 - 0x00007ffa93306000 	C:\Program Files\Android\Android Studio\jbr\bin\nio.dll
0x00007ffb0e8e0000 - 0x00007ffb0e8f0000 	C:\Program Files\Android\Android Studio\jbr\bin\verify.dll
0x00007ffa911a0000 - 0x00007ffa911c7000 	C:\Users\<USER>\.gradle\native\e1d6ef7f7dcc3fd88c89a11ec53ec762bb8ba0a96d01ffa2cd45eb1d1d8dd5c5\windows-amd64\native-platform.dll
0x00007ffa66360000 - 0x00007ffa664a4000 	C:\Users\<USER>\.gradle\native\0d407fdbe67a94daf76414ababcb853783967236a71b16ec16e742cd7a986fd3\windows-amd64\native-platform-file-events.dll
0x00007ffac10a0000 - 0x00007ffac10a9000 	C:\Program Files\Android\Android Studio\jbr\bin\management.dll
0x00007ffac0c60000 - 0x00007ffac0c6b000 	C:\Program Files\Android\Android Studio\jbr\bin\management_ext.dll
0x00007ffb86860000 - 0x00007ffb86868000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007ffb84050000 - 0x00007ffb8406c000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007ffb837d0000 - 0x00007ffb8380a000 	C:\WINDOWS\system32\rsaenh.dll
0x00007ffb83e50000 - 0x00007ffb83e7b000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007ffb84840000 - 0x00007ffb84866000 	C:\WINDOWS\SYSTEM32\bcrypt.dll
0x00007ffb84070000 - 0x00007ffb8407c000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007ffb831e0000 - 0x00007ffb83213000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007ffb86650000 - 0x00007ffb8665a000 	C:\WINDOWS\System32\NSI.dll
0x00007ffb7dba0000 - 0x00007ffb7dbbf000 	C:\WINDOWS\SYSTEM32\dhcpcsvc6.DLL
0x00007ffb7d700000 - 0x00007ffb7d725000 	C:\WINDOWS\SYSTEM32\dhcpcsvc.DLL
0x00007ffb83270000 - 0x00007ffb83395000 	C:\WINDOWS\SYSTEM32\DNSAPI.dll
0x00007ffae7e30000 - 0x00007ffae7e38000 	C:\WINDOWS\system32\wshunix.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Program Files\Android\Android Studio\jbr\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.3624_none_3e086962e3345f24;C:\Program Files\Android\Android Studio\jbr\bin\server;C:\Users\<USER>\.gradle\native\e1d6ef7f7dcc3fd88c89a11ec53ec762bb8ba0a96d01ffa2cd45eb1d1d8dd5c5\windows-amd64;C:\Users\<USER>\.gradle\native\0d407fdbe67a94daf76414ababcb853783967236a71b16ec16e742cd7a986fd3\windows-amd64

VM Arguments:
jvm_args: --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED -Xmx4G -Dfile.encoding=windows-1252 -Duser.country=US -Duser.language=en -Duser.variant 
java_command: org.gradle.launcher.daemon.bootstrap.GradleDaemon 7.5
java_class_path (initial): C:\Users\<USER>\.gradle\wrapper\dists\gradle-7.5-all\6qsw290k5lz422uaf8jf6m7co\gradle-7.5\lib\gradle-launcher-7.5.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 12                                        {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 13                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 2097152                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 266338304                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 4294967296                                {product} {command line}
   size_t MaxNewSize                               = 2575302656                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 2097152                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 7602480                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122027880                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122027880                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 4294967296                             {manageable} {ergonomic}
     bool UseCompressedClassPointers               = true                           {product lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
JAVA_HOME=C:\Program Files\Android\Android Studio\jbr
CLASSPATH=D:\Ecommerce-App\android\\gradle\wrapper\gradle-wrapper.jar
PATH=C:\Program Files\Android\Android Studio\jbr\bin;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Microsoft SQL Server\150\Tools\Binn\;C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\170\Tools\Binn\;C:\Program Files\dotnet\;C:\Program Files (x86)\Microsoft SQL Server\160\Tools\Binn\;C:\Program Files\Microsoft SQL Server\160\Tools\Binn\;C:\Program Files\Microsoft SQL Server\160\DTS\Binn\;C:\Program Files (x86)\Microsoft SQL Server\160\DTS\Binn\;C:\Program Files\Azure Data Studio\bin;C:\Program Files\Git\cmd;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\.dotnet\tools;C:\Program Files\Azure Data Studio\bin;C:\flutter\bin;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin;;C:\flutter\bin\mingit\cmd;C:\flutter\bin\mingit\cmd
USERNAME=zaher
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 165 Stepping 5, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp


JNI global refs:
JNI global refs: 32, weak refs: 0

JNI global refs memory usage: 843, weak refs: 841

OOME stack traces (most recent first):
Classloader memory used:
Loader org.gradle.internal.classloader.VisitableURLClassLoader                         : 4101K
Loader bootstrap                                                                       : 2584K
Loader org.gradle.initialization.MixInLegacyTypesClassLoader                           : 1148K
Loader jdk.internal.loader.ClassLoaders$PlatformClassLoader                            : 65182B
Loader jdk.internal.loader.ClassLoaders$AppClassLoader                                 : 25681B
Loader jdk.internal.reflect.DelegatingClassLoader                                      : 12613B
Loader org.gradle.groovy.scripts.internal.DefaultScriptCompilationHandler$ScriptClassLoader: 5804B
Loader org.gradle.internal.classloader.VisitableURLClassLoader                         : 5193B
Loader org.codehaus.groovy.runtime.callsite.CallSiteClassLoader                        : 1991B
Loader sun.reflect.misc.MethodUtil                                                     : 373B

Classes loaded by more than one classloader:
Class Program                                                                         : loaded 3 times (x 70B)
Class org.gradle.internal.classpath.ClassPath                                         : loaded 2 times (x 68B)
Class org.gradle.api.internal.classpath.DefaultModuleRegistry                         : loaded 2 times (x 84B)
Class org.gradle.api.Action                                                           : loaded 2 times (x 68B)
Class org.gradle.api.internal.DefaultClassPathProvider                                : loaded 2 times (x 74B)
Class org.gradle.internal.installation.GradleInstallation$1                           : loaded 2 times (x 73B)
Class org.gradle.internal.classloader.FilteringClassLoader                            : loaded 2 times (x 102B)
Class org.gradle.internal.classpath.DefaultClassPath$ImmutableUniqueList$Builder      : loaded 2 times (x 73B)
Class org.gradle.internal.classloader.ClassLoaderVisitor                              : loaded 2 times (x 74B)
Class org.gradle.internal.service.DefaultServiceLocator                               : loaded 2 times (x 81B)
Class org.gradle.internal.service.UnknownServiceException                             : loaded 2 times (x 81B)
Class org.gradle.internal.classloader.DefaultClassLoaderFactory                       : loaded 2 times (x 80B)
Class org.gradle.internal.classloader.VisitableURLClassLoader$Spec                    : loaded 2 times (x 72B)
Class org.gradle.api.GradleException                                                  : loaded 2 times (x 80B)
Class org.gradle.api.internal.classpath.ModuleRegistry                                : loaded 2 times (x 68B)
Class org.gradle.api.internal.ClassPathProvider                                       : loaded 2 times (x 68B)
Class org.gradle.internal.classloader.ClasspathUtil$1                                 : loaded 2 times (x 74B)
Class org.gradle.internal.classloader.ClassLoaderFactory                              : loaded 2 times (x 68B)
Class org.gradle.api.internal.classpath.ManifestUtil                                  : loaded 2 times (x 69B)
Class org.gradle.api.specs.Spec                                                       : loaded 2 times (x 68B)
Class org.gradle.internal.IoActions                                                   : loaded 2 times (x 69B)
Class org.gradle.api.internal.classpath.EffectiveClassPath                            : loaded 2 times (x 88B)
Class org.gradle.internal.installation.CurrentGradleInstallation                      : loaded 2 times (x 71B)
Class org.gradle.internal.installation.CurrentGradleInstallationLocator               : loaded 2 times (x 69B)
Class org.gradle.api.internal.classpath.Module                                        : loaded 2 times (x 68B)
Class org.gradle.internal.service.CachingServiceLocator                               : loaded 2 times (x 80B)
Class org.gradle.internal.classpath.DefaultClassPath                                  : loaded 2 times (x 88B)
Class org.gradle.api.Transformer                                                      : loaded 2 times (x 68B)
Class org.gradle.internal.classloader.SystemClassLoaderSpec                           : loaded 2 times (x 69B)
Class org.gradle.api.UncheckedIOException                                             : loaded 2 times (x 80B)
Class org.gradle.internal.installation.GradleInstallation                             : loaded 2 times (x 73B)
Class org.gradle.internal.classloader.ClasspathUtil                                   : loaded 2 times (x 69B)
Class org.gradle.api.internal.DefaultClassPathRegistry                                : loaded 2 times (x 74B)
Class org.gradle.api.internal.classpath.DefaultModuleRegistry$DefaultModule           : loaded 2 times (x 84B)
Class org.gradle.api.internal.classpath.UnknownModuleException                        : loaded 2 times (x 80B)
Class org.gradle.internal.service.ServiceLookupException                              : loaded 2 times (x 80B)
Class org.gradle.cache.GlobalCache                                                    : loaded 2 times (x 68B)
Class [Lorg.gradle.api.internal.ClassPathProvider;                                    : loaded 2 times (x 67B)
Class org.gradle.util.internal.GUtil                                                  : loaded 2 times (x 69B)
Class org.gradle.internal.classloader.ClassLoaderSpec                                 : loaded 2 times (x 69B)
Class org.gradle.internal.classloader.VisitableURLClassLoader                         : loaded 2 times (x 114B)
Class org.gradle.internal.Cast                                                        : loaded 2 times (x 69B)
Class Build_gradle                                                                    : loaded 2 times (x 128B)
Class org.gradle.api.internal.ClassPathRegistry                                       : loaded 2 times (x 68B)
Class org.gradle.internal.classloader.ClassLoaderHierarchy                            : loaded 2 times (x 68B)
Class org.gradle.internal.service.ServiceLocator                                      : loaded 2 times (x 68B)
Class org.gradle.internal.classpath.DefaultClassPath$ImmutableUniqueList              : loaded 2 times (x 159B)


---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 26100 (10.0.26100.3775)
OS uptime: 9 days 4:13 hours
Hyper-V role detected

CPU: total 16 (initial active 16) (8 cores per cpu, 2 threads per core) family 6 model 165 stepping 5 microcode 0xe0, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, fma, vzeroupper, clflush, clflushopt, hv

Memory: 4k page, system-wide physical 16195M (721M free)
TotalPageFile size 64338M (AvailPageFile size 5M)
current process WorkingSet (physical memory assigned to process): 313M, peak: 317M
current process commit charge ("private bytes"): 356M, peak: 405M

vm_info: OpenJDK 64-Bit Server VM (17.0.7+0-b2043.56-10550314) for windows-amd64 JRE (17.0.7+0-b2043.56-10550314), built on Jul 24 2023 18:27:45 by "androidbuild" with MS VC++ 16.10 / 16.11 (VS2019)

END.
