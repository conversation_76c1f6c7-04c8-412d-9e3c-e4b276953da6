import 'package:flutter/material.dart';
import 'package:alderishop/constants/constants.dart';
import 'package:intl_phone_field/countries.dart';
import 'package:intl_phone_field/intl_phone_field.dart';
import 'package:intl_phone_field/phone_number.dart';

class TextFieldWidget extends StatefulWidget {
  final TextInputType? keyboardType;
  final bool? isPassword;
  final bool? FullName;
  final String? text;
  final String? hintText;
  final Function? onChanged;
  final TextEditingController? controller;
  const TextFieldWidget({
    Key? key,
    this.keyboardType,
    this.text,
    this.hintText,
    this.isPassword,
    this.onChanged,
    this.controller,
    this.FullName,
  }) : super(key: key);

  @override
  State<TextFieldWidget> createState() => _TextFieldWidgetState();
}

class _TextFieldWidgetState extends State<TextFieldWidget> {
  bool _obscureText = true;
  TextEditingController _defulteController = TextEditingController();

  void _toggle() {
    setState(() {
      _obscureText = !_obscureText;
    });
  }

  @override
  void initState() {
    _defulteController = widget.controller ?? TextEditingController();

    super.initState();
  }

  @override
  void setState(VoidCallback fn) {
    super.setState(fn);
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 35,
      child: TextField(
        controller: _defulteController,
        onChanged: (value) {
          widget.onChanged!(value);
        },
        keyboardType: widget.keyboardType,
        obscureText: widget.isPassword == true ? _obscureText : false,
        decoration: InputDecoration(
          hintText: widget.hintText,
          hintStyle: TextStyle(color: AppColors.SOFT_GREY.withOpacity(0.3)),
          labelStyle: TextStyle(color: AppColors.BLACK_GREY),
          focusedBorder: UnderlineInputBorder(
            borderSide: BorderSide(color: AppColors.BLACK_GREY, width: 1),
          ),
          enabledBorder: UnderlineInputBorder(
            borderSide: BorderSide(color: Colors.transparent, width: 1),
          ),
          suffixIcon: widget.isPassword ?? false
              ? IconButton(
                  icon: Icon(
                    _obscureText ? Icons.visibility : Icons.visibility_off,
                    color: AppColors.BLACK_GREY,
                  ),
                  onPressed: _toggle,
                )
              : null,
        ),
      ),
    );
  }
}

//=======================================================

class MyPhoneField extends StatefulWidget {
  const MyPhoneField({
    Key? key,
    this.hintText,
    this.labelText,
    this.labelColor = const Color(0xff42474d),
    this.borderColor = const Color(0xff42474d),
    this.borderRadiusBottomLeft = 10,
    this.borderRadiusBottomRight = 10,
    this.borderRadiusTopRight = 10,
    this.borderRadiusTopLeft = 10,
    this.borderRadiusAll = 10,
    this.asideByAside = false,
    this.textInputAction = TextInputAction.done,
    required this.onChanged,
    required this.onCountryChanged,
  }) : super(key: key);
  final String? hintText;
  final String? labelText;
  final Color? labelColor;
  final Color? borderColor;
  final double? borderRadiusBottomLeft;
  final double? borderRadiusBottomRight;
  final double? borderRadiusTopRight;
  final double? borderRadiusTopLeft;
  final double? borderRadiusAll;

  final bool? asideByAside;
  final TextInputAction? textInputAction;
  final Function(PhoneNumber val) onChanged;
  final Function(Country val) onCountryChanged;
  @override
  State<MyPhoneField> createState() => _MyPhoneFieldState();
}
class _MyPhoneFieldState extends State<MyPhoneField> {
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          height: 35,
          child: IntlPhoneField(
            textInputAction: widget.textInputAction,
            initialCountryCode: 'IQ',
            decoration: InputDecoration(
              counterText: "",
              enabledBorder: UnderlineInputBorder(
                borderSide: BorderSide(color: AppColors.BLACK_COLOR, width: 1),
              ),
              focusedBorder: UnderlineInputBorder(
                borderSide: BorderSide(color: AppColors.BLACK_COLOR, width: 1),
              ),
              errorBorder:const UnderlineInputBorder(
                borderSide: BorderSide(color: Colors.red),
              ),
              focusedErrorBorder:const UnderlineInputBorder(
                borderSide: BorderSide(color: Colors.red),
              ),
              labelText: widget.labelText,
              hintText: widget.hintText,
              hintStyle: TextStyle(
                fontSize: 13,
                color: widget.labelColor,
           
              ),
            ),
            onChanged: widget.onChanged,
            onCountryChanged: widget.onCountryChanged,
          ),
        ),
        // Optional: Add a manual underline below if needed
        // Container(
        //   height: 1,
        //   color: AppColors.BLACK_COLOR,
        //   margin: EdgeInsets.only(top: 4),
        // ),
      ],
    );
  }
}

//=======================================================
