import 'package:alderishop/constants/constants.dart';
import 'package:alderishop/controllers/app_controller.dart';
import 'package:alderishop/views/home/<USER>/SearchPage.dart';
import 'package:flutter/material.dart';

class HomeHeader extends StatefulWidget {
  final bool disPlayFilter;
  const HomeHeader({super.key, this.disPlayFilter = true});

  @override
  State<HomeHeader> createState() => _SearchState();
}

class _SearchState extends State<HomeHeader> {
  var lang = AppController.currentLangId == 2;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const SearchPage()),
        );
      },
      child: Stack(
        children: [
          Container(
            width: AppController.W - 45,
            alignment: Alignment.centerLeft,
            height: 35,
            decoration: BoxDecoration(
              color:Colors.grey.shade200,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(lang ? 10 : 0),
                bottomLeft: Radius.circular(lang ? 10 : 0),
                topRight: Radius.circular(lang ? 10 : 10),
                bottomRight: Radius.circular(lang ? 10 : 10),
              ),
            ),
          ),
          Positioned(
            top: 0,
            left: 30,
            child: SizedBox(
              width: 40,
              height: 38,
              child: InkWell(
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(builder: (context) => const SearchPage()),
                  );
                },
                child:
              widget.disPlayFilter? const Image(
                  image: AssetImage('assets/img/new/Filter.png'),
                ):SizedBox()
              ),
            ),
          ),
          Positioned(
            top: 0,
            left: 0,
            child: Container(
              width: 35,
              height: 36,
              decoration: BoxDecoration(
                color: AppColors.brown,
                borderRadius: BorderRadius.circular(10),
              ),
              child: InkWell(
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(builder: (context) => SearchPage()),
                  );
                },

                
                child:Image.asset( 'assets/img/new/search1.png',height: 25,width: 25,color: AppColors.WHITE_COLOR,)
                
              
              ),
            ),
          ),
        ],
      ),
    );
  }
}
