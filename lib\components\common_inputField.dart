import 'package:flutter/material.dart';

class InputFieldWidget extends StatelessWidget {
  final TextEditingController controller;
  final String hint;
  final Color? color;
  final double? width;
  final double? fontSize;
  final bool enabled; // 👈 Add this

  const InputFieldWidget({
    Key? key,
    required this.controller,
    this.color,
    this.width,
    this.fontSize,
    required this.hint,
    this.enabled = true, // 👈 Default is editable
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 45,
      width: width,
      child: TextField(
        controller: controller,
        enabled: enabled, // 👈 Use enabled here
        style: TextStyle(
          fontSize: fontSize ?? 14,
          color: Colors.black,
        ),
        decoration: InputDecoration(
          hintText: hint,
          enabledBorder: UnderlineInputBorder(
            borderSide: BorderSide(color: color ?? Colors.grey),
          ),
          focusedBorder: UnderlineInputBorder(
            borderSide: BorderSide(color: color ?? Theme.of(context).primaryColor),
          ),
        ),
      ),
    );
  }
}
