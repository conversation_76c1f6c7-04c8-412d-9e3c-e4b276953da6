import 'package:alderishop/constants/constants.dart';
import 'package:flutter/material.dart';

class CustomInputField extends StatefulWidget {
  final String hintText;
  final bool isPassword;
  final TextInputType keyboardType;
  final ValueChanged<String> onChanged;
  final Color? backgroundColor;
  final double horizontalPadding;
  final double verticalPadding;
  final TextEditingController? controller;

  const CustomInputField({
    Key? key,
    required this.hintText,
    this.controller,
    this.isPassword = false,
    this.keyboardType = TextInputType.text,
    required this.onChanged,
    this.backgroundColor,
    this.horizontalPadding = 20,
    this.verticalPadding = 0,
  }) : super(key: key);

  @override
  _CustomInputFieldState createState() => _CustomInputFieldState();
}

class _CustomInputFieldState extends State<CustomInputField> {
  bool _isObscure = true;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 7),
      child: Sized<PERSON><PERSON>(
        height: 35,
        child: Row(
          children: [
            Expanded(
              child: TextField(
                controller: widget.controller,
                obscureText: widget.isPassword ? _isObscure : false,
                keyboardType: widget.keyboardType,
              decoration: InputDecoration(
                 contentPadding: EdgeInsets.only(bottom: 4),
          hintText: widget.hintText,
          hintStyle: TextStyle(
            color: AppColors.BLACK_COLOR,
            fontSize: 12,
          ),
          enabledBorder: UnderlineInputBorder(
            borderSide: BorderSide(color: AppColors.BLACK_COLOR, width: 1),
          ),
          focusedBorder: UnderlineInputBorder(
            borderSide: BorderSide(color: AppColors.BLACK_COLOR, width: 1),
          ),
          suffixIcon: widget.isPassword
        ? IconButton(
            icon: Icon(
              _isObscure ? Icons.visibility_off_outlined : Icons.visibility_outlined,
              color: AppColors.BLACK_COLOR,
              size: 20,
            ),
            onPressed: () {
              setState(() {
                _isObscure = !_isObscure;
              });
            },
          )
        : null,
        ),
        
                onChanged: widget.onChanged,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
