import 'package:alderishop/components/common_snckbar.dart';
import 'package:alderishop/constants/constants.dart';
import 'package:alderishop/controllers/app_controller.dart';
import 'package:alderishop/controllers/products_controller.dart';
import 'package:alderishop/data/model/Products/productModel/productModel.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';

class AttrinutesListWidget extends StatefulWidget {
  final Function onChose;

  const AttrinutesListWidget({Key? key, required this.onChose})
      : super(key: key);

  @override
  State<AttrinutesListWidget> createState() => _AttrinutesListWidgetState();
}

class _AttrinutesListWidgetState extends State<AttrinutesListWidget> {
  List<Options> selectedAtter = [];

  @override
  void initState() {
    super.initState();
  }

  Color hexToColor(String hex) {
    hex = hex.replaceAll("#", "");
    if (hex.length == 6) {
      return Color(int.parse("0xFF$hex"));
    } else if (hex.length == 8) {
      return Color(int.parse("0x$hex"));
    }
    return Colors.transparent;
  }

  @override
  Widget build(BuildContext context) {
    var data = Provider.of<ProductsController>(context).productDetailsAttribute;
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 15),
      child: Column(
        children: [
          SizedBox(
            height: AppController.h * 0.005,
          ),
          if (data != null && data.isNotEmpty)
            Column(
              children: _buildAttributeWidgets(data),
            ),
        ],
      ),
    );
  }

  List<Widget> _buildAttributeWidgets(List<ProductAttribute> attributes) {
    return attributes.map((attribute) {
      return _buildAttributeWidget(attribute);
    }).toList();
  }

  Widget _buildAttributeWidget(ProductAttribute attribute) {
    return Container(
      margin: const EdgeInsets.only(top: 10),
      child: Column(
        children: [
            Row(
              children: [
                Text(
                "${attribute.name} : ",
                style: TextStyle(
                  color: AppColors.PRIMARY_COLOR,
                  fontWeight: FontWeight.bold,
                  fontSize: 15,
                ),
                          ),
              ],
            ),
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: <Widget>[
            
              Expanded(
                child: Wrap(
                  spacing: 8,
                  children: attribute.options!.map<Widget>((option) {
                    return GestureDetector(
                      onTap: () {
                        print(
                            "Selected productAttributeId: ${option.productAttributeId}, Name: ${option.name}");
          
                        if (option.status == false && selectedAtter.isNotEmpty) {
                          errorMsg(context: context, title: "غير متوفر");
                          return;
                        }
                        var selectedFromSameType = selectedAtter.firstWhereOrNull(
                            (item) => item.productAttributeId == attribute.id);
                        if (selectedFromSameType != null) {
                          selectedAtter.remove(selectedFromSameType);
                        }
          
                        selectedAtter.add(option);
                        var availableIdsSameType = attribute.options
                                ?.where(
                                  (element) => element.status == true,
                                )
                                .map(
                                  (e) => e.id,
                                )
                                .toList() ??
                            [];
                        widget.onChose(option, selectedAtter, attribute.order,
                            availableIdsSameType);
                      },
                      child: Container(
                        padding:
                            const EdgeInsets.symmetric(vertical: 5, horizontal: 10),
                        decoration:
                            option.status == false && selectedAtter.isNotEmpty
                                ? BoxDecoration(
                                    color: Colors.grey,
                                    borderRadius: BorderRadius.circular(5),
                                    border: Border.all(
                                      color: Colors.grey,
                                      width: 1.5,
                                    ),
                                  )
                                : BoxDecoration(
                                    color: option.isSelected == true
                                        ? AppColors.PRIMARY_COLOR
                                        : Colors.white,
                                    borderRadius: BorderRadius.circular(5),
                                    border: Border.all(
                                      color: option.isSelected == true
                                          ? AppColors.PRIMARY_COLOR
                                          : Colors.grey,
                                      width: 1.5,
                                    ),
                                  ),
                        child: Text(
                          option.name ?? "",
                          style: TextStyle(
                            color: option.isSelected == true
                                ? Colors.white
                                : Colors.black,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    );
                  }).toList(),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
