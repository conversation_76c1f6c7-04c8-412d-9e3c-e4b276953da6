import 'package:alderishop/constants/constants.dart';
import 'package:flutter/material.dart';

class SortOption {
  final String value;
  final String label;
  final IconData icon;
  final Color iconColor;

  const SortOption({
    required this.value,
    required this.label,
    required this.icon,
    this.iconColor = Colors.black,
  });
}

class TopFilterBar extends StatelessWidget {
  final bool isGrid;
  final VoidCallback onToggleView;
  final Function(String) onSort;
  final VoidCallback onFilter;
  final List<SortOption> sortOptions;

  const TopFilterBar({
    Key? key,
    required this.isGrid,
    required this.onToggleView,
    required this.onSort,
    required this.onFilter,
    required this.sortOptions,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: [
        // View Toggle
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10),
            color: Colors.grey.shade200,
          ),
          child: Row(
            children: [
              IconButton(
                icon: Image.asset(
                  'assets/img/new/1.png',
                  color: !isGrid ? AppColors.PRIMARY_COLOR : Colors.grey,
                  width: 24,
                  height: 24,
                ),
                onPressed: () {
                  if (isGrid) onToggleView();
                },
              ),
              IconButton(
                icon: Image.asset(
                  'assets/img/new/2.png',
                  color: isGrid ? AppColors.PRIMARY_COLOR : Colors.grey,
                  width: 24,
                  height: 24,
                ),
                onPressed: () {
                  if (!isGrid) onToggleView();
                },
              ),
            ],
          ),
        ),

        const SizedBox(width: 10),

        // Sort Dropdown Menu (dynamic)
        PopupMenuButton<String>(
          onSelected: onSort,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
          offset: const Offset(0, 40),
          elevation: 3,
          itemBuilder: (context) => sortOptions.map((option) {
            return PopupMenuItem<String>(
              value: option.value,
              height: 35,
              child: Row(
                children: [
                  Icon(option.icon, color: option.iconColor, size: 18),
                  const SizedBox(width: 8),
                  Text(option.label, style: const TextStyle(fontSize: 10)),
                ],
              ),
            );
          }).toList(),
          child: const Padding(
            padding: EdgeInsets.symmetric(horizontal: 10, vertical: 8),
            child: Row(
              children: [
                Icon(Icons.swap_vert, size: 20, color: Colors.black),
                SizedBox(width: 5),
                Text('الترتيب', style: TextStyle(fontSize: 14)),
                Icon(Icons.arrow_drop_down),
              ],
            ),
          ),
        ),

        const SizedBox(width: 10),

        // Filter Button
        GestureDetector(
          onTap: onFilter,
          child: const Row(
            children: [
              Icon(Icons.filter_alt_outlined, size: 20, color: Colors.black),
              SizedBox(width: 5),
              Text('الفلاتر', style: TextStyle(fontSize: 14)),
            ],
          ),
        ),
      ],
    );
  }
}