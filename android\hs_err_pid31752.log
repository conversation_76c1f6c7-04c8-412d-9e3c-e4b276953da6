#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 23068672 bytes for G1 virtual space
# Possible reasons:
#   The system is out of physical RAM or swap space
#   The process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (os_windows.cpp:3771), pid=31752, tid=12408
#
# JRE version: OpenJDK Runtime Environment (17.0.7) (build 17.0.7+0-b2043.56-10550314)
# Java VM: OpenJDK 64-Bit Server VM (17.0.7+0-b2043.56-10550314, mixed mode, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED -Xmx4G -Dfile.encoding=windows-1252 -Duser.country=US -Duser.language=en -Duser.variant org.gradle.launcher.daemon.bootstrap.GradleDaemon 7.5

Host: Intel(R) Core(TM) i7-10700T CPU @ 2.00GHz, 16 cores, 15G,  Windows 11 , 64 bit Build 26100 (10.0.26100.3775)
Time: Thu Apr 24 16:27:29 2025 Turkey Standard Time elapsed time: 14.218999 seconds (0d 0h 0m 14s)

---------------  T H R E A D  ---------------

Current thread (0x000001d42fabc800):  VMThread "VM Thread" [stack: 0x000000cf94200000,0x000000cf94300000] [id=12408]

Stack: [0x000000cf94200000,0x000000cf94300000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x683bfa]
V  [jvm.dll+0x8430a4]
V  [jvm.dll+0x8449be]
V  [jvm.dll+0x845023]
V  [jvm.dll+0x24ad2f]
V  [jvm.dll+0x680ac9]
V  [jvm.dll+0x67519a]
V  [jvm.dll+0x30b3cb]
V  [jvm.dll+0x312876]
V  [jvm.dll+0x36221e]
V  [jvm.dll+0x36244f]
V  [jvm.dll+0x2e14a8]
V  [jvm.dll+0x2df684]
V  [jvm.dll+0x2dec8c]
V  [jvm.dll+0x32382b]
V  [jvm.dll+0x8496eb]
V  [jvm.dll+0x84a424]
V  [jvm.dll+0x84a93d]
V  [jvm.dll+0x84ad14]
V  [jvm.dll+0x84ade0]
V  [jvm.dll+0x7f2aea]
V  [jvm.dll+0x682a35]
C  [ucrtbase.dll+0x37b0]
C  [KERNEL32.DLL+0x2e8d7]
C  [ntdll.dll+0xb14fc]

VM_Operation (0x000000cf95feb6a0): G1CollectForAllocation, mode: safepoint, requested by thread 0x000001d435ebe020


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000001d4399b9960, length=235, elements={
0x000001d40fb70860, 0x000001d42fae1720, 0x000001d42fae20e0, 0x000001d42fb1da90,
0x000001d42fb20360, 0x000001d42fb20c20, 0x000001d42fb234f0, 0x000001d42fb240e0,
0x000001d42fb2a2f0, 0x000001d42fb32c00, 0x000001d42fc94dd0, 0x000001d42fcc7030,
0x000001d42fcc7500, 0x000001d434f589d0, 0x000001d435d388f0, 0x000001d4341a04e0,
0x000001d435f84f40, 0x000001d4351ee290, 0x000001d435b756e0, 0x000001d4360b8420,
0x000001d435c13040, 0x000001d435ebe020, 0x000001d435ebfa50, 0x000001d436136b30,
0x000001d436136660, 0x000001d436137000, 0x000001d4361374d0, 0x000001d436138ce0,
0x000001d436136190, 0x000001d4361391b0, 0x000001d436137e70, 0x000001d436138340,
0x000001d436138810, 0x000001d436139680, 0x000001d438815eb0, 0x000001d4388129c0,
0x000001d438813360, 0x000001d4388146a0, 0x000001d438816380, 0x000001d438814b70,
0x000001d438813830, 0x000001d438812e90, 0x000001d438818060, 0x000001d438819870,
0x000001d4388171f0, 0x000001d438818530, 0x000001d438819d40, 0x000001d4388176c0,
0x000001d438817b90, 0x000001d438818a00, 0x000001d4388193a0, 0x000001d43b32e220,
0x000001d43b32ca10, 0x000001d43b32c540, 0x000001d43b32d880, 0x000001d43b32f560,
0x000001d43b32cee0, 0x000001d43b32e6f0, 0x000001d43b32d3b0, 0x000001d43b32dd50,
0x000001d43b32f090, 0x000001d43b32ebc0, 0x000001d43b32fa30, 0x000001d43b32c070,
0x000001d43b3333f0, 0x000001d43b332f20, 0x000001d43b332580, 0x000001d43b331240,
0x000001d43b331710, 0x000001d43b330d70, 0x000001d43b331be0, 0x000001d43b32ff00,
0x000001d43b3308a0, 0x000001d43b3303d0, 0x000001d43b3320b0, 0x000001d4381a9ed0,
0x000001d4381ac080, 0x000001d4381aa3a0, 0x000001d4381a9530, 0x000001d4381aca20,
0x000001d4381aad40, 0x000001d4381a9a00, 0x000001d4381ab210, 0x000001d4381ab6e0,
0x000001d4381abbb0, 0x000001d4381ac550, 0x000001d4381aa870, 0x000001d4381a9060,
0x000001d4381ae230, 0x000001d4381ae700, 0x000001d4381b08b0, 0x000001d4381af0a0,
0x000001d4381afa40, 0x000001d4381aebd0, 0x000001d4381ad3c0, 0x000001d4381ad890,
0x000001d4381af570, 0x000001d4381add60, 0x000001d4381acef0, 0x000001d4381aff10,
0x000001d4381b03e0, 0x000001d43b047230, 0x000001d43b043870, 0x000001d43b046890,
0x000001d43b044210, 0x000001d43b044bb0, 0x000001d43b0446e0, 0x000001d43b045080,
0x000001d43b045550, 0x000001d43b0463c0, 0x000001d43b047700, 0x000001d43b046d60,
0x000001d43b043d40, 0x000001d43b045ef0, 0x000001d43b048a40, 0x000001d43b047bd0,
0x000001d43b0480a0, 0x000001d43b0493e0, 0x000001d43b048570, 0x000001d43b048f10,
0x000001d43b045a20, 0x000001d43b04a250, 0x000001d43b0498b0, 0x000001d43b049d80,
0x000001d43b04a720, 0x000001d43b04abf0, 0x000001d43b04b0c0, 0x000001d437396880,
0x000001d437398560, 0x000001d437396d50, 0x000001d437398a30, 0x000001d437397220,
0x000001d4373993d0, 0x000001d437398090, 0x000001d437397bc0, 0x000001d4373976f0,
0x000001d437398f00, 0x000001d4373998a0, 0x000001d437399d70, 0x000001d4373963b0,
0x000001d43739abe0, 0x000001d43739d730, 0x000001d43739a710, 0x000001d43739cd90,
0x000001d43739d260, 0x000001d43739c8c0, 0x000001d43739c3f0, 0x000001d43739dc00,
0x000001d43739a240, 0x000001d43739b580, 0x000001d43739ba50, 0x000001d43739bf20,
0x000001d43739b0b0, 0x000001d4375df440, 0x000001d4375e02b0, 0x000001d4375e0780,
0x000001d4375e0c50, 0x000001d4375df910, 0x000001d4375e1120, 0x000001d4375dd760,
0x000001d4375ddc30, 0x000001d4375e15f0, 0x000001d4375e1ac0, 0x000001d4375de100,
0x000001d4375de5d0, 0x000001d4375e1f90, 0x000001d4375deaa0, 0x000001d4375e2460,
0x000001d4375def70, 0x000001d4375e2930, 0x000001d4375e2e00, 0x000001d4375e37a0,
0x000001d4375e32d0, 0x000001d4375dfde0, 0x000001d4375e3c70, 0x000001d4375e4140,
0x000001d4375e4ae0, 0x000001d4375e4610, 0x000001d4375e4fb0, 0x000001d43a67dec0,
0x000001d43a67c6b0, 0x000001d43a67b840, 0x000001d43a67cb80, 0x000001d43a67d050,
0x000001d43a67e860, 0x000001d43a67c1e0, 0x000001d43a67d520, 0x000001d43a67f6d0,
0x000001d43a67d9f0, 0x000001d43a67e390, 0x000001d43a67bd10, 0x000001d43a6813b0,
0x000001d43a680070, 0x000001d43a67ed30, 0x000001d43a67f200, 0x000001d43a682bc0,
0x000001d43a683090, 0x000001d43a6826f0, 0x000001d43a681880, 0x000001d43a67fba0,
0x000001d43a680ee0, 0x000001d43a680540, 0x000001d43a680a10, 0x000001d43a681d50,
0x000001d43a682220, 0x000001d438854000, 0x000001d438854e70, 0x000001d4388527f0,
0x000001d4388561b0, 0x000001d438855340, 0x000001d438853660, 0x000001d4388549a0,
0x000001d4388544d0, 0x000001d438853190, 0x000001d438856680, 0x000001d438855ce0,
0x000001d438852cc0, 0x000001d438857020, 0x000001d4388574f0, 0x000001d438856b50,
0x000001d438853b30, 0x000001d438858830, 0x000001d4388579c0, 0x000001d4388591d0,
0x000001d4388596a0, 0x000001d438857e90, 0x000001d438859b70, 0x000001d43885a040,
0x000001d438858360, 0x000001d438858d00, 0x000001d435436e70, 0x000001d435435b30,
0x000001d435437340, 0x000001d435435190, 0x000001d437732240
}

Java Threads: ( => current thread )
  0x000001d40fb70860 JavaThread "main" [_thread_blocked, id=17376, stack(0x000000cf93c00000,0x000000cf93d00000)]
  0x000001d42fae1720 JavaThread "Reference Handler" daemon [_thread_blocked, id=9116, stack(0x000000cf94300000,0x000000cf94400000)]
  0x000001d42fae20e0 JavaThread "Finalizer" daemon [_thread_blocked, id=39852, stack(0x000000cf94400000,0x000000cf94500000)]
  0x000001d42fb1da90 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=34016, stack(0x000000cf94500000,0x000000cf94600000)]
  0x000001d42fb20360 JavaThread "Attach Listener" daemon [_thread_blocked, id=10368, stack(0x000000cf94600000,0x000000cf94700000)]
  0x000001d42fb20c20 JavaThread "Service Thread" daemon [_thread_blocked, id=38980, stack(0x000000cf94700000,0x000000cf94800000)]
  0x000001d42fb234f0 JavaThread "Monitor Deflation Thread" daemon [_thread_blocked, id=25444, stack(0x000000cf94800000,0x000000cf94900000)]
  0x000001d42fb240e0 JavaThread "C2 CompilerThread0" daemon [_thread_blocked, id=8708, stack(0x000000cf94900000,0x000000cf94a00000)]
  0x000001d42fb2a2f0 JavaThread "C1 CompilerThread0" daemon [_thread_blocked, id=40188, stack(0x000000cf94a00000,0x000000cf94b00000)]
  0x000001d42fb32c00 JavaThread "Sweeper thread" daemon [_thread_blocked, id=41736, stack(0x000000cf94b00000,0x000000cf94c00000)]
  0x000001d42fc94dd0 JavaThread "C1 CompilerThread1" daemon [_thread_blocked, id=36828, stack(0x000000cf94c00000,0x000000cf94d00000)]
  0x000001d42fcc7030 JavaThread "Common-Cleaner" daemon [_thread_blocked, id=25668, stack(0x000000cf94d00000,0x000000cf94e00000)]
  0x000001d42fcc7500 JavaThread "Notification Thread" daemon [_thread_blocked, id=32684, stack(0x000000cf94e00000,0x000000cf94f00000)]
  0x000001d434f589d0 JavaThread "C1 CompilerThread2" daemon [_thread_blocked, id=36092, stack(0x000000cf95200000,0x000000cf95300000)]
  0x000001d435d388f0 JavaThread "Daemon health stats" [_thread_blocked, id=9984, stack(0x000000cf95900000,0x000000cf95a00000)]
  0x000001d4341a04e0 JavaThread "Incoming local TCP Connector on port 61722" [_thread_in_native, id=32632, stack(0x000000cf95000000,0x000000cf95100000)]
  0x000001d435f84f40 JavaThread "Daemon periodic checks" [_thread_blocked, id=15136, stack(0x000000cf95100000,0x000000cf95200000)]
  0x000001d4351ee290 JavaThread "C2 CompilerThread1" daemon [_thread_blocked, id=26960, stack(0x000000cf95a00000,0x000000cf95b00000)]
  0x000001d435b756e0 JavaThread "Daemon" [_thread_blocked, id=17764, stack(0x000000cf95c00000,0x000000cf95d00000)]
  0x000001d4360b8420 JavaThread "Handler for socket connection from /127.0.0.1:61722 to /127.0.0.1:61723" [_thread_in_native, id=4864, stack(0x000000cf95d00000,0x000000cf95e00000)]
  0x000001d435c13040 JavaThread "Cancel handler" [_thread_blocked, id=41780, stack(0x000000cf95e00000,0x000000cf95f00000)]
  0x000001d435ebe020 JavaThread "Daemon worker" [_thread_blocked, id=37460, stack(0x000000cf95f00000,0x000000cf96000000)]
  0x000001d435ebfa50 JavaThread "Asynchronous log dispatcher for DefaultDaemonConnection: socket connection from /127.0.0.1:61722 to /127.0.0.1:61723" [_thread_blocked, id=15512, stack(0x000000cf96000000,0x000000cf96100000)]
  0x000001d436136b30 JavaThread "Stdin handler" [_thread_blocked, id=11156, stack(0x000000cf96100000,0x000000cf96200000)]
  0x000001d436136660 JavaThread "Daemon client event forwarder" [_thread_blocked, id=10392, stack(0x000000cf96200000,0x000000cf96300000)]
  0x000001d436137000 JavaThread "Cache worker for journal cache (C:\Users\<USER>\.gradle\caches\journal-1)" [_thread_blocked, id=31740, stack(0x000000cf96300000,0x000000cf96400000)]
  0x000001d4361374d0 JavaThread "File lock request listener" [_thread_in_native, id=30768, stack(0x000000cf96400000,0x000000cf96500000)]
  0x000001d436138ce0 JavaThread "Cache worker for file hash cache (C:\Users\<USER>\.gradle\caches\7.5\fileHashes)" [_thread_blocked, id=33520, stack(0x000000cf96e00000,0x000000cf96f00000)]
  0x000001d436136190 JavaThread "File watcher server" daemon [_thread_in_native, id=35140, stack(0x000000cf96f00000,0x000000cf97000000)]
  0x000001d4361391b0 JavaThread "File watcher consumer" daemon [_thread_blocked, id=12760, stack(0x000000cf97000000,0x000000cf97100000)]
  0x000001d436137e70 JavaThread "Cache worker for checksums cache (D:\Ecommerce-App\android\.gradle\7.5\checksums)" [_thread_blocked, id=11184, stack(0x000000cf97100000,0x000000cf97200000)]
  0x000001d436138340 JavaThread "Cache worker for cache directory md-rule (C:\Users\<USER>\.gradle\caches\7.5\md-rule)" [_thread_blocked, id=11364, stack(0x000000cf97200000,0x000000cf97300000)]
  0x000001d436138810 JavaThread "Cache worker for file content cache (C:\Users\<USER>\.gradle\caches\7.5\fileContent)" [_thread_blocked, id=11228, stack(0x000000cf97300000,0x000000cf97400000)]
  0x000001d436139680 JavaThread "Cache worker for file hash cache (D:\Ecommerce-App\android\.gradle\7.5\fileHashes)" [_thread_blocked, id=5432, stack(0x000000cf97400000,0x000000cf97500000)]
  0x000001d438815eb0 JavaThread "Cache worker for cache directory md-supplier (C:\Users\<USER>\.gradle\caches\7.5\md-supplier)" [_thread_blocked, id=31836, stack(0x000000cf97500000,0x000000cf97600000)]
  0x000001d4388129c0 JavaThread "Cache worker for execution history cache (C:\Users\<USER>\.gradle\caches\7.5\executionHistory)" [_thread_blocked, id=27564, stack(0x000000cf97600000,0x000000cf97700000)]
  0x000001d438813360 JavaThread "jar transforms" [_thread_blocked, id=31168, stack(0x000000cf97800000,0x000000cf97900000)]
  0x000001d4388146a0 JavaThread "Cache worker for kotlin-dsl (C:\Users\<USER>\.gradle\caches\7.5\kotlin-dsl)" [_thread_blocked, id=25272, stack(0x000000cf97900000,0x000000cf97a00000)]
  0x000001d438816380 JavaThread "jar transforms Thread 2" [_thread_blocked, id=10656, stack(0x000000cf97a00000,0x000000cf97b00000)]
  0x000001d438814b70 JavaThread "Cache worker for dependencies-accessors (D:\Ecommerce-App\android\.gradle\7.5\dependencies-accessors)" [_thread_blocked, id=9012, stack(0x000000cf97b00000,0x000000cf97c00000)]
  0x000001d438813830 JavaThread "Cache worker for Build Output Cleanup Cache (C:\flutter\packages\flutter_tools\gradle\.gradle\buildOutputCleanup)" [_thread_blocked, id=17832, stack(0x000000cf97700000,0x000000cf97800000)]
  0x000001d438812e90 JavaThread "jar transforms Thread 3" [_thread_blocked, id=26444, stack(0x000000cf97c00000,0x000000cf97d00000)]
  0x000001d438818060 JavaThread "Memory manager" [_thread_blocked, id=6880, stack(0x000000cf97d00000,0x000000cf97e00000)]
  0x000001d438819870 JavaThread "jar transforms Thread 4" [_thread_blocked, id=31920, stack(0x000000cf97e00000,0x000000cf97f00000)]
  0x000001d4388171f0 JavaThread "included builds" [_thread_blocked, id=3436, stack(0x000000cf97f00000,0x000000cf98000000)]
  0x000001d438818530 JavaThread "Execution worker" [_thread_blocked, id=26956, stack(0x000000cf98000000,0x000000cf98100000)]
  0x000001d438819d40 JavaThread "Execution worker Thread 2" [_thread_blocked, id=26900, stack(0x000000cf98100000,0x000000cf98200000)]
  0x000001d4388176c0 JavaThread "Execution worker Thread 3" [_thread_blocked, id=40860, stack(0x000000cf98200000,0x000000cf98300000)]
  0x000001d438817b90 JavaThread "Execution worker Thread 4" [_thread_blocked, id=40228, stack(0x000000cf98300000,0x000000cf98400000)]
  0x000001d438818a00 JavaThread "Execution worker Thread 5" [_thread_blocked, id=36772, stack(0x000000cf98400000,0x000000cf98500000)]
  0x000001d4388193a0 JavaThread "Execution worker Thread 6" [_thread_blocked, id=22116, stack(0x000000cf98500000,0x000000cf98600000)]
  0x000001d43b32e220 JavaThread "Execution worker Thread 7" [_thread_blocked, id=40328, stack(0x000000cf98600000,0x000000cf98700000)]
  0x000001d43b32ca10 JavaThread "Execution worker Thread 8" [_thread_blocked, id=40132, stack(0x000000cf98700000,0x000000cf98800000)]
  0x000001d43b32c540 JavaThread "Execution worker Thread 9" [_thread_blocked, id=5372, stack(0x000000cf98800000,0x000000cf98900000)]
  0x000001d43b32d880 JavaThread "Execution worker Thread 10" [_thread_blocked, id=3348, stack(0x000000cf98900000,0x000000cf98a00000)]
  0x000001d43b32f560 JavaThread "Execution worker Thread 11" [_thread_blocked, id=37872, stack(0x000000cf98a00000,0x000000cf98b00000)]
  0x000001d43b32cee0 JavaThread "Execution worker Thread 12" [_thread_blocked, id=39504, stack(0x000000cf98b00000,0x000000cf98c00000)]
  0x000001d43b32e6f0 JavaThread "Execution worker Thread 13" [_thread_blocked, id=12628, stack(0x000000cf98c00000,0x000000cf98d00000)]
  0x000001d43b32d3b0 JavaThread "Execution worker Thread 14" [_thread_blocked, id=8564, stack(0x000000cf98d00000,0x000000cf98e00000)]
  0x000001d43b32dd50 JavaThread "Execution worker Thread 15" [_thread_blocked, id=30328, stack(0x000000cf98e00000,0x000000cf98f00000)]
  0x000001d43b32f090 JavaThread "Cache worker for execution history cache (C:\flutter\packages\flutter_tools\gradle\.gradle\7.5\executionHistory)" [_thread_blocked, id=8692, stack(0x000000cf98f00000,0x000000cf99000000)]
  0x000001d43b32ebc0 JavaThread "Unconstrained build operations" [_thread_blocked, id=1776, stack(0x000000cf99000000,0x000000cf99100000)]
  0x000001d43b32fa30 JavaThread "Unconstrained build operations Thread 2" [_thread_blocked, id=35644, stack(0x000000cf99100000,0x000000cf99200000)]
  0x000001d43b32c070 JavaThread "Unconstrained build operations Thread 3" [_thread_blocked, id=38968, stack(0x000000cf99200000,0x000000cf99300000)]
  0x000001d43b3333f0 JavaThread "Unconstrained build operations Thread 4" [_thread_blocked, id=29328, stack(0x000000cf99300000,0x000000cf99400000)]
  0x000001d43b332f20 JavaThread "Unconstrained build operations Thread 5" [_thread_blocked, id=30168, stack(0x000000cf99400000,0x000000cf99500000)]
  0x000001d43b332580 JavaThread "Unconstrained build operations Thread 6" [_thread_blocked, id=32288, stack(0x000000cf99500000,0x000000cf99600000)]
  0x000001d43b331240 JavaThread "Unconstrained build operations Thread 7" [_thread_blocked, id=41828, stack(0x000000cf99600000,0x000000cf99700000)]
  0x000001d43b331710 JavaThread "Unconstrained build operations Thread 8" [_thread_blocked, id=12248, stack(0x000000cf99700000,0x000000cf99800000)]
  0x000001d43b330d70 JavaThread "Unconstrained build operations Thread 9" [_thread_blocked, id=28448, stack(0x000000cf99800000,0x000000cf99900000)]
  0x000001d43b331be0 JavaThread "Unconstrained build operations Thread 10" [_thread_blocked, id=18828, stack(0x000000cf99900000,0x000000cf99a00000)]
  0x000001d43b32ff00 JavaThread "Unconstrained build operations Thread 11" [_thread_blocked, id=31044, stack(0x000000cf99a00000,0x000000cf99b00000)]
  0x000001d43b3308a0 JavaThread "Unconstrained build operations Thread 12" [_thread_blocked, id=34584, stack(0x000000cf99b00000,0x000000cf99c00000)]
  0x000001d43b3303d0 JavaThread "Unconstrained build operations Thread 13" [_thread_blocked, id=33636, stack(0x000000cf99c00000,0x000000cf99d00000)]
  0x000001d43b3320b0 JavaThread "Unconstrained build operations Thread 14" [_thread_blocked, id=5540, stack(0x000000cf99d00000,0x000000cf99e00000)]
  0x000001d4381a9ed0 JavaThread "Unconstrained build operations Thread 15" [_thread_blocked, id=22356, stack(0x000000cf99e00000,0x000000cf99f00000)]
  0x000001d4381ac080 JavaThread "Unconstrained build operations Thread 16" [_thread_blocked, id=17612, stack(0x000000cf99f00000,0x000000cf9a000000)]
  0x000001d4381aa3a0 JavaThread "Unconstrained build operations Thread 17" [_thread_blocked, id=26032, stack(0x000000cf9a000000,0x000000cf9a100000)]
  0x000001d4381a9530 JavaThread "Unconstrained build operations Thread 18" [_thread_blocked, id=11132, stack(0x000000cf9a100000,0x000000cf9a200000)]
  0x000001d4381aca20 JavaThread "Unconstrained build operations Thread 19" [_thread_blocked, id=21200, stack(0x000000cf9a200000,0x000000cf9a300000)]
  0x000001d4381aad40 JavaThread "Unconstrained build operations Thread 20" [_thread_blocked, id=18780, stack(0x000000cf9a300000,0x000000cf9a400000)]
  0x000001d4381a9a00 JavaThread "Unconstrained build operations Thread 21" [_thread_blocked, id=18112, stack(0x000000cf9a400000,0x000000cf9a500000)]
  0x000001d4381ab210 JavaThread "Unconstrained build operations Thread 22" [_thread_blocked, id=29284, stack(0x000000cf9a500000,0x000000cf9a600000)]
  0x000001d4381ab6e0 JavaThread "Unconstrained build operations Thread 23" [_thread_blocked, id=29668, stack(0x000000cf9a600000,0x000000cf9a700000)]
  0x000001d4381abbb0 JavaThread "Unconstrained build operations Thread 24" [_thread_blocked, id=20228, stack(0x000000cf9a700000,0x000000cf9a800000)]
  0x000001d4381ac550 JavaThread "Unconstrained build operations Thread 25" [_thread_blocked, id=37476, stack(0x000000cf9a800000,0x000000cf9a900000)]
  0x000001d4381aa870 JavaThread "Unconstrained build operations Thread 26" [_thread_blocked, id=15772, stack(0x000000cf9a900000,0x000000cf9aa00000)]
  0x000001d4381a9060 JavaThread "Unconstrained build operations Thread 27" [_thread_blocked, id=26872, stack(0x000000cf9aa00000,0x000000cf9ab00000)]
  0x000001d4381ae230 JavaThread "Unconstrained build operations Thread 28" [_thread_blocked, id=496, stack(0x000000cf9ab00000,0x000000cf9ac00000)]
  0x000001d4381ae700 JavaThread "Unconstrained build operations Thread 29" [_thread_blocked, id=31052, stack(0x000000cf9ac00000,0x000000cf9ad00000)]
  0x000001d4381b08b0 JavaThread "Unconstrained build operations Thread 30" [_thread_blocked, id=4432, stack(0x000000cf9ad00000,0x000000cf9ae00000)]
  0x000001d4381af0a0 JavaThread "Unconstrained build operations Thread 31" [_thread_blocked, id=4900, stack(0x000000cf9ae00000,0x000000cf9af00000)]
  0x000001d4381afa40 JavaThread "Unconstrained build operations Thread 32" [_thread_blocked, id=22400, stack(0x000000cf9af00000,0x000000cf9b000000)]
  0x000001d4381aebd0 JavaThread "Unconstrained build operations Thread 33" [_thread_blocked, id=5920, stack(0x000000cf9b000000,0x000000cf9b100000)]
  0x000001d4381ad3c0 JavaThread "Unconstrained build operations Thread 34" [_thread_blocked, id=28768, stack(0x000000cf9b100000,0x000000cf9b200000)]
  0x000001d4381ad890 JavaThread "Unconstrained build operations Thread 35" [_thread_blocked, id=16416, stack(0x000000cf9b200000,0x000000cf9b300000)]
  0x000001d4381af570 JavaThread "Unconstrained build operations Thread 36" [_thread_blocked, id=2672, stack(0x000000cf9b300000,0x000000cf9b400000)]
  0x000001d4381add60 JavaThread "Unconstrained build operations Thread 37" [_thread_blocked, id=39540, stack(0x000000cf9b400000,0x000000cf9b500000)]
  0x000001d4381acef0 JavaThread "Unconstrained build operations Thread 38" [_thread_blocked, id=3732, stack(0x000000cf9b500000,0x000000cf9b600000)]
  0x000001d4381aff10 JavaThread "Unconstrained build operations Thread 39" [_thread_blocked, id=18364, stack(0x000000cf9b600000,0x000000cf9b700000)]
  0x000001d4381b03e0 JavaThread "Unconstrained build operations Thread 40" [_thread_blocked, id=25860, stack(0x000000cf9b700000,0x000000cf9b800000)]
  0x000001d43b047230 JavaThread "Unconstrained build operations Thread 41" [_thread_blocked, id=25572, stack(0x000000cf9b800000,0x000000cf9b900000)]
  0x000001d43b043870 JavaThread "Unconstrained build operations Thread 42" [_thread_blocked, id=18876, stack(0x000000cf9b900000,0x000000cf9ba00000)]
  0x000001d43b046890 JavaThread "Unconstrained build operations Thread 43" [_thread_blocked, id=39252, stack(0x000000cf9ba00000,0x000000cf9bb00000)]
  0x000001d43b044210 JavaThread "Unconstrained build operations Thread 44" [_thread_blocked, id=27920, stack(0x000000cf9bb00000,0x000000cf9bc00000)]
  0x000001d43b044bb0 JavaThread "Unconstrained build operations Thread 45" [_thread_blocked, id=39616, stack(0x000000cf9bc00000,0x000000cf9bd00000)]
  0x000001d43b0446e0 JavaThread "Unconstrained build operations Thread 46" [_thread_blocked, id=34720, stack(0x000000cf9bd00000,0x000000cf9be00000)]
  0x000001d43b045080 JavaThread "Unconstrained build operations Thread 47" [_thread_blocked, id=33140, stack(0x000000cf9be00000,0x000000cf9bf00000)]
  0x000001d43b045550 JavaThread "Unconstrained build operations Thread 48" [_thread_blocked, id=23732, stack(0x000000cf9bf00000,0x000000cf9c000000)]
  0x000001d43b0463c0 JavaThread "Unconstrained build operations Thread 49" [_thread_blocked, id=33696, stack(0x000000cf9c000000,0x000000cf9c100000)]
  0x000001d43b047700 JavaThread "Unconstrained build operations Thread 50" [_thread_blocked, id=19468, stack(0x000000cf9c100000,0x000000cf9c200000)]
  0x000001d43b046d60 JavaThread "Unconstrained build operations Thread 51" [_thread_blocked, id=25888, stack(0x000000cf9c200000,0x000000cf9c300000)]
  0x000001d43b043d40 JavaThread "Unconstrained build operations Thread 52" [_thread_blocked, id=38096, stack(0x000000cf9c300000,0x000000cf9c400000)]
  0x000001d43b045ef0 JavaThread "Unconstrained build operations Thread 53" [_thread_blocked, id=29588, stack(0x000000cf9c400000,0x000000cf9c500000)]
  0x000001d43b048a40 JavaThread "Unconstrained build operations Thread 54" [_thread_blocked, id=4020, stack(0x000000cf9c500000,0x000000cf9c600000)]
  0x000001d43b047bd0 JavaThread "Unconstrained build operations Thread 55" [_thread_blocked, id=21896, stack(0x000000cf9c600000,0x000000cf9c700000)]
  0x000001d43b0480a0 JavaThread "Unconstrained build operations Thread 56" [_thread_blocked, id=36764, stack(0x000000cf9c700000,0x000000cf9c800000)]
  0x000001d43b0493e0 JavaThread "Unconstrained build operations Thread 57" [_thread_blocked, id=32260, stack(0x000000cf9c800000,0x000000cf9c900000)]
  0x000001d43b048570 JavaThread "Unconstrained build operations Thread 58" [_thread_blocked, id=31784, stack(0x000000cf9c900000,0x000000cf9ca00000)]
  0x000001d43b048f10 JavaThread "Unconstrained build operations Thread 59" [_thread_blocked, id=31664, stack(0x000000cf9ca00000,0x000000cf9cb00000)]
  0x000001d43b045a20 JavaThread "Unconstrained build operations Thread 60" [_thread_blocked, id=24464, stack(0x000000cf9cb00000,0x000000cf9cc00000)]
  0x000001d43b04a250 JavaThread "Unconstrained build operations Thread 61" [_thread_blocked, id=31540, stack(0x000000cf9cc00000,0x000000cf9cd00000)]
  0x000001d43b0498b0 JavaThread "Unconstrained build operations Thread 62" [_thread_blocked, id=35860, stack(0x000000cf9cd00000,0x000000cf9ce00000)]
  0x000001d43b049d80 JavaThread "Unconstrained build operations Thread 63" [_thread_blocked, id=4880, stack(0x000000cf9ce00000,0x000000cf9cf00000)]
  0x000001d43b04a720 JavaThread "Unconstrained build operations Thread 64" [_thread_blocked, id=18376, stack(0x000000cf9cf00000,0x000000cf9d000000)]
  0x000001d43b04abf0 JavaThread "Unconstrained build operations Thread 65" [_thread_blocked, id=1496, stack(0x000000cf9d000000,0x000000cf9d100000)]
  0x000001d43b04b0c0 JavaThread "Unconstrained build operations Thread 66" [_thread_blocked, id=41308, stack(0x000000cf9d100000,0x000000cf9d200000)]
  0x000001d437396880 JavaThread "Unconstrained build operations Thread 67" [_thread_blocked, id=32044, stack(0x000000cf9d200000,0x000000cf9d300000)]
  0x000001d437398560 JavaThread "Unconstrained build operations Thread 68" [_thread_blocked, id=24128, stack(0x000000cf9d300000,0x000000cf9d400000)]
  0x000001d437396d50 JavaThread "Unconstrained build operations Thread 69" [_thread_blocked, id=31468, stack(0x000000cf9d400000,0x000000cf9d500000)]
  0x000001d437398a30 JavaThread "Unconstrained build operations Thread 70" [_thread_blocked, id=19940, stack(0x000000cf9d500000,0x000000cf9d600000)]
  0x000001d437397220 JavaThread "Unconstrained build operations Thread 71" [_thread_blocked, id=30580, stack(0x000000cf9d600000,0x000000cf9d700000)]
  0x000001d4373993d0 JavaThread "Unconstrained build operations Thread 72" [_thread_blocked, id=41436, stack(0x000000cf9d700000,0x000000cf9d800000)]
  0x000001d437398090 JavaThread "Unconstrained build operations Thread 73" [_thread_blocked, id=26036, stack(0x000000cf9d800000,0x000000cf9d900000)]
  0x000001d437397bc0 JavaThread "Unconstrained build operations Thread 74" [_thread_blocked, id=24432, stack(0x000000cf9d900000,0x000000cf9da00000)]
  0x000001d4373976f0 JavaThread "Unconstrained build operations Thread 75" [_thread_blocked, id=9168, stack(0x000000cf9da00000,0x000000cf9db00000)]
  0x000001d437398f00 JavaThread "Unconstrained build operations Thread 76" [_thread_blocked, id=4708, stack(0x000000cf9db00000,0x000000cf9dc00000)]
  0x000001d4373998a0 JavaThread "Unconstrained build operations Thread 77" [_thread_blocked, id=41232, stack(0x000000cf9dc00000,0x000000cf9dd00000)]
  0x000001d437399d70 JavaThread "Unconstrained build operations Thread 78" [_thread_blocked, id=21536, stack(0x000000cf9dd00000,0x000000cf9de00000)]
  0x000001d4373963b0 JavaThread "Unconstrained build operations Thread 79" [_thread_blocked, id=2748, stack(0x000000cf9de00000,0x000000cf9df00000)]
  0x000001d43739abe0 JavaThread "Unconstrained build operations Thread 80" [_thread_blocked, id=21500, stack(0x000000cf9df00000,0x000000cf9e000000)]
  0x000001d43739d730 JavaThread "Unconstrained build operations Thread 81" [_thread_blocked, id=23156, stack(0x000000cf9e000000,0x000000cf9e100000)]
  0x000001d43739a710 JavaThread "Unconstrained build operations Thread 82" [_thread_blocked, id=4192, stack(0x000000cf9e100000,0x000000cf9e200000)]
  0x000001d43739cd90 JavaThread "Unconstrained build operations Thread 83" [_thread_blocked, id=30376, stack(0x000000cf9e200000,0x000000cf9e300000)]
  0x000001d43739d260 JavaThread "Unconstrained build operations Thread 84" [_thread_blocked, id=13236, stack(0x000000cf9e300000,0x000000cf9e400000)]
  0x000001d43739c8c0 JavaThread "Unconstrained build operations Thread 85" [_thread_blocked, id=41896, stack(0x000000cf9e400000,0x000000cf9e500000)]
  0x000001d43739c3f0 JavaThread "Unconstrained build operations Thread 86" [_thread_blocked, id=6540, stack(0x000000cf9e500000,0x000000cf9e600000)]
  0x000001d43739dc00 JavaThread "Unconstrained build operations Thread 87" [_thread_blocked, id=12044, stack(0x000000cf9e600000,0x000000cf9e700000)]
  0x000001d43739a240 JavaThread "Unconstrained build operations Thread 88" [_thread_blocked, id=31160, stack(0x000000cf9e700000,0x000000cf9e800000)]
  0x000001d43739b580 JavaThread "Unconstrained build operations Thread 89" [_thread_blocked, id=29596, stack(0x000000cf9e800000,0x000000cf9e900000)]
  0x000001d43739ba50 JavaThread "Unconstrained build operations Thread 90" [_thread_blocked, id=32532, stack(0x000000cf9e900000,0x000000cf9ea00000)]
  0x000001d43739bf20 JavaThread "Unconstrained build operations Thread 91" [_thread_blocked, id=2816, stack(0x000000cf9ea00000,0x000000cf9eb00000)]
  0x000001d43739b0b0 JavaThread "Unconstrained build operations Thread 92" [_thread_blocked, id=22060, stack(0x000000cf9eb00000,0x000000cf9ec00000)]
  0x000001d4375df440 JavaThread "Unconstrained build operations Thread 93" [_thread_blocked, id=24200, stack(0x000000cf9ec00000,0x000000cf9ed00000)]
  0x000001d4375e02b0 JavaThread "Unconstrained build operations Thread 94" [_thread_blocked, id=17652, stack(0x000000cf9ed00000,0x000000cf9ee00000)]
  0x000001d4375e0780 JavaThread "Unconstrained build operations Thread 95" [_thread_blocked, id=11236, stack(0x000000cf9ee00000,0x000000cf9ef00000)]
  0x000001d4375e0c50 JavaThread "Unconstrained build operations Thread 96" [_thread_blocked, id=38916, stack(0x000000cf9ef00000,0x000000cf9f000000)]
  0x000001d4375df910 JavaThread "Unconstrained build operations Thread 97" [_thread_blocked, id=33508, stack(0x000000cf9f000000,0x000000cf9f100000)]
  0x000001d4375e1120 JavaThread "Unconstrained build operations Thread 98" [_thread_blocked, id=20632, stack(0x000000cf9f100000,0x000000cf9f200000)]
  0x000001d4375dd760 JavaThread "Unconstrained build operations Thread 99" [_thread_blocked, id=18564, stack(0x000000cf9f200000,0x000000cf9f300000)]
  0x000001d4375ddc30 JavaThread "Unconstrained build operations Thread 100" [_thread_blocked, id=8712, stack(0x000000cf9f300000,0x000000cf9f400000)]
  0x000001d4375e15f0 JavaThread "Unconstrained build operations Thread 101" [_thread_blocked, id=28384, stack(0x000000cf9f400000,0x000000cf9f500000)]
  0x000001d4375e1ac0 JavaThread "Unconstrained build operations Thread 102" [_thread_blocked, id=38080, stack(0x000000cf9f500000,0x000000cf9f600000)]
  0x000001d4375de100 JavaThread "Unconstrained build operations Thread 103" [_thread_blocked, id=33144, stack(0x000000cf9f600000,0x000000cf9f700000)]
  0x000001d4375de5d0 JavaThread "Unconstrained build operations Thread 104" [_thread_blocked, id=34432, stack(0x000000cf9f700000,0x000000cf9f800000)]
  0x000001d4375e1f90 JavaThread "Unconstrained build operations Thread 105" [_thread_blocked, id=20404, stack(0x000000cf9f800000,0x000000cf9f900000)]
  0x000001d4375deaa0 JavaThread "Unconstrained build operations Thread 106" [_thread_blocked, id=26528, stack(0x000000cf9f900000,0x000000cf9fa00000)]
  0x000001d4375e2460 JavaThread "Unconstrained build operations Thread 107" [_thread_blocked, id=29252, stack(0x000000cf9fa00000,0x000000cf9fb00000)]
  0x000001d4375def70 JavaThread "Unconstrained build operations Thread 108" [_thread_blocked, id=31864, stack(0x000000cf9fb00000,0x000000cf9fc00000)]
  0x000001d4375e2930 JavaThread "Unconstrained build operations Thread 109" [_thread_blocked, id=33336, stack(0x000000cf9fc00000,0x000000cf9fd00000)]
  0x000001d4375e2e00 JavaThread "Unconstrained build operations Thread 110" [_thread_blocked, id=35384, stack(0x000000cf9fd00000,0x000000cf9fe00000)]
  0x000001d4375e37a0 JavaThread "Unconstrained build operations Thread 111" [_thread_blocked, id=38992, stack(0x000000cf9fe00000,0x000000cf9ff00000)]
  0x000001d4375e32d0 JavaThread "Unconstrained build operations Thread 112" [_thread_blocked, id=28668, stack(0x000000cf9ff00000,0x000000cfa0000000)]
  0x000001d4375dfde0 JavaThread "Unconstrained build operations Thread 113" [_thread_blocked, id=35444, stack(0x000000cfa0000000,0x000000cfa0100000)]
  0x000001d4375e3c70 JavaThread "Unconstrained build operations Thread 114" [_thread_blocked, id=4488, stack(0x000000cfa0100000,0x000000cfa0200000)]
  0x000001d4375e4140 JavaThread "Unconstrained build operations Thread 115" [_thread_blocked, id=12980, stack(0x000000cfa0200000,0x000000cfa0300000)]
  0x000001d4375e4ae0 JavaThread "Unconstrained build operations Thread 116" [_thread_blocked, id=10936, stack(0x000000cfa0300000,0x000000cfa0400000)]
  0x000001d4375e4610 JavaThread "Unconstrained build operations Thread 117" [_thread_blocked, id=5560, stack(0x000000cfa0400000,0x000000cfa0500000)]
  0x000001d4375e4fb0 JavaThread "Unconstrained build operations Thread 118" [_thread_blocked, id=36824, stack(0x000000cfa0500000,0x000000cfa0600000)]
  0x000001d43a67dec0 JavaThread "Unconstrained build operations Thread 119" [_thread_blocked, id=3340, stack(0x000000cfa0600000,0x000000cfa0700000)]
  0x000001d43a67c6b0 JavaThread "Unconstrained build operations Thread 120" [_thread_blocked, id=6092, stack(0x000000cfa0700000,0x000000cfa0800000)]
  0x000001d43a67b840 JavaThread "Unconstrained build operations Thread 121" [_thread_blocked, id=12372, stack(0x000000cfa0800000,0x000000cfa0900000)]
  0x000001d43a67cb80 JavaThread "Unconstrained build operations Thread 122" [_thread_blocked, id=37112, stack(0x000000cfa0900000,0x000000cfa0a00000)]
  0x000001d43a67d050 JavaThread "Unconstrained build operations Thread 123" [_thread_blocked, id=10164, stack(0x000000cfa0a00000,0x000000cfa0b00000)]
  0x000001d43a67e860 JavaThread "Unconstrained build operations Thread 124" [_thread_blocked, id=30892, stack(0x000000cfa0b00000,0x000000cfa0c00000)]
  0x000001d43a67c1e0 JavaThread "Unconstrained build operations Thread 125" [_thread_blocked, id=23128, stack(0x000000cfa0c00000,0x000000cfa0d00000)]
  0x000001d43a67d520 JavaThread "Unconstrained build operations Thread 126" [_thread_blocked, id=33364, stack(0x000000cfa0d00000,0x000000cfa0e00000)]
  0x000001d43a67f6d0 JavaThread "Unconstrained build operations Thread 127" [_thread_blocked, id=41964, stack(0x000000cfa0e00000,0x000000cfa0f00000)]
  0x000001d43a67d9f0 JavaThread "Unconstrained build operations Thread 128" [_thread_blocked, id=32392, stack(0x000000cfa0f00000,0x000000cfa1000000)]
  0x000001d43a67e390 JavaThread "Unconstrained build operations Thread 129" [_thread_blocked, id=21512, stack(0x000000cfa1000000,0x000000cfa1100000)]
  0x000001d43a67bd10 JavaThread "Unconstrained build operations Thread 130" [_thread_blocked, id=37648, stack(0x000000cfa1100000,0x000000cfa1200000)]
  0x000001d43a6813b0 JavaThread "Unconstrained build operations Thread 131" [_thread_blocked, id=12404, stack(0x000000cfa1200000,0x000000cfa1300000)]
  0x000001d43a680070 JavaThread "Unconstrained build operations Thread 132" [_thread_blocked, id=41344, stack(0x000000cfa1300000,0x000000cfa1400000)]
  0x000001d43a67ed30 JavaThread "Unconstrained build operations Thread 133" [_thread_blocked, id=13260, stack(0x000000cfa1400000,0x000000cfa1500000)]
  0x000001d43a67f200 JavaThread "jar transforms Thread 5" [_thread_blocked, id=41120, stack(0x000000cfa1500000,0x000000cfa1600000)]
  0x000001d43a682bc0 JavaThread "jar transforms Thread 6" [_thread_blocked, id=1036, stack(0x000000cfa1600000,0x000000cfa1700000)]
  0x000001d43a683090 JavaThread "jar transforms Thread 7" [_thread_blocked, id=41248, stack(0x000000cfa1700000,0x000000cfa1800000)]
  0x000001d43a6826f0 JavaThread "jar transforms Thread 8" [_thread_blocked, id=7712, stack(0x000000cfa1800000,0x000000cfa1900000)]
  0x000001d43a681880 JavaThread "jar transforms Thread 9" [_thread_blocked, id=33564, stack(0x000000cfa1900000,0x000000cfa1a00000)]
  0x000001d43a67fba0 JavaThread "jar transforms Thread 10" [_thread_blocked, id=27972, stack(0x000000cfa1a00000,0x000000cfa1b00000)]
  0x000001d43a680ee0 JavaThread "jar transforms Thread 11" [_thread_blocked, id=21432, stack(0x000000cfa1b00000,0x000000cfa1c00000)]
  0x000001d43a680540 JavaThread "jar transforms Thread 12" [_thread_blocked, id=1352, stack(0x000000cfa1c00000,0x000000cfa1d00000)]
  0x000001d43a680a10 JavaThread "jar transforms Thread 13" [_thread_blocked, id=39396, stack(0x000000cfa1d00000,0x000000cfa1e00000)]
  0x000001d43a681d50 JavaThread "jar transforms Thread 14" [_thread_blocked, id=25740, stack(0x000000cfa1e00000,0x000000cfa1f00000)]
  0x000001d43a682220 JavaThread "jar transforms Thread 15" [_thread_blocked, id=9840, stack(0x000000cfa1f00000,0x000000cfa2000000)]
  0x000001d438854000 JavaThread "jar transforms Thread 16" [_thread_blocked, id=37584, stack(0x000000cfa2000000,0x000000cfa2100000)]
  0x000001d438854e70 JavaThread "Cache worker for Build Output Cleanup Cache (D:\Ecommerce-App\android\.gradle\buildOutputCleanup)" [_thread_blocked, id=40152, stack(0x000000cf95b00000,0x000000cf95c00000)]
  0x000001d4388527f0 JavaThread "Unconstrained build operations Thread 134" [_thread_blocked, id=41744, stack(0x000000cfa2100000,0x000000cfa2200000)]
  0x000001d4388561b0 JavaThread "Unconstrained build operations Thread 135" [_thread_blocked, id=22848, stack(0x000000cfa2200000,0x000000cfa2300000)]
  0x000001d438855340 JavaThread "Unconstrained build operations Thread 136" [_thread_blocked, id=19252, stack(0x000000cfa2300000,0x000000cfa2400000)]
  0x000001d438853660 JavaThread "Unconstrained build operations Thread 137" [_thread_blocked, id=5456, stack(0x000000cfa2400000,0x000000cfa2500000)]
  0x000001d4388549a0 JavaThread "Unconstrained build operations Thread 138" [_thread_blocked, id=36876, stack(0x000000cfa2500000,0x000000cfa2600000)]
  0x000001d4388544d0 JavaThread "Unconstrained build operations Thread 139" [_thread_blocked, id=35628, stack(0x000000cfa2600000,0x000000cfa2700000)]
  0x000001d438853190 JavaThread "Unconstrained build operations Thread 140" [_thread_blocked, id=21480, stack(0x000000cfa2700000,0x000000cfa2800000)]
  0x000001d438856680 JavaThread "Unconstrained build operations Thread 141" [_thread_blocked, id=20908, stack(0x000000cfa2800000,0x000000cfa2900000)]
  0x000001d438855ce0 JavaThread "Unconstrained build operations Thread 142" [_thread_blocked, id=33660, stack(0x000000cfa2900000,0x000000cfa2a00000)]
  0x000001d438852cc0 JavaThread "Unconstrained build operations Thread 143" [_thread_blocked, id=8940, stack(0x000000cfa2a00000,0x000000cfa2b00000)]
  0x000001d438857020 JavaThread "Unconstrained build operations Thread 144" [_thread_blocked, id=1920, stack(0x000000cfa2b00000,0x000000cfa2c00000)]
  0x000001d4388574f0 JavaThread "Unconstrained build operations Thread 145" [_thread_blocked, id=29212, stack(0x000000cfa2c00000,0x000000cfa2d00000)]
  0x000001d438856b50 JavaThread "Unconstrained build operations Thread 146" [_thread_blocked, id=24004, stack(0x000000cfa2d00000,0x000000cfa2e00000)]
  0x000001d438853b30 JavaThread "Unconstrained build operations Thread 147" [_thread_blocked, id=23356, stack(0x000000cfa2e00000,0x000000cfa2f00000)]
  0x000001d438858830 JavaThread "Unconstrained build operations Thread 148" [_thread_blocked, id=22252, stack(0x000000cfa2f00000,0x000000cfa3000000)]
  0x000001d4388579c0 JavaThread "Unconstrained build operations Thread 149" [_thread_blocked, id=41848, stack(0x000000cfa3000000,0x000000cfa3100000)]
  0x000001d4388591d0 JavaThread "Unconstrained build operations Thread 150" [_thread_blocked, id=28544, stack(0x000000cfa3100000,0x000000cfa3200000)]
  0x000001d4388596a0 JavaThread "Unconstrained build operations Thread 151" [_thread_blocked, id=31528, stack(0x000000cfa3200000,0x000000cfa3300000)]
  0x000001d438857e90 JavaThread "Unconstrained build operations Thread 152" [_thread_blocked, id=24244, stack(0x000000cfa3300000,0x000000cfa3400000)]
  0x000001d438859b70 JavaThread "Unconstrained build operations Thread 153" [_thread_blocked, id=32328, stack(0x000000cfa3400000,0x000000cfa3500000)]
  0x000001d43885a040 JavaThread "Unconstrained build operations Thread 154" [_thread_blocked, id=13008, stack(0x000000cfa3500000,0x000000cfa3600000)]
  0x000001d438858360 JavaThread "Unconstrained build operations Thread 155" [_thread_blocked, id=2080, stack(0x000000cfa3600000,0x000000cfa3700000)]
  0x000001d438858d00 JavaThread "Unconstrained build operations Thread 156" [_thread_blocked, id=37676, stack(0x000000cfa3700000,0x000000cfa3800000)]
  0x000001d435436e70 JavaThread "Unconstrained build operations Thread 157" [_thread_blocked, id=28900, stack(0x000000cfa3a00000,0x000000cfa3b00000)]
  0x000001d435435b30 JavaThread "Unconstrained build operations Thread 158" [_thread_blocked, id=15708, stack(0x000000cfa3b00000,0x000000cfa3c00000)]
  0x000001d435437340 JavaThread "Unconstrained build operations Thread 159" [_thread_blocked, id=18748, stack(0x000000cfa3c00000,0x000000cfa3d00000)]
  0x000001d435435190 JavaThread "Unconstrained build operations Thread 160" [_thread_blocked, id=4492, stack(0x000000cfa3d00000,0x000000cfa3e00000)]
  0x000001d437732240 JavaThread "C2 CompilerThread2" daemon [_thread_blocked, id=25060, stack(0x000000cfa3e00000,0x000000cfa3f00000)]

Other Threads:
=>0x000001d42fabc800 VMThread "VM Thread" [stack: 0x000000cf94200000,0x000000cf94300000] [id=12408]
  0x000001d40fc0fcf0 WatcherThread [stack: 0x000000cf94f00000,0x000000cf95000000] [id=6360]
  0x000001d40fbe2ca0 GCTaskThread "GC Thread#0" [stack: 0x000000cf93d00000,0x000000cf93e00000] [id=13112]
  0x000001d434aac190 GCTaskThread "GC Thread#1" [stack: 0x000000cf95300000,0x000000cf95400000] [id=31904]
  0x000001d434aac440 GCTaskThread "GC Thread#2" [stack: 0x000000cf95400000,0x000000cf95500000] [id=31584]
  0x000001d43523d6f0 GCTaskThread "GC Thread#3" [stack: 0x000000cf95500000,0x000000cf95600000] [id=29368]
  0x000001d43523dda0 GCTaskThread "GC Thread#4" [stack: 0x000000cf95600000,0x000000cf95700000] [id=36284]
  0x000001d435500ae0 GCTaskThread "GC Thread#5" [stack: 0x000000cf95700000,0x000000cf95800000] [id=26512]
  0x000001d434fb7f10 GCTaskThread "GC Thread#6" [stack: 0x000000cf96500000,0x000000cf96600000] [id=508]
  0x000001d434fb81c0 GCTaskThread "GC Thread#7" [stack: 0x000000cf96600000,0x000000cf96700000] [id=22684]
  0x000001d436080de0 GCTaskThread "GC Thread#8" [stack: 0x000000cf96700000,0x000000cf96800000] [id=25364]
  0x000001d43653da00 GCTaskThread "GC Thread#9" [stack: 0x000000cf96800000,0x000000cf96900000] [id=6252]
  0x000001d43653d4a0 GCTaskThread "GC Thread#10" [stack: 0x000000cf96900000,0x000000cf96a00000] [id=25208]
  0x000001d43653dcb0 GCTaskThread "GC Thread#11" [stack: 0x000000cf96a00000,0x000000cf96b00000] [id=41164]
  0x000001d43653ea20 GCTaskThread "GC Thread#12" [stack: 0x000000cf96b00000,0x000000cf96c00000] [id=34488]
  0x000001d40fbf47c0 ConcurrentGCThread "G1 Main Marker" [stack: 0x000000cf93e00000,0x000000cf93f00000] [id=3632]
  0x000001d40fbf5880 ConcurrentGCThread "G1 Conc#0" [stack: 0x000000cf93f00000,0x000000cf94000000] [id=37980]
  0x000001d43653ecd0 ConcurrentGCThread "G1 Conc#1" [stack: 0x000000cf96c00000,0x000000cf96d00000] [id=24320]
  0x000001d43653e4c0 ConcurrentGCThread "G1 Conc#2" [stack: 0x000000cf96d00000,0x000000cf96e00000] [id=34080]
  0x000001d42f7d5ba0 ConcurrentGCThread "G1 Refine#0" [stack: 0x000000cf94000000,0x000000cf94100000] [id=41796]
  0x000001d42f7d7380 ConcurrentGCThread "G1 Service" [stack: 0x000000cf94100000,0x000000cf94200000] [id=23860]

Threads with active compile tasks:

VM state: at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x000001d40e2093b0] Threads_lock - owner thread: 0x000001d42fabc800
[0x000001d40fb6e460] Heap_lock - owner thread: 0x000001d435ebe020

Heap address: 0x0000000700000000, size: 4096 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) not mapped
Compressed class space mapped at: 0x0000000800000000-0x0000000840000000, reserved size: 1073741824
Narrow klass base: 0x0000000800000000, Narrow klass shift: 0, Narrow klass range: 0x40000000

GC Precious Log:
 CPUs: 16 total, 16 available
 Memory: 16195M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (Zero based)
 Heap Region Size: 2M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 254M
 Heap Max Capacity: 4G
 Pre-touch: Disabled
 Parallel Workers: 13
 Concurrent Workers: 3
 Concurrent Refinement Workers: 13
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 106496K, used 63352K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 1 young (2048K), 1 survivors (2048K)
 Metaspace       used 68441K, committed 69056K, reserved 1114112K
  class space    used 9332K, committed 9600K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, OA=open archive, CA=closed archive, TAMS=top-at-mark-start (previous, next)
|   0|0x0000000700000000, 0x0000000700200000, 0x0000000700200000|100%| O|  |TAMS 0x0000000700200000, 0x0000000700200000| Untracked 
|   1|0x0000000700200000, 0x0000000700400000, 0x0000000700400000|100%| O|  |TAMS 0x0000000700400000, 0x0000000700400000| Untracked 
|   2|0x0000000700400000, 0x0000000700600000, 0x0000000700600000|100%|HS|  |TAMS 0x0000000700600000, 0x0000000700600000| Complete 
|   3|0x0000000700600000, 0x0000000700800000, 0x0000000700800000|100%|HC|  |TAMS 0x0000000700800000, 0x0000000700800000| Complete 
|   4|0x0000000700800000, 0x0000000700a00000, 0x0000000700a00000|100%| O|  |TAMS 0x0000000700a00000, 0x0000000700a00000| Untracked 
|   5|0x0000000700a00000, 0x0000000700c00000, 0x0000000700c00000|100%| O|  |TAMS 0x0000000700c00000, 0x0000000700c00000| Untracked 
|   6|0x0000000700c00000, 0x0000000700e00000, 0x0000000700e00000|100%| O|  |TAMS 0x0000000700e00000, 0x0000000700e00000| Untracked 
|   7|0x0000000700e00000, 0x0000000701000000, 0x0000000701000000|100%| O|  |TAMS 0x0000000701000000, 0x0000000701000000| Untracked 
|   8|0x0000000701000000, 0x0000000701200000, 0x0000000701200000|100%| O|  |TAMS 0x0000000701200000, 0x0000000701200000| Untracked 
|   9|0x0000000701200000, 0x0000000701400000, 0x0000000701400000|100%| O|  |TAMS 0x0000000701400000, 0x0000000701400000| Untracked 
|  10|0x0000000701400000, 0x0000000701600000, 0x0000000701600000|100%| O|  |TAMS 0x0000000701600000, 0x0000000701600000| Untracked 
|  11|0x0000000701600000, 0x0000000701800000, 0x0000000701800000|100%| O|  |TAMS 0x0000000701800000, 0x0000000701800000| Untracked 
|  12|0x0000000701800000, 0x0000000701a00000, 0x0000000701a00000|100%| O|  |TAMS 0x0000000701a00000, 0x0000000701a00000| Untracked 
|  13|0x0000000701a00000, 0x0000000701c00000, 0x0000000701c00000|100%| O|  |TAMS 0x0000000701c00000, 0x0000000701c00000| Untracked 
|  14|0x0000000701c00000, 0x0000000701e00000, 0x0000000701e00000|100%| O|  |TAMS 0x0000000701e00000, 0x0000000701e00000| Untracked 
|  15|0x0000000701e00000, 0x0000000702000000, 0x0000000702000000|100%| O|  |TAMS 0x0000000702000000, 0x0000000702000000| Untracked 
|  16|0x0000000702000000, 0x0000000702200000, 0x0000000702200000|100%| O|  |TAMS 0x0000000702200000, 0x0000000702200000| Untracked 
|  17|0x0000000702200000, 0x0000000702400000, 0x0000000702400000|100%| O|  |TAMS 0x0000000702400000, 0x0000000702400000| Untracked 
|  18|0x0000000702400000, 0x0000000702600000, 0x0000000702600000|100%| O|  |TAMS 0x0000000702600000, 0x0000000702600000| Untracked 
|  19|0x0000000702600000, 0x0000000702800000, 0x0000000702800000|100%| O|  |TAMS 0x0000000702800000, 0x0000000702800000| Untracked 
|  20|0x0000000702800000, 0x0000000702a00000, 0x0000000702a00000|100%| O|  |TAMS 0x0000000702a00000, 0x0000000702a00000| Untracked 
|  21|0x0000000702a00000, 0x0000000702c00000, 0x0000000702c00000|100%| O|  |TAMS 0x0000000702c00000, 0x0000000702c00000| Untracked 
|  22|0x0000000702c00000, 0x0000000702e00000, 0x0000000702e00000|100%| O|  |TAMS 0x0000000702e00000, 0x0000000702e00000| Untracked 
|  23|0x0000000702e00000, 0x0000000703000000, 0x0000000703000000|100%|HS|  |TAMS 0x0000000703000000, 0x0000000703000000| Complete 
|  24|0x0000000703000000, 0x0000000703200000, 0x0000000703200000|100%| O|  |TAMS 0x0000000703200000, 0x0000000703200000| Untracked 
|  25|0x0000000703200000, 0x0000000703400000, 0x0000000703400000|100%| O|  |TAMS 0x00000007032ccc00, 0x0000000703400000| Untracked 
|  26|0x0000000703400000, 0x0000000703600000, 0x0000000703600000|100%| O|  |TAMS 0x0000000703400000, 0x0000000703600000| Untracked 
|  27|0x0000000703600000, 0x0000000703800000, 0x0000000703800000|100%|HS|  |TAMS 0x0000000703600000, 0x0000000703800000| Complete 
|  28|0x0000000703800000, 0x0000000703a00000, 0x0000000703a00000|100%|HC|  |TAMS 0x0000000703800000, 0x0000000703a00000| Complete 
|  29|0x0000000703a00000, 0x0000000703c00000, 0x0000000703c00000|100%| O|  |TAMS 0x0000000703a00000, 0x0000000703c00000| Untracked 
|  30|0x0000000703c00000, 0x0000000703c13000, 0x0000000703e00000|  3%| O|  |TAMS 0x0000000703c00000, 0x0000000703c13000| Untracked 
|  31|0x0000000703e00000, 0x0000000703e00000, 0x0000000704000000|  0%| F|  |TAMS 0x0000000703e00000, 0x0000000703e00000| Untracked 
|  32|0x0000000704000000, 0x0000000704000000, 0x0000000704200000|  0%| F|  |TAMS 0x0000000704000000, 0x0000000704000000| Untracked 
|  33|0x0000000704200000, 0x0000000704200000, 0x0000000704400000|  0%| F|  |TAMS 0x0000000704200000, 0x0000000704200000| Untracked 
|  34|0x0000000704400000, 0x0000000704400000, 0x0000000704600000|  0%| F|  |TAMS 0x0000000704400000, 0x0000000704400000| Untracked 
|  35|0x0000000704600000, 0x0000000704600000, 0x0000000704800000|  0%| F|  |TAMS 0x0000000704600000, 0x0000000704600000| Untracked 
|  36|0x0000000704800000, 0x0000000704800000, 0x0000000704a00000|  0%| F|  |TAMS 0x0000000704800000, 0x0000000704800000| Untracked 
|  37|0x0000000704a00000, 0x0000000704a00000, 0x0000000704c00000|  0%| F|  |TAMS 0x0000000704a00000, 0x0000000704a00000| Untracked 
|  38|0x0000000704c00000, 0x0000000704c00000, 0x0000000704e00000|  0%| F|  |TAMS 0x0000000704c00000, 0x0000000704c00000| Untracked 
|  39|0x0000000704e00000, 0x0000000704e00000, 0x0000000705000000|  0%| F|  |TAMS 0x0000000704e00000, 0x0000000704e00000| Untracked 
|  40|0x0000000705000000, 0x0000000705000000, 0x0000000705200000|  0%| F|  |TAMS 0x0000000705000000, 0x0000000705000000| Untracked 
|  41|0x0000000705200000, 0x0000000705200000, 0x0000000705400000|  0%| F|  |TAMS 0x0000000705200000, 0x0000000705200000| Untracked 
|  42|0x0000000705400000, 0x00000007055cb1c0, 0x0000000705600000| 89%| S|CS|TAMS 0x0000000705400000, 0x0000000705400000| Complete 
|  43|0x0000000705600000, 0x0000000705600000, 0x0000000705800000|  0%| F|  |TAMS 0x0000000705600000, 0x0000000705600000| Untracked 
|  44|0x0000000705800000, 0x0000000705800000, 0x0000000705a00000|  0%| F|  |TAMS 0x0000000705800000, 0x0000000705800000| Untracked 
|  45|0x0000000705a00000, 0x0000000705a00000, 0x0000000705c00000|  0%| F|  |TAMS 0x0000000705a00000, 0x0000000705a00000| Untracked 
|  46|0x0000000705c00000, 0x0000000705c00000, 0x0000000705e00000|  0%| F|  |TAMS 0x0000000705c00000, 0x0000000705c00000| Untracked 
|  97|0x000000070c200000, 0x000000070c200000, 0x000000070c400000|  0%| F|  |TAMS 0x000000070c200000, 0x000000070c200000| Untracked 
|  98|0x000000070c400000, 0x000000070c400000, 0x000000070c600000|  0%| F|  |TAMS 0x000000070c400000, 0x000000070c400000| Untracked 
|  99|0x000000070c600000, 0x000000070c600000, 0x000000070c800000|  0%| F|  |TAMS 0x000000070c600000, 0x000000070c600000| Untracked 
| 100|0x000000070c800000, 0x000000070c800000, 0x000000070ca00000|  0%| F|  |TAMS 0x000000070c800000, 0x000000070c800000| Untracked 
| 126|0x000000070fc00000, 0x000000070fc00000, 0x000000070fe00000|  0%| F|  |TAMS 0x000000070fc00000, 0x000000070fc00000| Untracked 

Card table byte_map: [0x000001d423880000,0x000001d424080000] _byte_map_base: 0x000001d420080000

Marking Bits (Prev, Next): (CMBitMap*) 0x000001d40fbe3200, (CMBitMap*) 0x000001d40fbe31c0
 Prev Bits: [0x000001d428880000, 0x000001d42c880000)
 Next Bits: [0x000001d424880000, 0x000001d428880000)

Polling page: 0x000001d40e0f0000

Metaspace:

Usage:
  Non-class:     57.72 MB used.
      Class:      9.11 MB used.
       Both:     66.84 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,      58.06 MB ( 91%) committed,  1 nodes.
      Class space:        1.00 GB reserved,       9.38 MB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,      67.44 MB (  6%) committed. 

Chunk freelists:
   Non-Class:  5.53 MB
       Class:  6.66 MB
        Both:  12.19 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 106.69 MB
CDS: off
MetaspaceReclaimPolicy: balanced
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - new_chunks_are_fully_committed: 0.
 - uncommit_free_chunks: 1.
 - use_allocation_guard: 0.
 - handle_deallocations: 1.


Internal statistics:

num_allocs_failed_limit: 9.
num_arena_births: 730.
num_arena_deaths: 0.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 1079.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 9.
num_chunks_taken_from_freelist: 3094.
num_chunk_merges: 6.
num_chunk_splits: 2044.
num_chunks_enlarged: 1375.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=119168Kb used=4300Kb max_used=4300Kb free=114867Kb
 bounds [0x000001d41ac60000, 0x000001d41b0a0000, 0x000001d4220c0000]
CodeHeap 'profiled nmethods': size=119104Kb used=14611Kb max_used=14611Kb free=104492Kb
 bounds [0x000001d4130c0000, 0x000001d413f10000, 0x000001d41a510000]
CodeHeap 'non-nmethods': size=7488Kb used=3483Kb max_used=4121Kb free=4004Kb
 bounds [0x000001d41a510000, 0x000001d41a930000, 0x000001d41ac60000]
 total_blobs=8206 nmethods=7325 adapters=789
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 12.118 Thread 0x000001d42fb2a2f0 nmethod 8063 0x000001d413f00490 code [0x000001d413f00640, 0x000001d413f009c8]
Event: 12.118 Thread 0x000001d42fc94dd0 nmethod 8061 0x000001d413f00b10 code [0x000001d413f00ce0, 0x000001d413f01128]
Event: 12.118 Thread 0x000001d434f589d0 nmethod 8062 0x000001d413f01290 code [0x000001d413f01460, 0x000001d413f01848]
Event: 12.119 Thread 0x000001d434f589d0 8065       3       java.security.ProtectionDomain::<init> (98 bytes)
Event: 12.120 Thread 0x000001d434f589d0 nmethod 8065 0x000001d413f01990 code [0x000001d413f01ba0, 0x000001d413f026a8]
Event: 12.123 Thread 0x000001d434f589d0 8066       1       java.util.zip.ZipFile$ZipFileInputStream::size (5 bytes)
Event: 12.123 Thread 0x000001d434f589d0 nmethod 8066 0x000001d41b081690 code [0x000001d41b081820, 0x000001d41b0818f8]
Event: 12.124 Thread 0x000001d4351ee290 nmethod 8058 0x000001d41b081990 code [0x000001d41b081b40, 0x000001d41b082328]
Event: 12.124 Thread 0x000001d4351ee290 8064       4       jdk.internal.reflect.AccessorGenerator::sub (5 bytes)
Event: 12.124 Thread 0x000001d4351ee290 nmethod 8064 0x000001d41b082710 code [0x000001d41b082880, 0x000001d41b0828d8]
Event: 12.132 Thread 0x000001d434f589d0 8067       3       java.lang.StringBuilder::append (8 bytes)
Event: 12.133 Thread 0x000001d434f589d0 nmethod 8067 0x000001d413f02910 code [0x000001d413f02aa0, 0x000001d413f02be8]
Event: 12.138 Thread 0x000001d434f589d0 8068       3       java.lang.String::substring (58 bytes)
Event: 12.139 Thread 0x000001d434f589d0 nmethod 8068 0x000001d413f02c90 code [0x000001d413f02e80, 0x000001d413f03328]
Event: 12.141 Thread 0x000001d434f589d0 8069       3       java.util.ImmutableCollections$ListItr::hasNext (17 bytes)
Event: 12.141 Thread 0x000001d434f589d0 nmethod 8069 0x000001d413f03510 code [0x000001d413f036a0, 0x000001d413f03818]
Event: 12.154 Thread 0x000001d42fb240e0 nmethod 8056 0x000001d41b082a10 code [0x000001d41b082c00, 0x000001d41b0843a8]
Event: 12.173 Thread 0x000001d434f589d0 8070   !   3       jdk.internal.loader.BuiltinClassLoader$2::run (114 bytes)
Event: 12.174 Thread 0x000001d434f589d0 nmethod 8070 0x000001d413f03890 code [0x000001d413f03b20, 0x000001d413f04978]
Event: 12.190 Thread 0x000001d437732240 nmethod 7989 0x000001d41b084f90 code [0x000001d41b0861a0, 0x000001d41b08eb30]

GC Heap History (20 events):
Event: 7.001 GC heap after
{Heap after GC invocations=15 (full 0):
 garbage-first heap   total 88064K, used 34146K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 2 young (4096K), 2 survivors (4096K)
 Metaspace       used 51153K, committed 51648K, reserved 1114112K
  class space    used 6835K, committed 7040K, reserved 1048576K
}
Event: 7.650 GC heap before
{Heap before GC invocations=15 (full 0):
 garbage-first heap   total 88064K, used 66914K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 18 young (36864K), 2 survivors (4096K)
 Metaspace       used 54868K, committed 55360K, reserved 1114112K
  class space    used 7377K, committed 7552K, reserved 1048576K
}
Event: 7.654 GC heap after
{Heap after GC invocations=16 (full 0):
 garbage-first heap   total 88064K, used 35762K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 3 young (6144K), 3 survivors (6144K)
 Metaspace       used 54868K, committed 55360K, reserved 1114112K
  class space    used 7377K, committed 7552K, reserved 1048576K
}
Event: 8.230 GC heap before
{Heap before GC invocations=16 (full 0):
 garbage-first heap   total 88064K, used 64434K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 18 young (36864K), 3 survivors (6144K)
 Metaspace       used 58442K, committed 59008K, reserved 1114112K
  class space    used 7862K, committed 8128K, reserved 1048576K
}
Event: 8.235 GC heap after
{Heap after GC invocations=17 (full 0):
 garbage-first heap   total 88064K, used 38134K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 3 young (6144K), 3 survivors (6144K)
 Metaspace       used 58442K, committed 59008K, reserved 1114112K
  class space    used 7862K, committed 8128K, reserved 1048576K
}
Event: 8.473 GC heap before
{Heap before GC invocations=17 (full 0):
 garbage-first heap   total 88064K, used 50422K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 10 young (20480K), 3 survivors (6144K)
 Metaspace       used 60240K, committed 60736K, reserved 1114112K
  class space    used 8136K, committed 8384K, reserved 1048576K
}
Event: 8.477 GC heap after
{Heap after GC invocations=18 (full 0):
 garbage-first heap   total 88064K, used 39167K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 2 young (4096K), 2 survivors (4096K)
 Metaspace       used 60240K, committed 60736K, reserved 1114112K
  class space    used 8136K, committed 8384K, reserved 1048576K
}
Event: 9.110 GC heap before
{Heap before GC invocations=19 (full 0):
 garbage-first heap   total 88064K, used 69887K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 17 young (34816K), 2 survivors (4096K)
 Metaspace       used 63051K, committed 63552K, reserved 1114112K
  class space    used 8641K, committed 8896K, reserved 1048576K
}
Event: 9.115 GC heap after
{Heap after GC invocations=20 (full 0):
 garbage-first heap   total 88064K, used 41806K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 3 young (6144K), 3 survivors (6144K)
 Metaspace       used 63051K, committed 63552K, reserved 1114112K
  class space    used 8641K, committed 8896K, reserved 1048576K
}
Event: 9.750 GC heap before
{Heap before GC invocations=20 (full 0):
 garbage-first heap   total 88064K, used 68430K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 16 young (32768K), 3 survivors (6144K)
 Metaspace       used 64007K, committed 64512K, reserved 1114112K
  class space    used 8746K, committed 9024K, reserved 1048576K
}
Event: 9.756 GC heap after
{Heap after GC invocations=21 (full 0):
 garbage-first heap   total 88064K, used 44061K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 2 young (4096K), 2 survivors (4096K)
 Metaspace       used 64007K, committed 64512K, reserved 1114112K
  class space    used 8746K, committed 9024K, reserved 1048576K
}
Event: 9.939 GC heap before
{Heap before GC invocations=21 (full 0):
 garbage-first heap   total 88064K, used 50205K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 7 young (14336K), 2 survivors (4096K)
 Metaspace       used 64026K, committed 64576K, reserved 1114112K
  class space    used 8747K, committed 9024K, reserved 1048576K
}
Event: 9.941 GC heap after
{Heap after GC invocations=22 (full 0):
 garbage-first heap   total 88064K, used 50612K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 2 young (4096K), 2 survivors (4096K)
 Metaspace       used 64026K, committed 64576K, reserved 1114112K
  class space    used 8747K, committed 9024K, reserved 1048576K
}
Event: 10.322 GC heap before
{Heap before GC invocations=23 (full 0):
 garbage-first heap   total 92160K, used 66996K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 9 young (18432K), 2 survivors (4096K)
 Metaspace       used 64640K, committed 65216K, reserved 1114112K
  class space    used 8828K, committed 9088K, reserved 1048576K
}
Event: 10.325 GC heap after
{Heap after GC invocations=24 (full 0):
 garbage-first heap   total 92160K, used 54939K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 2 young (4096K), 2 survivors (4096K)
 Metaspace       used 64640K, committed 65216K, reserved 1114112K
  class space    used 8828K, committed 9088K, reserved 1048576K
}
Event: 10.610 GC heap before
{Heap before GC invocations=24 (full 0):
 garbage-first heap   total 92160K, used 61083K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 7 young (14336K), 2 survivors (4096K)
 Metaspace       used 64911K, committed 65536K, reserved 1114112K
  class space    used 8869K, committed 9152K, reserved 1048576K
}
Event: 10.614 GC heap after
{Heap after GC invocations=25 (full 0):
 garbage-first heap   total 92160K, used 57192K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 1 young (2048K), 1 survivors (2048K)
 Metaspace       used 64911K, committed 65536K, reserved 1114112K
  class space    used 8869K, committed 9152K, reserved 1048576K
}
Event: 11.861 GC heap before
{Heap before GC invocations=26 (full 0):
 garbage-first heap   total 106496K, used 73576K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 7 young (14336K), 1 survivors (2048K)
 Metaspace       used 66315K, committed 66944K, reserved 1114112K
  class space    used 9045K, committed 9344K, reserved 1048576K
}
Event: 11.864 GC heap after
{Heap after GC invocations=27 (full 0):
 garbage-first heap   total 106496K, used 62792K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 1 young (2048K), 1 survivors (2048K)
 Metaspace       used 66315K, committed 66944K, reserved 1114112K
  class space    used 9045K, committed 9344K, reserved 1048576K
}
Event: 12.198 GC heap before
{Heap before GC invocations=27 (full 0):
 garbage-first heap   total 106496K, used 79176K [0x0000000700000000, 0x0000000800000000)
  region size 2048K, 9 young (18432K), 1 survivors (2048K)
 Metaspace       used 68441K, committed 69056K, reserved 1114112K
  class space    used 9332K, committed 9600K, reserved 1048576K
}

Dll operation events (2 events):
Event: 0.012 Loaded shared library C:\Program Files\Android\Android Studio\jbr\bin\java.dll
Event: 0.578 Loaded shared library C:\Program Files\Android\Android Studio\jbr\bin\verify.dll

Deoptimization events (20 events):
Event: 11.987 Thread 0x000001d435ebe020 DEOPT PACKING pc=0x000001d41ad2a204 sp=0x000000cf95ff00f0
Event: 11.987 Thread 0x000001d435ebe020 DEOPT UNPACKING pc=0x000001d41a5669a3 sp=0x000000cf95ff0060 mode 2
Event: 11.989 Thread 0x000001d435ebe020 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000001d41ad42408 relative=0x0000000000000468
Event: 11.989 Thread 0x000001d435ebe020 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000001d41ad42408 method=java.lang.String.substring(II)Ljava/lang/String; @ 31 c2
Event: 11.989 Thread 0x000001d435ebe020 DEOPT PACKING pc=0x000001d41ad42408 sp=0x000000cf95ff0160
Event: 11.989 Thread 0x000001d435ebe020 DEOPT UNPACKING pc=0x000001d41a5669a3 sp=0x000000cf95ff00c0 mode 2
Event: 12.079 Thread 0x000001d435ebe020 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000001d41b021748 relative=0x0000000000000308
Event: 12.079 Thread 0x000001d435ebe020 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000001d41b021748 method=java.lang.ThreadLocal$ThreadLocalMap.cleanSomeSlots(II)Z @ 37 c2
Event: 12.079 Thread 0x000001d435ebe020 DEOPT PACKING pc=0x000001d41b021748 sp=0x000000cf95ff17e0
Event: 12.079 Thread 0x000001d435ebe020 DEOPT UNPACKING pc=0x000001d41a5669a3 sp=0x000000cf95ff1708 mode 2
Event: 12.108 Thread 0x000001d435ebe020 Uncommon trap: trap_request=0xffffffde fr.pc=0x000001d41ae66010 relative=0x0000000000000a10
Event: 12.108 Thread 0x000001d435ebe020 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000001d41ae66010 method=java.io.BufferedInputStream.read1([BII)I @ 39 c2
Event: 12.108 Thread 0x000001d435ebe020 DEOPT PACKING pc=0x000001d41ae66010 sp=0x000000cf95ff1e20
Event: 12.108 Thread 0x000001d435ebe020 DEOPT UNPACKING pc=0x000001d41a5669a3 sp=0x000000cf95ff1d38 mode 2
Event: 12.108 Thread 0x000001d435ebe020 Uncommon trap: trap_request=0xffffffde fr.pc=0x000001d41ae66f84 relative=0x00000000000001c4
Event: 12.108 Thread 0x000001d435ebe020 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000001d41ae66f84 method=java.io.BufferedInputStream.read1([BII)I @ 39 c2
Event: 12.108 Thread 0x000001d435ebe020 DEOPT PACKING pc=0x000001d41ae66f84 sp=0x000000cf95ff1d80
Event: 12.108 Thread 0x000001d435ebe020 DEOPT UNPACKING pc=0x000001d41a5669a3 sp=0x000000cf95ff1d28 mode 2
Event: 12.109 Thread 0x000001d435ebe020 DEOPT PACKING pc=0x000001d413bad5d9 sp=0x000000cf95ff1750
Event: 12.109 Thread 0x000001d435ebe020 DEOPT UNPACKING pc=0x000001d41a567143 sp=0x000000cf95ff0c78 mode 0

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 10.133 Thread 0x000001d435ebe020 Exception <a 'java/lang/ClassNotFoundException'{0x000000070c241410}: java/lang/StringBeanInfo> (0x000000070c241410) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 10.133 Thread 0x000001d435ebe020 Exception <a 'java/lang/ClassNotFoundException'{0x000000070c247e28}: java/lang/StringCustomizer> (0x000000070c247e28) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 10.158 Thread 0x000001d435ebe020 Exception <a 'java/lang/ClassNotFoundException'{0x000000070c2f6b80}: settings_2gruv7bjn7ke34ukd7sgbaihaBeanInfo> (0x000000070c2f6b80) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 10.158 Thread 0x000001d435ebe020 Exception <a 'java/lang/ClassNotFoundException'{0x000000070c2fc628}: settings_2gruv7bjn7ke34ukd7sgbaihaCustomizer> (0x000000070c2fc628) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 10.223 Thread 0x000001d435ebe020 Exception <a 'java/lang/ClassNotFoundException'{0x0000000704f2c030}: build_cbgkpd0obcy601vdg3ozkkrdeBeanInfo> (0x0000000704f2c030) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 10.224 Thread 0x000001d435ebe020 Exception <a 'java/lang/ClassNotFoundException'{0x0000000704f365b0}: org/gradle/api/internal/project/ProjectScriptBeanInfo> (0x0000000704f365b0) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 10.224 Thread 0x000001d435ebe020 Exception <a 'java/lang/ClassNotFoundException'{0x0000000704f40b80}: org/gradle/api/internal/project/ProjectScriptCustomizer> (0x0000000704f40b80) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 10.226 Thread 0x000001d435ebe020 Exception <a 'java/lang/ClassNotFoundException'{0x0000000704f58058}: build_cbgkpd0obcy601vdg3ozkkrdeCustomizer> (0x0000000704f58058) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 10.231 Thread 0x000001d435ebe020 Exception <a 'java/lang/ClassNotFoundException'{0x0000000704fa3730}: org/gradle/api/internal/initialization/DefaultScriptHandlerBeanInfo> (0x0000000704fa3730) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 10.232 Thread 0x000001d435ebe020 Exception <a 'java/lang/ClassNotFoundException'{0x0000000704fb5a38}: org/gradle/api/internal/initialization/DefaultScriptHandlerCustomizer> (0x0000000704fb5a38) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 10.237 Thread 0x000001d435ebe020 Implicit null exception at 0x000001d41afda18f to 0x000001d41afda860
Event: 10.237 Thread 0x000001d435ebe020 Implicit null exception at 0x000001d41afdbd94 to 0x000001d41afdc450
Event: 10.241 Thread 0x000001d435ebe020 Exception <a 'java/lang/ClassNotFoundException'{0x0000000704c23170}: org/gradle/api/internal/artifacts/dsl/dependencies/DefaultDependencyHandler_DecoratedBeanInfo> (0x0000000704c23170) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 10.242 Thread 0x000001d435ebe020 Exception <a 'java/lang/ClassNotFoundException'{0x0000000704c3a600}: org/gradle/api/internal/artifacts/dsl/dependencies/DefaultDependencyHandlerBeanInfo> (0x0000000704c3a600) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 10.244 Thread 0x000001d435ebe020 Exception <a 'java/lang/ClassNotFoundException'{0x0000000704c51ad0}: org/gradle/api/internal/artifacts/dsl/dependencies/DefaultDependencyHandlerCustomizer> (0x0000000704c51ad0) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 10.247 Thread 0x000001d435ebe020 Exception <a 'java/lang/ClassNotFoundException'{0x0000000704c91d18}: org/gradle/api/internal/artifacts/dsl/dependencies/DefaultDependencyHandler_DecoratedCustomizer> (0x0000000704c91d18) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 10.500 Thread 0x000001d435ebe020 Exception <a 'java/lang/ClassNotFoundException'{0x000000070c7384a8}: build_cbgkpd0obcy601vdg3ozkkrdeBeanInfo> (0x000000070c7384a8) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 10.501 Thread 0x000001d435ebe020 Exception <a 'java/lang/ClassNotFoundException'{0x000000070c73ec80}: build_cbgkpd0obcy601vdg3ozkkrdeCustomizer> (0x000000070c73ec80) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 10.648 Thread 0x000001d435ebe020 Exception <a 'java/lang/ClassNotFoundException'{0x000000070fce5bd8}: build_f4lyfob7og778nmfmhgwc9617BeanInfo> (0x000000070fce5bd8) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 10.649 Thread 0x000001d435ebe020 Exception <a 'java/lang/ClassNotFoundException'{0x000000070fcf2790}: build_f4lyfob7og778nmfmhgwc9617Customizer> (0x000000070fcf2790) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]

VM Operations (20 events):
Event: 9.985 Executing VM operation: G1Concurrent done
Event: 10.002 Executing VM operation: G1Concurrent
Event: 10.003 Executing VM operation: G1Concurrent done
Event: 10.065 Executing VM operation: HandshakeAllThreads
Event: 10.065 Executing VM operation: HandshakeAllThreads done
Event: 10.322 Executing VM operation: G1CollectForAllocation
Event: 10.325 Executing VM operation: G1CollectForAllocation done
Event: 10.610 Executing VM operation: G1TryInitiateConcMark
Event: 10.614 Executing VM operation: G1TryInitiateConcMark done
Event: 10.657 Executing VM operation: G1Concurrent
Event: 11.663 Executing VM operation: G1Concurrent done
Event: 11.681 Executing VM operation: G1Concurrent
Event: 11.681 Executing VM operation: G1Concurrent done
Event: 11.861 Executing VM operation: G1CollectForAllocation
Event: 11.864 Executing VM operation: G1CollectForAllocation done
Event: 11.916 Executing VM operation: HandshakeAllThreads
Event: 11.917 Executing VM operation: HandshakeAllThreads done
Event: 11.983 Executing VM operation: HandshakeAllThreads
Event: 11.983 Executing VM operation: HandshakeAllThreads done
Event: 12.198 Executing VM operation: G1CollectForAllocation

Events (20 events):
Event: 12.090 loading class jdk/internal/reflect/UnsafeQualifiedBooleanFieldAccessorImpl
Event: 12.091 loading class jdk/internal/reflect/UnsafeQualifiedBooleanFieldAccessorImpl done
Event: 12.091 loading class jdk/internal/reflect/UnsafeIntegerFieldAccessorImpl
Event: 12.091 loading class jdk/internal/reflect/UnsafeIntegerFieldAccessorImpl done
Event: 12.140 loading class javax/xml/stream/XMLStreamException
Event: 12.140 loading class javax/xml/stream/XMLStreamException done
Event: 12.141 loading class org/xml/sax/InputSource
Event: 12.141 loading class org/xml/sax/InputSource done
Event: 12.144 loading class javax/xml/parsers/DocumentBuilder
Event: 12.144 loading class javax/xml/parsers/DocumentBuilder done
Event: 12.146 loading class org/xml/sax/SAXNotRecognizedException
Event: 12.146 loading class org/xml/sax/SAXNotRecognizedException done
Event: 12.146 loading class org/xml/sax/SAXNotSupportedException
Event: 12.146 loading class org/xml/sax/SAXNotSupportedException done
Event: 12.148 loading class org/xml/sax/ErrorHandler
Event: 12.148 loading class org/xml/sax/ErrorHandler done
Event: 12.162 loading class org/w3c/dom/DOMError
Event: 12.162 loading class org/w3c/dom/DOMError done
Event: 12.166 loading class org/xml/sax/EntityResolver
Event: 12.166 loading class org/xml/sax/EntityResolver done


Dynamic libraries:
0x00007ff706710000 - 0x00007ff70671a000 	C:\Program Files\Android\Android Studio\jbr\bin\java.exe
0x00007ffb87780000 - 0x00007ffb879e0000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ffb863b0000 - 0x00007ffb86477000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ffb84bf0000 - 0x00007ffb84fba000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ffb84fc0000 - 0x00007ffb8510b000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ffb59520000 - 0x00007ffb5953b000 	C:\Program Files\Android\Android Studio\jbr\bin\VCRUNTIME140.dll
0x00007ffb59540000 - 0x00007ffb59557000 	C:\Program Files\Android\Android Studio\jbr\bin\jli.dll
0x00007ffb86480000 - 0x00007ffb8664c000 	C:\WINDOWS\System32\USER32.dll
0x00007ffb85550000 - 0x00007ffb85577000 	C:\WINDOWS\System32\win32u.dll
0x00007ffb47d40000 - 0x00007ffb47fd7000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.3624_none_3e086962e3345f24\COMCTL32.dll
0x00007ffb85580000 - 0x00007ffb855aa000 	C:\WINDOWS\System32\GDI32.dll
0x00007ffb86870000 - 0x00007ffb86919000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ffb84a00000 - 0x00007ffb84b31000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ffb84b40000 - 0x00007ffb84be3000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffb85b70000 - 0x00007ffb85b9f000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ffb6c850000 - 0x00007ffb6c85c000 	C:\Program Files\Android\Android Studio\jbr\bin\vcruntime140_1.dll
0x00007ffa69320000 - 0x00007ffa693ad000 	C:\Program Files\Android\Android Studio\jbr\bin\msvcp140.dll
0x00007ffa645d0000 - 0x00007ffa65250000 	C:\Program Files\Android\Android Studio\jbr\bin\server\jvm.dll
0x00007ffb86970000 - 0x00007ffb86a22000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ffb86660000 - 0x00007ffb86706000 	C:\WINDOWS\System32\sechost.dll
0x00007ffb86710000 - 0x00007ffb86826000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffb73320000 - 0x00007ffb73356000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ffb6cfa0000 - 0x00007ffb6cfaa000 	C:\WINDOWS\SYSTEM32\WSOCK32.dll
0x00007ffb78740000 - 0x00007ffb7874b000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ffb861d0000 - 0x00007ffb86244000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ffb83870000 - 0x00007ffb8388a000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ffb0e950000 - 0x00007ffb0e95a000 	C:\Program Files\Android\Android Studio\jbr\bin\jimage.dll
0x00007ffb82a40000 - 0x00007ffb82c81000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ffb87240000 - 0x00007ffb875c2000 	C:\WINDOWS\System32\combase.dll
0x00007ffb87160000 - 0x00007ffb87236000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ffb73250000 - 0x00007ffb73289000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ffb84960000 - 0x00007ffb849f9000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ffb0c5d0000 - 0x00007ffb0c5f5000 	C:\Program Files\Android\Android Studio\jbr\bin\java.dll
0x00007ffb0c5b0000 - 0x00007ffb0c5c8000 	C:\Program Files\Android\Android Studio\jbr\bin\zip.dll
0x00007ffb86a30000 - 0x00007ffb87159000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ffb85290000 - 0x00007ffb853f8000 	C:\WINDOWS\System32\wintypes.dll
0x00007ffb82110000 - 0x00007ffb82962000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ffb86250000 - 0x00007ffb8633d000 	C:\WINDOWS\System32\SHCORE.dll
0x00007ffb876d0000 - 0x00007ffb87734000 	C:\WINDOWS\System32\shlwapi.dll
0x00007ffb84870000 - 0x00007ffb8489f000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ffb0c590000 - 0x00007ffb0c5a9000 	C:\Program Files\Android\Android Studio\jbr\bin\net.dll
0x00007ffb7e270000 - 0x00007ffb7e38d000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007ffb83db0000 - 0x00007ffb83e1a000 	C:\WINDOWS\system32\mswsock.dll
0x00007ffa932f0000 - 0x00007ffa93306000 	C:\Program Files\Android\Android Studio\jbr\bin\nio.dll
0x00007ffb0e8e0000 - 0x00007ffb0e8f0000 	C:\Program Files\Android\Android Studio\jbr\bin\verify.dll
0x00007ffa911a0000 - 0x00007ffa911c7000 	C:\Users\<USER>\.gradle\native\e1d6ef7f7dcc3fd88c89a11ec53ec762bb8ba0a96d01ffa2cd45eb1d1d8dd5c5\windows-amd64\native-platform.dll
0x00007ffa66360000 - 0x00007ffa664a4000 	C:\Users\<USER>\.gradle\native\0d407fdbe67a94daf76414ababcb853783967236a71b16ec16e742cd7a986fd3\windows-amd64\native-platform-file-events.dll
0x00007ffac10a0000 - 0x00007ffac10a9000 	C:\Program Files\Android\Android Studio\jbr\bin\management.dll
0x00007ffac0c60000 - 0x00007ffac0c6b000 	C:\Program Files\Android\Android Studio\jbr\bin\management_ext.dll
0x00007ffb86860000 - 0x00007ffb86868000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007ffb84050000 - 0x00007ffb8406c000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007ffb837d0000 - 0x00007ffb8380a000 	C:\WINDOWS\system32\rsaenh.dll
0x00007ffb83e50000 - 0x00007ffb83e7b000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007ffb84840000 - 0x00007ffb84866000 	C:\WINDOWS\SYSTEM32\bcrypt.dll
0x00007ffb84070000 - 0x00007ffb8407c000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007ffb831e0000 - 0x00007ffb83213000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007ffb86650000 - 0x00007ffb8665a000 	C:\WINDOWS\System32\NSI.dll
0x00007ffb7dba0000 - 0x00007ffb7dbbf000 	C:\WINDOWS\SYSTEM32\dhcpcsvc6.DLL
0x00007ffb7d700000 - 0x00007ffb7d725000 	C:\WINDOWS\SYSTEM32\dhcpcsvc.DLL
0x00007ffb83270000 - 0x00007ffb83395000 	C:\WINDOWS\SYSTEM32\DNSAPI.dll
0x00007ffae7e30000 - 0x00007ffae7e38000 	C:\WINDOWS\system32\wshunix.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Program Files\Android\Android Studio\jbr\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.3624_none_3e086962e3345f24;C:\Program Files\Android\Android Studio\jbr\bin\server;C:\Users\<USER>\.gradle\native\e1d6ef7f7dcc3fd88c89a11ec53ec762bb8ba0a96d01ffa2cd45eb1d1d8dd5c5\windows-amd64;C:\Users\<USER>\.gradle\native\0d407fdbe67a94daf76414ababcb853783967236a71b16ec16e742cd7a986fd3\windows-amd64

VM Arguments:
jvm_args: --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED -Xmx4G -Dfile.encoding=windows-1252 -Duser.country=US -Duser.language=en -Duser.variant 
java_command: org.gradle.launcher.daemon.bootstrap.GradleDaemon 7.5
java_class_path (initial): C:\Users\<USER>\.gradle\wrapper\dists\gradle-7.5-all\6qsw290k5lz422uaf8jf6m7co\gradle-7.5\lib\gradle-launcher-7.5.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 12                                        {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 13                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 2097152                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 266338304                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 4294967296                                {product} {command line}
   size_t MaxNewSize                               = 2575302656                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 2097152                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 7602480                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122027880                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122027880                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 4294967296                             {manageable} {ergonomic}
     bool UseCompressedClassPointers               = true                           {product lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
JAVA_HOME=C:\Program Files\Android\Android Studio\jbr
CLASSPATH=D:\Ecommerce-App\android\\gradle\wrapper\gradle-wrapper.jar
PATH=C:\Program Files\Android\Android Studio\jbr\bin;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Microsoft SQL Server\150\Tools\Binn\;C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\170\Tools\Binn\;C:\Program Files\dotnet\;C:\Program Files (x86)\Microsoft SQL Server\160\Tools\Binn\;C:\Program Files\Microsoft SQL Server\160\Tools\Binn\;C:\Program Files\Microsoft SQL Server\160\DTS\Binn\;C:\Program Files (x86)\Microsoft SQL Server\160\DTS\Binn\;C:\Program Files\Azure Data Studio\bin;C:\Program Files\Git\cmd;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\.dotnet\tools;C:\Program Files\Azure Data Studio\bin;C:\flutter\bin;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin;;C:\flutter\bin\mingit\cmd;C:\flutter\bin\mingit\cmd
USERNAME=zaher
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 165 Stepping 5, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp


JNI global refs:
JNI global refs: 34, weak refs: 0

JNI global refs memory usage: 843, weak refs: 841

OOME stack traces (most recent first):
Classloader memory used:
Loader org.gradle.internal.classloader.VisitableURLClassLoader                         : 4218K
Loader bootstrap                                                                       : 2616K
Loader org.gradle.initialization.MixInLegacyTypesClassLoader                           : 1196K
Loader org.gradle.internal.classloader.VisitableURLClassLoader                         : 383K
Loader jdk.internal.loader.ClassLoaders$PlatformClassLoader                            : 65722B
Loader jdk.internal.reflect.DelegatingClassLoader                                      : 26037B
Loader jdk.internal.loader.ClassLoaders$AppClassLoader                                 : 25681B
Loader org.gradle.groovy.scripts.internal.DefaultScriptCompilationHandler$ScriptClassLoader: 18958B
Loader org.codehaus.groovy.runtime.callsite.CallSiteClassLoader                        : 5727B
Loader sun.reflect.misc.MethodUtil                                                     : 373B

Classes loaded by more than one classloader:
Class Program                                                                         : loaded 3 times (x 70B)
Class com.google.common.collect.Iterators$MergingIterator                             : loaded 2 times (x 79B)
Class [Lcom.google.common.collect.AbstractMapEntry;                                   : loaded 2 times (x 67B)
Class com.google.common.collect.SingletonImmutableList                                : loaded 2 times (x 167B)
Class org.gradle.internal.classpath.ClassPath                                         : loaded 2 times (x 68B)
Class org.gradle.api.internal.classpath.DefaultModuleRegistry                         : loaded 2 times (x 84B)
Class com.google.common.collect.ListMultimap                                          : loaded 2 times (x 68B)
Class com.google.common.base.CharMatcher$JavaDigit                                    : loaded 2 times (x 109B)
Class com.google.common.base.CharMatcher$Digit                                        : loaded 2 times (x 110B)
Class com.google.common.collect.AbstractMultimap                                      : loaded 2 times (x 121B)
Class org.gradle.api.Action                                                           : loaded 2 times (x 68B)
Class com.google.common.collect.ImmutableEntry                                        : loaded 2 times (x 80B)
Class com.google.common.collect.Lists$StringAsImmutableList                           : loaded 2 times (x 167B)
Class com.google.common.base.CharMatcher                                              : loaded 2 times (x 109B)
Class com.google.common.base.CharMatcher$IsNot                                        : loaded 2 times (x 109B)
Class com.google.common.base.Splitter                                                 : loaded 2 times (x 70B)
Class com.google.common.collect.Iterators$ArrayItr                                    : loaded 2 times (x 95B)
Class org.gradle.api.internal.DefaultClassPathProvider                                : loaded 2 times (x 74B)
Class org.gradle.internal.installation.GradleInstallation$1                           : loaded 2 times (x 73B)
Class com.google.common.base.CharMatcher$BitSetMatcher                                : loaded 2 times (x 110B)
Class com.google.common.collect.ImmutableMap$IteratorBasedImmutableMap                : loaded 2 times (x 123B)
Class com.google.common.collect.ImmutableMap                                          : loaded 2 times (x 118B)
Class com.google.common.base.Converter                                                : loaded 2 times (x 88B)
Class com.google.common.base.Predicate                                                : loaded 2 times (x 68B)
Class org.gradle.internal.classloader.FilteringClassLoader                            : loaded 2 times (x 102B)
Class com.google.common.collect.RegularImmutableSet                                   : loaded 2 times (x 146B)
Class org.gradle.internal.classpath.DefaultClassPath$ImmutableUniqueList$Builder      : loaded 2 times (x 73B)
Class com.google.common.collect.Maps$8                                                : loaded 2 times (x 80B)
Class com.google.common.base.CharMatcher$InRange                                      : loaded 2 times (x 109B)
Class com.google.common.collect.BiMap                                                 : loaded 2 times (x 68B)
Class com.google.common.collect.Lists                                                 : loaded 2 times (x 69B)
Class com.google.common.math.IntMath$1                                                : loaded 2 times (x 69B)
Class org.gradle.internal.classloader.ClassLoaderVisitor                              : loaded 2 times (x 74B)
Class com.google.common.math.MathPreconditions                                        : loaded 2 times (x 69B)
Class org.gradle.internal.service.DefaultServiceLocator                               : loaded 2 times (x 81B)
Class org.gradle.internal.service.UnknownServiceException                             : loaded 2 times (x 81B)
Class com.google.common.collect.ArrayListMultimap                                     : loaded 2 times (x 170B)
Class org.gradle.internal.classloader.DefaultClassLoaderFactory                       : loaded 2 times (x 80B)
Class com.google.common.collect.UnmodifiableIterator                                  : loaded 2 times (x 78B)
Class com.google.common.base.CharMatcher$SingleWidth                                  : loaded 2 times (x 110B)
Class com.google.common.collect.Hashing                                               : loaded 2 times (x 69B)
Class com.google.common.base.JdkPattern                                               : loaded 2 times (x 73B)
Class com.google.common.collect.Multimap                                              : loaded 2 times (x 68B)
Class org.gradle.internal.classloader.VisitableURLClassLoader$Spec                    : loaded 2 times (x 72B)
Class org.gradle.api.GradleException                                                  : loaded 2 times (x 80B)
Class com.google.common.base.CharMatcher$JavaLetterOrDigit                            : loaded 2 times (x 109B)
Class org.gradle.api.internal.classpath.ModuleRegistry                                : loaded 2 times (x 68B)
Class com.google.common.collect.ImmutableCollection                                   : loaded 2 times (x 123B)
Class org.gradle.api.internal.ClassPathProvider                                       : loaded 2 times (x 68B)
Class com.google.common.collect.RegularImmutableMap$Values                            : loaded 2 times (x 167B)
Class org.gradle.internal.classloader.ClasspathUtil$1                                 : loaded 2 times (x 74B)
Class com.google.common.collect.ImmutableEnumMap                                      : loaded 2 times (x 123B)
Class com.google.common.collect.ImmutableList$ReverseImmutableList                    : loaded 2 times (x 168B)
Class build_cbgkpd0obcy601vdg3ozkkrde                                                 : loaded 2 times (x 178B)
Class org.gradle.internal.classloader.ClassLoaderFactory                              : loaded 2 times (x 68B)
Class com.google.common.collect.ObjectArrays                                          : loaded 2 times (x 69B)
Class com.google.common.collect.Iterators$10                                          : loaded 2 times (x 79B)
Class com.google.common.collect.ImmutableList                                         : loaded 2 times (x 166B)
Class org.gradle.api.internal.classpath.ManifestUtil                                  : loaded 2 times (x 69B)
Class org.gradle.api.specs.Spec                                                       : loaded 2 times (x 68B)
Class com.google.common.base.CharMatcher$Whitespace                                   : loaded 2 times (x 110B)
Class com.google.common.collect.Iterators$1                                           : loaded 2 times (x 79B)
Class com.google.common.collect.Iterators$4                                           : loaded 2 times (x 80B)
Class com.google.common.collect.Iterators$5                                           : loaded 2 times (x 80B)
Class com.google.common.base.Preconditions                                            : loaded 2 times (x 69B)
Class com.google.common.base.Function                                                 : loaded 2 times (x 68B)
Class com.google.common.collect.Iterators$9                                           : loaded 2 times (x 79B)
Class org.gradle.internal.IoActions                                                   : loaded 2 times (x 69B)
Class settings_2gruv7bjn7ke34ukd7sgbaiha                                              : loaded 2 times (x 177B)
Class com.google.common.collect.RegularImmutableMap$KeySet                            : loaded 2 times (x 148B)
Class com.google.common.collect.CollectPreconditions                                  : loaded 2 times (x 69B)
Class com.google.common.collect.Maps                                                  : loaded 2 times (x 69B)
Class com.google.common.collect.RegularImmutableMap                                   : loaded 2 times (x 119B)
Class com.google.common.collect.AbstractIndexedListIterator                           : loaded 2 times (x 94B)
Class com.google.common.base.CharMatcher$None                                         : loaded 2 times (x 110B)
Class org.gradle.api.internal.classpath.EffectiveClassPath                            : loaded 2 times (x 88B)
Class com.google.common.collect.UnmodifiableListIterator                              : loaded 2 times (x 93B)
Class com.google.common.collect.ImmutableList$1                                       : loaded 2 times (x 95B)
Class com.google.common.base.Splitter$Strategy                                        : loaded 2 times (x 68B)
Class com.google.common.collect.ImmutableMapEntrySet$RegularEntrySet                  : loaded 2 times (x 149B)
Class [Lcom.google.common.collect.ImmutableMapEntry;                                  : loaded 2 times (x 67B)
Class org.gradle.internal.installation.CurrentGradleInstallation                      : loaded 2 times (x 71B)
Class org.gradle.internal.installation.CurrentGradleInstallationLocator               : loaded 2 times (x 69B)
Class org.gradle.api.internal.classpath.Module                                        : loaded 2 times (x 68B)
Class com.google.common.base.Splitter$1$1                                             : loaded 2 times (x 84B)
Class com.google.common.collect.ImmutableSet$JdkBackedSetBuilderImpl                  : loaded 2 times (x 74B)
Class com.google.common.collect.ImmutableSet$RegularSetBuilderImpl                    : loaded 2 times (x 75B)
Class org.gradle.internal.service.CachingServiceLocator                               : loaded 2 times (x 80B)
Class [Lcom.google.common.collect.ImmutableEntry;                                     : loaded 2 times (x 67B)
Class org.gradle.internal.classpath.DefaultClassPath                                  : loaded 2 times (x 88B)
Class org.gradle.api.Transformer                                                      : loaded 2 times (x 68B)
Class com.google.common.base.Splitter$SplittingIterator                               : loaded 2 times (x 82B)
Class com.google.common.base.CharMatcher$Is                                           : loaded 2 times (x 109B)
Class com.google.common.collect.RegularImmutableAsList                                : loaded 2 times (x 176B)
Class com.google.common.collect.PeekingIterator                                       : loaded 2 times (x 68B)
Class com.google.common.collect.ImmutableMapEntrySet                                  : loaded 2 times (x 149B)
Class com.google.common.collect.ImmutableBiMapFauxverideShim                          : loaded 2 times (x 118B)
Class org.gradle.internal.classloader.SystemClassLoaderSpec                           : loaded 2 times (x 69B)
Class com.google.common.collect.SingletonImmutableSet                                 : loaded 2 times (x 146B)
Class [Lcom.google.common.base.AbstractIterator$State;                                : loaded 2 times (x 67B)
Class com.google.common.collect.ImmutableMap$Builder                                  : loaded 2 times (x 78B)
Class com.google.common.base.AbstractIterator                                         : loaded 2 times (x 78B)
Class com.google.common.base.AbstractIterator$1                                       : loaded 2 times (x 69B)
Class com.google.common.collect.Iterators                                             : loaded 2 times (x 69B)
Class com.google.common.base.CharMatcher$1                                            : loaded 2 times (x 111B)
Class com.google.common.base.CharMatcher$Ascii                                        : loaded 2 times (x 110B)
Class org.gradle.api.UncheckedIOException                                             : loaded 2 times (x 80B)
Class com.google.common.base.CharMatcher$And                                          : loaded 2 times (x 110B)
Class com.google.common.collect.IndexedImmutableSet                                   : loaded 2 times (x 148B)
Class com.google.common.collect.AbstractListMultimap                                  : loaded 2 times (x 170B)
Class com.google.common.base.CharMatcher$Any                                          : loaded 2 times (x 110B)
Class org.gradle.internal.installation.GradleInstallation                             : loaded 2 times (x 73B)
Class com.google.common.collect.ArrayListMultimapGwtSerializationDependencies         : loaded 2 times (x 170B)
Class com.google.common.base.CharMatcher$RangesMatcher                                : loaded 2 times (x 110B)
Class com.google.common.collect.ImmutableList$SubList                                 : loaded 2 times (x 168B)
Class org.gradle.internal.classloader.ClasspathUtil                                   : loaded 2 times (x 69B)
Class com.google.common.base.Splitter$1                                               : loaded 2 times (x 75B)
Class com.google.common.collect.Maps$BiMapConverter                                   : loaded 2 times (x 88B)
Class org.gradle.api.internal.DefaultClassPathRegistry                                : loaded 2 times (x 74B)
Class com.google.common.collect.ImmutableSet$SetBuilderImpl                           : loaded 2 times (x 74B)
Class org.gradle.api.internal.classpath.DefaultModuleRegistry$DefaultModule           : loaded 2 times (x 84B)
Class com.google.common.base.CharMatcher$JavaIsoControl                               : loaded 2 times (x 110B)
Class com.google.common.collect.ImmutableMap$1                                        : loaded 2 times (x 79B)
Class com.google.common.base.CharMatcher$Or                                           : loaded 2 times (x 110B)
Class com.google.common.collect.Iterables                                             : loaded 2 times (x 69B)
Class com.google.common.base.CharMatcher$JavaLowerCase                                : loaded 2 times (x 109B)
Class org.gradle.api.internal.classpath.UnknownModuleException                        : loaded 2 times (x 80B)
Class com.google.common.collect.Maps$EntryTransformer                                 : loaded 2 times (x 68B)
Class com.google.common.collect.ImmutableCollection$Builder                           : loaded 2 times (x 74B)
Class com.google.common.base.CommonPattern                                            : loaded 2 times (x 72B)
Class org.gradle.internal.service.ServiceLookupException                              : loaded 2 times (x 80B)
Class org.gradle.cache.GlobalCache                                                    : loaded 2 times (x 68B)
Class com.google.common.base.CharMatcher$NegatedFastMatcher                           : loaded 2 times (x 111B)
Class [Lorg.gradle.api.internal.ClassPathProvider;                                    : loaded 2 times (x 67B)
Class org.gradle.util.internal.GUtil                                                  : loaded 2 times (x 69B)
Class com.google.common.math.IntMath                                                  : loaded 2 times (x 69B)
Class com.google.common.collect.AbstractIterator                                      : loaded 2 times (x 80B)
Class org.gradle.internal.classloader.ClassLoaderSpec                                 : loaded 2 times (x 69B)
Class com.google.common.collect.AbstractMapEntry                                      : loaded 2 times (x 79B)
Class com.google.common.base.CharMatcher$Negated                                      : loaded 2 times (x 111B)
Class com.google.common.collect.ImmutableSet$Builder                                  : loaded 2 times (x 83B)
Class com.google.common.base.CharMatcher$ForPredicate                                 : loaded 2 times (x 110B)
Class com.google.common.collect.ImmutableMap$MapViewOfValuesAsSingletonSets           : loaded 2 times (x 123B)
Class com.google.common.collect.SortedMapDifference                                   : loaded 2 times (x 68B)
Class org.gradle.internal.classloader.VisitableURLClassLoader                         : loaded 2 times (x 114B)
Class com.google.common.collect.ImmutableBiMap                                        : loaded 2 times (x 142B)
Class org.gradle.internal.Cast                                                        : loaded 2 times (x 69B)
Class Build_gradle                                                                    : loaded 2 times (x 128B)
Class org.gradle.api.internal.ClassPathRegistry                                       : loaded 2 times (x 68B)
Class com.google.common.collect.ImmutableSet                                          : loaded 2 times (x 145B)
Class org.gradle.internal.classloader.ClassLoaderHierarchy                            : loaded 2 times (x 68B)
Class com.google.common.base.AbstractIterator$State                                   : loaded 2 times (x 77B)
Class com.google.common.base.CharMatcher$NamedFastMatcher                             : loaded 2 times (x 110B)
Class org.gradle.internal.service.ServiceLocator                                      : loaded 2 times (x 68B)
Class com.google.common.collect.ImmutableMapEntry                                     : loaded 2 times (x 83B)
Class com.google.common.base.CharMatcher$Invisible                                    : loaded 2 times (x 110B)
Class com.google.common.base.CharMatcher$FastMatcher                                  : loaded 2 times (x 109B)
Class org.gradle.internal.classpath.DefaultClassPath$ImmutableUniqueList              : loaded 2 times (x 159B)
Class com.google.common.base.CharMatcher$JavaLetter                                   : loaded 2 times (x 109B)
Class com.google.common.collect.MapDifference                                         : loaded 2 times (x 68B)
Class com.google.common.collect.Platform                                              : loaded 2 times (x 69B)
Class com.google.common.collect.ImmutableAsList                                       : loaded 2 times (x 169B)
Class com.google.common.base.CharMatcher$AnyOf                                        : loaded 2 times (x 110B)
Class com.google.common.base.CharMatcher$IsEither                                     : loaded 2 times (x 109B)
Class com.google.common.collect.RegularImmutableList                                  : loaded 2 times (x 172B)
Class com.google.common.base.CharMatcher$JavaUpperCase                                : loaded 2 times (x 109B)
Class com.google.common.collect.Multiset                                              : loaded 2 times (x 68B)
Class build_cbgkpd0obcy601vdg3ozkkrde$_run_closure1                                   : loaded 2 times (x 134B)
Class com.google.common.collect.ImmutableMapEntry$NonTerminalImmutableMapEntry        : loaded 2 times (x 83B)
Class com.google.common.collect.AbstractMapBasedMultimap                              : loaded 2 times (x 137B)


---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 26100 (10.0.26100.3775)
OS uptime: 9 days 4:13 hours
Hyper-V role detected

CPU: total 16 (initial active 16) (8 cores per cpu, 2 threads per core) family 6 model 165 stepping 5 microcode 0xe0, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, fma, vzeroupper, clflush, clflushopt, hv

Memory: 4k page, system-wide physical 16195M (1175M free)
TotalPageFile size 64338M (AvailPageFile size 7M)
current process WorkingSet (physical memory assigned to process): 326M, peak: 326M
current process commit charge ("private bytes"): 379M, peak: 404M

vm_info: OpenJDK 64-Bit Server VM (17.0.7+0-b2043.56-10550314) for windows-amd64 JRE (17.0.7+0-b2043.56-10550314), built on Jul 24 2023 18:27:45 by "androidbuild" with MS VC++ 16.10 / 16.11 (VS2019)

END.
