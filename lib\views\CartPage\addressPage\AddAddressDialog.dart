import 'package:alderishop/controllers/addresses_controller.dart';
import 'package:alderishop/controllers/app_controller.dart';
import 'package:alderishop/data/model/addresses_model.dart';
import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class AddAddressDialog extends StatefulWidget {
  final int? id;

  const AddAddressDialog({Key? key, this.id}) : super(key: key);

  @override
  _AddAddressDialogState createState() => _AddAddressDialogState();
}

class _AddAddressDialogState extends State<AddAddressDialog> {
  var mdoel = AddressesModel();

  @override
  void initState() {
    if (widget.id != null) {
      mdoel = Provider.of<AddressesController>(context, listen: false)
          .addresses
          .firstWhere((element) => element.id == widget.id);
    }
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      backgroundColor: Colors.white,
      content: SingleChildScrollView(
        child: SizedBox(
          width: AppController.W,
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 5),
            child: DottedBorder(
              padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 15),
              color: const Color.fromARGB(255, 0, 0, 0),
              strokeWidth: 2,
              dashPattern: const [5, 5],
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Center(
                    child: Column(
                      children: [
                        SizedBox(height: AppController.h * 0.01),
                        // CommonTextField(
                        //   initialValue: mdoel.title,
                        //   label: T('Title *'),
                        //   onChanged: (value) {
                        //     mdoel.title = value;
                        //   },
                        // ),
                        // SizedBox(height: AppController.h * 0.01),
                        // CommonTextField(
                        //   initialValue: mdoel.fullName,
                        //   label: T('Full Name *'),
                        //   onChanged: (value) {
                        //     mdoel.fullName = value;
                        //   },
                        // ),
                        // SizedBox(height: AppController.h * 0.01),
                        // CommonTextField(
                        //   initialValue: mdoel.email,
                        //   keyboardType: TextInputType.emailAddress,
                        //   label: T('Email *'),
                        //   onChanged: (value) {
                        //     mdoel.email = value;
                        //   },
                        // ),
                        // SizedBox(height: AppController.h * 0.01),
                        // CommonTextField(
                        //   initialValue: mdoel.phoneNumber,
                        //   keyboardType: TextInputType.phone,
                        //   label: T('Phone *'),
                        //   onChanged: (value) {
                        //     mdoel.phoneNumber = value;
                        //   },
                        // ),
                        // SizedBox(height: AppController.h * 0.01),
                        // CommonTextField(
                        //   initialValue: mdoel.address1,
                        //   label: T('Addrees1 *'),
                        //   onChanged: (value) {
                        //     mdoel.address1 = value;
                        //   },
                        // ),
                        // SizedBox(height: AppController.h * 0.01),
                        // CommonTextField(
                        //   initialValue: mdoel.address1,
                        //   label: T('Addrees2 *'),
                        //   onChanged: (value) {
                        //     mdoel.address2 = value;
                        //   },
                        // ),
                        // MyComboBox(
                        //   borderRadius: 2,
                        //   selectedValue: mdoel.countryId,
                        //   isShowLabel: false,
                        //   caption: mdoel.countryId != null
                        //       ? Provider.of<ConfigrationController>(context,
                        //               listen: false)
                        //           .countries
                        //           .firstWhere((element) =>
                        //               element.id == mdoel.countryId)
                        //           .name
                        //       : T("Select Country"),
                        //   onSelect: (int? id, String name) {
                        //     setState(() {
                        //       mdoel.countryId = id;
                        //     });
                        //   },
                        //   backColor: AppColors.SECOUND_COLOR,
                        //   modalTitle: T('Countries'),
                        //   fontColor: AppColors.BLACK_COLOR,
                        //   fontSize: 12,
                        //   data: Provider.of<ConfigrationController>(context,
                        //           listen: false)
                        //       .countries,
                        //   labelText: mdoel.countryId != null
                        //       ? Provider.of<ConfigrationController>(context,
                        //               listen: false)
                        //           .countries
                        //           .firstWhere((element) =>
                        //               element.id == mdoel.countryId)
                        //           .name
                        //       : T("Select Country"),
                        // ),
                        // SizedBox(height: AppController.h * 0.01),
                        // InkWell(
                        //   onTap: () async {
                        //     pleaseWaitDialog(context: context, isShown: true);
                        //     bool result = false;
                        //     if (mdoel.id != null) {
                        //       result = await Provider.of<AddressesController>(
                        //               context,
                        //               listen: false)
                        //           .updateAddress(addresses: mdoel);
                        //     } else {
                        //       result = await Provider.of<AddressesController>(
                        //               context,
                        //               listen: false)
                        //           .addAddress(model: mdoel);
                        //     }

                        //     // ignore: use_build_context_synchronously
                        //     pleaseWaitDialog(context: context, isShown: false);
                        //     if (result == true) {
                        //       // ignore: use_build_context_synchronously
                        //       Navigator.of(context).pop();
                        //       // ignore: use_build_context_synchronously
                        //       successMsg(
                        //         context: context,
                        //         msg: T("Operation done Successfully"),
                        //       );
                        //     } else {
                        //       // ignore: use_build_context_synchronously
                        //       errorMsg(context: context, title: T('Error'));
                        //     }
                        //   },
                        //   child: CustomContainerWidget(
                        //     height: 30.h,
                        //     width: 320.w,
                        //     text: T('Add'),
                        //     color: AppColors.Theard_COLOR,
                        //     fontSize: 14,
                        //   ),
                        // ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
