import 'package:alderishop/components/CustomInputField.dart';
import 'package:alderishop/constants/constants.dart';
import 'package:alderishop/controllers/app_controller.dart';
import 'package:alderishop/data/model/auth_model.dart';
import 'package:alderishop/services/helper.dart';
import 'package:alderishop/views/profile/widgets/customContanier.dart';
import 'package:alderishop/views/auth/send_code.dart';
import 'package:alderishop/views/home/<USER>/%C4%B0magebgWidget.dart';
import 'package:alderishop/views/home/<USER>/CustomText.dart';
import 'package:flutter/material.dart';

class ResetPasswordScreen extends StatefulWidget {
  final LoginModel model;
  const ResetPasswordScreen({super.key, required this.model});

  @override
  State<ResetPasswordScreen> createState() => _ResetPasswordScreenState();
}

class _ResetPasswordScreenState extends State<ResetPasswordScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          ImageBgWidget(
            height: 230,
          ),
          SizedBox(
            height: AppController.h * 0.07,
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 40),
            child: Column(
              children: [
                Center(
                  child: CustomText(
                      text: T('Reset Password'),
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: AppColors.BLACK_COLOR),
                ),
                SizedBox(
                  height: AppController.h * 0.03,
                ),
                CustomInputField(
                  hintText: T('email'),
                  backgroundColor: const Color(0xFFB3E5FC).withOpacity(0.4),
                  onChanged: (value) {
                    setState(() {});
                  },
                ),
                SizedBox(
                  height: AppController.h * 0.01,
                ),
                InkWell(
                  onTap: () {
                    Navigator.of(context).pushReplacement(MaterialPageRoute(
                        builder: (context) => const SendCodePage()));
                  },
                  child: CustomContainer(
                    width: AppController.W,
                    text: T("Send Code"),
                    color: AppColors.brown,
                  ),
                )
              ],
            ),
          )
        ],
      ),
    );
  }
}
