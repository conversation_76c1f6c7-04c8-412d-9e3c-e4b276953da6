import 'package:alderishop/components/CustomInputField.dart';
import 'package:alderishop/components/common_text_field.dart';
import 'package:alderishop/data/model/auth_model.dart';
import 'package:alderishop/services/helper.dart';
import 'package:alderishop/views/profile/widgets/customContanier.dart';
import 'package:alderishop/views/auth/reset_password_page.dart';
import 'package:alderishop/views/home/<USER>/%C4%B0magebgWidget.dart';
import 'package:alderishop/views/home/<USER>';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:alderishop/components/common_snckbar.dart';
import 'package:alderishop/constants/constants.dart';
import 'package:alderishop/controllers/app_controller.dart';
import 'package:alderishop/controllers/auth_Controller.dart';
import 'package:alderishop/views/auth/Sign_up.dart';
import 'package:alderishop/views/home/<USER>/CustomText.dart';

class SignInPage extends StatefulWidget {
  const SignInPage({super.key});

  @override
  State<SignInPage> createState() => _SignInPageState();
}

class _SignInPageState extends State<SignInPage> {
  var model = LoginModel(
      emailOrUserName: '<EMAIL>',
      password: '@Dm!n123',
      rememberMe: true,
      isCustomerApp: true);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.WHITE_COLOR,
      body: SingleChildScrollView(
        child: Column(
          children: [
            ImageBgWidget(
              height: 220,
            ),
            SizedBox(
              height: AppController.h * 0.1,
            ),
            Image.asset("assets/img/new/logo2.png"),
            SizedBox(
              height: AppController.h * 0.06,
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 30),
              child: Column(
                children: [
                   SizedBox(
                    height: AppController.h * 0.02,
                  ),
                  MyPhoneField(
                    textInputAction: TextInputAction.next,
                    hintText: T('************'),
                    
                    labelColor: AppColors.BLACK_GREY.withOpacity(0.5),
                    onChanged: (val) {
                      model.emailOrUserName = val.countryCode + val.number;
                    },
                    onCountryChanged: (val) {},
                  ),
                  CustomInputField(
                   
                    hintText: T("Password"),
                    backgroundColor: AppColors.WHITE_COLOR,
                    isPassword: true,
                    keyboardType: TextInputType.visiblePassword,
                    onChanged: (value) {
                      setState(() {
                        model.password = value;
                      });
                    },
                  ),
                  SizedBox(
                    height: AppController.h * 0.02,
                  ),
                  InkWell(
onTap: () async {
  if (model.password!.isEmpty && model.emailOrUserName!.isEmpty) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (context.mounted) {
        errorMsg(
            context: context,
            title: T('Error'),
            msg: T("Please write userName and password"));
      }
    });
  } else if (model.password!.isEmpty) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (context.mounted) {
        errorMsg(
            context: context,
            title: T('Error'),
            msg: T("Please write Your password"));
      }
    });
  } else if (model.emailOrUserName!.isEmpty) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (context.mounted) {
        errorMsg(
            context: context,
            title: T('Error'),
            msg: T("Please write userName"));
      }
    });
  } else {
    var result = await Provider.of<AuthController>(
            context,
            listen: false)
        .login(context, model);
    if (result.isSuccess) {
      // Success: Navigate to HomePage
      if (context.mounted) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(
            builder: (context) => const HomePage(),
          ),
        );
      }
    } else {
      // Failed login: Show error message
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (context.mounted) {
          errorMsg(
              context: context,
              title: T('Error'),
              msg: T("Phone number or password is incorrect"));
        }
      });
    }
  }
},

                      child: CustomContainer(
                        width: AppController.W,
                        text: T("Sign In"),
                        color: AppColors.brown,
                      )),
                  SizedBox(
                    height: AppController.h * 0.09,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      CustomText(
                          text: T('Forget Password ?'),
                          fontSize: 13,
                          fontWeight: FontWeight.bold,
                          color: AppColors.PRIMARY_COLOR),
                      InkWell(
                        onTap: () {
                          Navigator.of(context)
                              .pushReplacement(MaterialPageRoute(
                                  builder: (context) => ResetPasswordScreen(
                                        model: model,
                                      )));
                        },
                        child: CustomText(
                            text: T('click here'),
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                            color: AppColors.brown),
                      ),
                    ],
                  ),
                  SizedBox(
                    height: AppController.h * 0.02,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      CustomText(
                          text: T("If you have not registered before, you can"),
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: AppColors.PRIMARY_COLOR),
                      InkWell(
                        onTap: () {
                          Navigator.of(context).pushReplacement(
                              MaterialPageRoute(
                                  builder: (context) => const SignUp()));
                        },
                        child: CustomText(
                            text: T('sign up here'),
                            fontSize: 13,
                            fontWeight: FontWeight.bold,
                            color: AppColors.brown),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _launchURL(BuildContext context) async {
    const url = 'https://alderishop.com/passwordrecovery/';
    final Uri uri = Uri.parse(url);

    if (await canLaunchUrl(uri)) {
      await launchUrl(uri);
    } else {
      // Handle error
      if (context.mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: Text(T('Error')),
            content: Text('${T("Could not launch")} $url'),
            actions: <Widget>[
              TextButton(
                child: Text(T('OK')),
                onPressed: () {
                  Navigator.of(context).pop();
                },
              ),
            ],
          ),
        );
      }
    }
  }
}
