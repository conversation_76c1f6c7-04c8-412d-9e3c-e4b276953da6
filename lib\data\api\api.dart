// ignore_for_file: avoid_print

import 'package:alderishop/controllers/auth_Controller.dart';
import 'package:alderishop/data/model/response.dart';
import 'package:dio/dio.dart';

// const String baseUrl1 = 'https://shop.pal4it.org';
//const String baseUrl1 = 'https://alderishop.com';
const String baseUrl1 = 'http://192.168.1.160:45455';

class Api {
  static Dio api = Dio();

  //************************************************************************** */
  static Future<dynamic> getOne({
    required String action,
    bool useBaseUrl2 = false,
  }) async {
    try {
      String baseUrlToUse = baseUrl1;
      var url = '$baseUrlToUse/$action';
      print('this is the api url requested: $url');
      var headers = AuthController.getHeader();
      var result = await api.get(
        url,
        options: Options(headers: headers),
      );
      if (result.statusCode == 200) {
        // Success
        return result;
      } else {
        // Failed
        print('Request failed with status code: ${result.statusCode}');
        return null;
      }
    } catch (e, stackTrace) {
      if (e is DioError) {
        if (e.response != null) {
          print('Dio error response:');
          print('status: ${e.response!.statusCode}');
          print('data: ${e.response!.data}');
          print('headers: ${e.response!.headers}');
        } else {
          print('Dio request failed with error:');
          print('message: $e');
        }
      } else {
        print('Unexpected error:');
        print('message: $e');
      }
      print('Stack trace:');
      print(stackTrace);
      return null;
    }

    //************************************************************************** */
  }

  static Future<dynamic> getDynamic({
    required String action,
    bool useBaseUrl2 = false,
  }) async {
    try {
      // String baseUrlToUse = useBaseUrl2 ? baseUrl2 : baseUrl1;
      String baseUrlToUse = baseUrl1;
      var url = '$baseUrlToUse/$action';
      var result = await api.get(url);
      return result;
    } catch (e) {
      print(e);
      return null;
    }
  }

  static Future<ResponseResultModel?> getList({required String action}) async {
    try {
      var url = '$baseUrl1/$action';
      var result = await api.get(url);

      return result.data;
    } catch (e) {
      print(e);
      return null;
    }
  }

  //************************************************************************** */
  static Dio _dio = Dio(); // Create Dio instance

  static Future<ResponseResultModel?> post({
    required String action,
    dynamic body,
    bool useBaseUrl2 = false,
  }) async {
    try {
      String baseUrlToUse = baseUrl1;
      var url = '$baseUrlToUse/$action';
      var headers = AuthController.getHeader();
      var response = await _dio.post(
        url,
        data: body,
        options: Options(headers: headers),
      );

      // print('Response status code: ${response.statusCode}');
      // print('Response data: ${response.data}');

      if (response.statusCode == 200 || response.statusCode == 201) {
        return ResponseResultModel.fromJson(response.data);
      } else {
        print('Error: Request failed with status code ${response.statusCode}');
        return null;
      }
    } on DioException catch (e) {
      print('Dio Exception: ${e.message}');
      return null;
      // ignore: dead_code_on_catch_subtype
    } on DioError catch (e) {
      print('Dio Error: ${e.message}');
      return null;
    } catch (e) {
      print('Error: $e');
      return null;
    }
  }

  //************************************************************************** */
  static Future<dynamic> postList(
      {required String action, required dynamic body}) async {
    var url = '$baseUrl1/$action';
    try {
      var result = await api.post(url, data: body);
      return result.data;
    } catch (e) {
      print('Error While Post Request, $url');
      print(e);
      return null;
    }
  }

  //************************************************************************** */
  static Future<ResponseResultModel?> put(
      {required String action, bool useBaseUrl2 = false, dynamic body}) async {
    String baseUrlToUse = baseUrl1;
    // String baseUrlToUse = baseUrl1;
    var url = '$baseUrlToUse/$action';
    try {
      var result = await api.put(
        url,
        data: body,
        options: Options(headers: AuthController.getHeader()),
      );
      if ((result.statusCode ?? 0) == 200 || (result.statusCode ?? 0) < 250) {
        return ResponseResultModel(
          isSuccess: true,
          data: result.data,
        );
      } else {
        return ResponseResultModel.fromJson(result.data);
      }
    } catch (e) {
      print('Error While put Request, $url');
      return null;
    }
  }

  //************************************************************************** */
  static Future<ResponseResultModel?> delete({required String action}) async {
    var url = '$baseUrl1/$action';
    try {
      var result = await api.delete(
        url,
        options: Options(headers: AuthController.getHeader()),
      );
      if ((result.statusCode ?? 0) > 200 && (result.statusCode ?? 0) < 250) {
        return ResponseResultModel(
          isSuccess: true,
          data: result.data,
        );
      } else {
        return ResponseResultModel.fromJson(result.data);
      }
    } catch (e) {
      print('Error While delete Request, $url');
      return null;
    }
  }

  //************************************************************************** */

  // static Future<bool> refreshToken() async {
  //   final pref = await SharedPreferences.getInstance();

  //   final refreshToken = pref.getString('refreshToken');
  //   final userId = pref.getString('userId');
  //   final response = await api.post('$baseUrl1/auth/RefreshToken', data: {
  //     'userId': userId,
  //     'refreshToken': refreshToken,
  //   });

  //   var result = ResponseResultModel.fromJson(response.data);
  //   if (result.isSuccess) {
  //     Provider.of<AuthController>(navigatorKey.currentContext!, listen: false)
  //         .setToken(result.data['token']);
  //     pref.setString('refreshToken', result.data['refreshToken']);
  //     return true;
  //   } else {
  //     // refresh token is wrong
  //     //AuthController.logOut();
  //     return false;
  //   }
  // }
}
