class AddressesModel {
  int? id;
  int? userId;
  int? postalCode;
  String? street;
  String? addressName;
  String? city;
  String? state;
  String? buildingNumber;
  String? apartmentNumber;
  String? district;
 
  String? country;
  bool? isActive;

  AddressesModel(
      {this.id,
      this.userId,
      this.addressName,
      this.postalCode,
      this.street,
      this.city,
      this.state = "",
      this.buildingNumber,
      this.apartmentNumber,
      this.district,
      this.country,
      this.isActive = true});

  AddressesModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    userId = json['userId'];
    postalCode = json['postalCode'];
    addressName = json['addressName'];
    street = json['street'];
    city = json['city'];
    state = json['state'];
    buildingNumber = json['buildingNumber'];
    apartmentNumber = json['apartmentNumber'];
    district = json['district'];
    country = json['country'];
    isActive = json['isActive'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['userId'] = this.userId;
    data['postalCode'] = this.postalCode;
    data['street'] = this.street;
    data['addressName'] = this.addressName;
    data['city'] = this.city;
    data['state'] = this.state;
    data['buildingNumber'] = this.buildingNumber;
    data['apartmentNumber'] = this.apartmentNumber;
    data['district'] = this.district;
    data['country'] = this.country;
    data['isActive'] = this.isActive;
    return data;
  }
}
