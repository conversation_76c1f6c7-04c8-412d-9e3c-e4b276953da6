class ProductSearchFilter {
  int? categoryId;
  int? pageNumber;
  int? pageSize;
  String? search;
  SortEnum? sort;
  List<AttrFilter>? attrFilter = [];

  ProductSearchFilter(
      {this.categoryId,
      this.pageNumber,
      this.pageSize,
      this.search,
      this.sort,
      this.attrFilter});

  ProductSearchFilter.fromJson(Map<String, dynamic> json) {
    categoryId = json['categoryId'];
    pageNumber = json['pageNumber'];
    pageSize = json['pageSize'];
    search = json['search'];

    if (json['attrFilter'] != null) {
      attrFilter = <AttrFilter>[];
      json['attrFilter'].forEach((v) {
        attrFilter!.add(new AttrFilter.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['categoryId'] = categoryId;
    data['pageNumber'] = pageNumber;
    data['pageSize'] = pageSize;
    data['search'] = search;
    data['sort'] = sort?.index;

    if (attrFilter != null) {
      data['attrFilter'] = attrFilter!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class AttrFilter {
  String? name;
  String? value;
  int? optionId;

  AttrFilter({this.optionId, this.name, this.value});

  AttrFilter.fromJson(Map<String, dynamic> json) {
    name = json['name'];
    value = json['value'];
    optionId = json['optionId'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['name'] = name;
    data['value'] = value;
    data['optionId'] = optionId;

    return data;
  }
}

enum SortEnum {
  hightPriceToLow,
  lowPriceToHigh,
  oldest,
  newest,
  popularProduct,
}
