import 'package:alderishop/constants/constants.dart';
import 'package:alderishop/controllers/app_controller.dart';
import 'package:alderishop/data/model/categories/categroy_model.dart';

import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';

class SearchFilterCategoriesCheckBoxesWidget extends StatefulWidget {
  final List<CategoryModel> options;
  final Function(CategoryModel?, bool) onChange;

  const SearchFilterCategoriesCheckBoxesWidget({
    Key? key,
    required this.options,
    required this.onChange,
  }) : super(key: key);

  @override
  State<SearchFilterCategoriesCheckBoxesWidget> createState() =>
      _SearchFilterCategoriesCheckBoxesWidgetState();
}

class _SearchFilterCategoriesCheckBoxesWidgetState
    extends State<SearchFilterCategoriesCheckBoxesWidget> {
  late Map<int?, bool> values;

  @override
  void initState() {
    super.initState();
    // values = {for (var option in widget.options) option.id: false};
    // var initialValue = Provider.of<ProductsController>(context, listen: false)
    //     .searchFilter
    //     .selectedCateogries;
    // for (var initia in initialValue) {
    //   if (values.containsKey(initia.id)) {
    //     values[initia.id] = true;
    //   }
    // }
  }

  @override
  Widget build(BuildContext context) {
    return AlignedGridView.count(
      crossAxisCount: AppController.W > 600 ? 3 : 2,
      mainAxisSpacing: 8,
      crossAxisSpacing: 5,
      physics: const NeverScrollableScrollPhysics(),
      scrollDirection: Axis.vertical,
      shrinkWrap: true,
      itemCount: widget.options.length,
      itemBuilder: (BuildContext context, int index) {
        return Transform.scale(
          scale: 1,
          child: Container(
            padding: EdgeInsets.zero,
            decoration: BoxDecoration(
              color: AppColors.Theard_COLOR.withOpacity(0.15),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Transform.scale(
              scale: 1,
              child: CheckboxListTile(
                controlAffinity: ListTileControlAffinity.leading,
                contentPadding: const EdgeInsets.all(0),
                activeColor: AppColors.Theard_COLOR,
                materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                visualDensity: VisualDensity(horizontal: -4.0),
                title: Text(
                  widget.options[index].name ?? "",
                  style: TextStyle(
                      fontSize: 12,
                      color: AppColors.BLACK_COLOR,
                      fontWeight: FontWeight.w400),
                ),
                value: values[widget.options[index].id],
                onChanged: (bool? value) {
                  setState(() {
                    values[widget.options[index].id] = value ?? false;
                    widget.onChange(widget.options[index], value ?? false);
                  });
                },
              ),
            ),
          ),
        );
      },
    );
  }
}
