import 'package:alderishop/services/helper.dart';
import 'package:flutter/material.dart';
import 'package:alderishop/constants/constants.dart';
import 'package:table_calendar/table_calendar.dart';

// ignore: must_be_immutable
class CalendarPage extends StatefulWidget {
  final Function(DateTime) onDateSelected;

  CalendarPage({Key? key, required this.onDateSelected}) : super(key: key);

  @override
  _CalendarPageState createState() => _CalendarPageState();
}

class _CalendarPageState extends State<CalendarPage> {
  CalendarFormat _calendarFormat = CalendarFormat.month;
  DateTime _focusedDay = DateTime.now();
  DateTime? _selectedDay;
  final TextEditingController _yearController = TextEditingController();

  void _showYearPickerDialog() {
    _yearController.text = _focusedDay.year.toString();
    showDialog(
      context: context,
      builder: (context) => Dialog(
        insetPadding: const EdgeInsets.all(100),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10.0),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _yearController,
                    keyboardType: TextInputType.number,
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  child: Text(T('Cancel')),
                ),
                TextButton(
                  onPressed: () {
                    setState(() {
                      _focusedDay = DateTime(
                        int.tryParse(_yearController.text) ?? _focusedDay.year,
                        _focusedDay.month,
                        _focusedDay.day,
                      );
                    });
                    widget.onDateSelected(_focusedDay);
                    Navigator.of(context).pop();
                  },
                  child: Text(T('OK')),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Center(
          child: Container(
            width: 280,
            height: 300,
            padding: const EdgeInsets.all(5),
            decoration: BoxDecoration(
              color: AppColors.WHITE_COLOR,
              borderRadius: BorderRadius.circular(5),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.5),
                  spreadRadius: 2,
                  blurRadius: 5,
                  offset: const Offset(0, 3),
                ),
              ],
            ),
            child: TableCalendar(
              rowHeight: 35,
              firstDay: DateTime.utc(1900, 1, 1),
              lastDay: DateTime.utc(2030, 12, 31),
              focusedDay: _focusedDay,
              calendarFormat: _calendarFormat,
              selectedDayPredicate: (day) {
                return isSameDay(_selectedDay, day);
              },
              onDaySelected: (selectedDay, focusedDay) {
                setState(() {
                  _selectedDay = selectedDay;
                  _focusedDay = focusedDay;
                  widget.onDateSelected(selectedDay);
                });
              },
              onFormatChanged: (format) {
                setState(() {
                  _calendarFormat = format;
                });
              },
              onHeaderTapped: (date) {
                _showYearPickerDialog();
              },
              headerStyle: const HeaderStyle(
                formatButtonVisible: false,
                titleCentered: true,
              ),
              daysOfWeekStyle: const DaysOfWeekStyle(
                weekdayStyle: TextStyle(fontSize: 10),
                weekendStyle: TextStyle(fontSize: 10),
              ),
              availableCalendarFormats: const {
                CalendarFormat.month: 'Month',
                CalendarFormat.week: 'Week',
              },
            ),
          ),
        ),
      ],
    );
  }
}
