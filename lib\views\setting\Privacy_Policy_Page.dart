import 'package:alderishop/components/layout.dart';
import 'package:alderishop/controllers/configration_controller.dart';

import 'package:alderishop/services/helper.dart';
import 'package:alderishop/views/catalog/widgets/headerWidget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:provider/provider.dart';

class PrivacyPolicyPage extends StatefulWidget {
  const PrivacyPolicyPage({super.key});

  @override
  State<PrivacyPolicyPage> createState() => _PrivacyPolicyPageState();
}

class _PrivacyPolicyPageState extends State<PrivacyPolicyPage> {
  @override
  Widget build(BuildContext context) {
    final data = Provider.of<ConfigrationController>(context, listen: false);

    return ApplicationLayout(
      selectedBottomNavbarItem: BottomNavbarItems.none,
      content: Column(
        children: [
          HeaderWidget(
            onTap: () {
              if (Navigator.of(context).canPop()) {
                Navigator.of(context).pop();
              }
            },
            text: T("Privacy Policy"),
          ),
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: data.privacyPolicy == null
                  ? const Center(child: CircularProgressIndicator())
                  : Html(
                      data: data.privacyPolicy!.body?.value,
                    ),
            ),
          ),
        ],
      ),
    );
  }
}
