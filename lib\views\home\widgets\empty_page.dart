import 'package:alderishop/components/layout.dart';
import 'package:alderishop/constants/constants.dart';
import 'package:alderishop/controllers/app_controller.dart';
import 'package:alderishop/data/model/categories/categroy_model.dart';
import 'package:alderishop/views/catalog/widgets/filterWidget.dart';
import 'package:alderishop/views/home/<USER>/SearchPage.dart';
import 'package:alderishop/views/home/<USER>/home_header.dart';
import 'package:alderishop/views/home/<USER>/home_imag_slider.dart';
import 'package:flutter/material.dart';

class NoDataPage extends StatefulWidget {
  const NoDataPage({super.key});

  @override
  State<NoDataPage> createState() => _NoDataPageState();
}

class _NoDataPageState extends State<NoDataPage> {
  final sortOptions = [
    SortOption(
      value: 'az',
      label: 'الاسم من A إلى Z',
      icon: Icons.sort_by_alpha,
      iconColor: AppColors.PRIMARY_COLOR,
    ),
    SortOption(
      value: 'za',
      label: 'الاسم من Z إلى A',
      icon: Icons.sort_by_alpha,
      iconColor: AppColors.PRIMARY_COLOR,
    ),
  ];
  bool isGrid = true;
  String _sortBy = 'none';
  void _applySort(List<CategoryModel> products) {
    if (_sortBy == 'az') {
      products.sort((a, b) => (a.name ?? '').compareTo(b.name ?? ''));
    } else if (_sortBy == 'za') {
      products.sort((a, b) => (b.name ?? '').compareTo(a.name ?? ''));
    }
  }

  @override
  Widget build(BuildContext context) {
    return ApplicationLayout(
      content: Column(
        children: [
          SizedBox(
            height: AppController.h * 0.015,
          ),
          //-------------------------------Search--------------------------
          Row(
            children: [
              // Compact back button
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 14),
                decoration: BoxDecoration(
                  color: AppColors.PRIMARY_COLOR.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    onTap: () {
                      Navigator.of(context).pop();
                    },
                    borderRadius: BorderRadius.circular(8),
                    child: Container(
                      padding: const EdgeInsets.all(8),
                      child: Icon(
                        AppController.currentLangId == 2
                            ? Icons.arrow_forward_ios
                            : Icons.arrow_back_ios,
                        color: AppColors.PRIMARY_COLOR,
                        size: 18,
                      ),
                    ),
                  ),
                ),
              ),
              // Expanded search header to take remaining space
              const Expanded(
                child: HomeHeader(
                  disPlayFilter: false,
                ),
              ),
              const SizedBox(width: 12),
            ],
          ),
          //-------------------------------SliderImage--------------------------
          SizedBox(
            height: AppController.h * 0.03,
          ),
          const HomeBanner(
            autoPlay: true,
          ),
          SizedBox(
            height: AppController.h * 0.015,
          ),
          TopFilterBar(
            isGrid: isGrid,
            onToggleView: () {
              setState(() {
                isGrid = !isGrid;
              });
            },
            onSort: (String value) {
              setState(() {
                _sortBy = value; // update sort type
              });
            },
            onFilter: () {
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => const SearchPage()),
              );
            },
            sortOptions: sortOptions,
          ),
          SizedBox(
            height: 20,
          ),
          //-------------------------------Subcategories--------------------------
          const Text(
            "لايوجد منتجات",
            style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
            textAlign: TextAlign.center,
          ),
        ],
      ),
      selectedBottomNavbarItem: BottomNavbarItems.home,
    );
  }
}
