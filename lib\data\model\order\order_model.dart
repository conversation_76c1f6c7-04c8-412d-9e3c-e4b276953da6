// ignore: constant_identifier_names

import '../addresses_model.dart';

enum PaymentType { OnDoor, ElectronicPayment }

enum OrderStatus {
  PaymentPending,
  Pending,
  Confirmed,
  Preparing,
  OutForDelivery,
  Delivered,
  Canceled,
  Returned,
  Refunded,
  Failed
}

enum OrderApproveStatus {
  Unknown,
  Approved,
  DisApproved,
  CanceledByCustomer,
}

// ignore: constant_identifier_names
enum DeliveryType { FromPlace, ToAddress }

class UserOrderModel {
  int? totalOrders;
  double? totalPrice;
  List<Orders>? orders;

  UserOrderModel({this.totalOrders, this.totalPrice, this.orders});

  UserOrderModel.fromJson(Map<String, dynamic> json) {
    totalOrders = json['totalOrders'];
    totalPrice = json['totalPrice'];
    if (json['orders'] != null) {
      orders = <Orders>[];
      json['orders'].forEach((v) {
        orders!.add(new Orders.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['totalOrders'] = this.totalOrders;
    data['totalPrice'] = this.totalPrice;
    if (this.orders != null) {
      data['orders'] = this.orders!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Orders {
  int? id;
  int? customerId;
  String? customerName;
  int? customerPhoneNumber;
  String? customerEmail;
  int? approvedById;
  String? approvedByName;
  int? addressId;
  AddressesModel? address;
  String? orderCode;
  int? deliveryType;
  int? paymentType;
  int? orderApproveStatus;
  int? orderStatus;
  String? invoiceRefrenceCodeERP;
  String? invoiceRefrenceIdERP;
  double? total;
  double? totalDiscount;
  String? customerNote;
  int? couponId;
  DateTime? date;
  bool? isWholesaleOrder;
  bool? isComplated;
  List<OrderItem>? orderItem;
  List<OrderStatusHistory>? orderStatusHistory;
  String? createdBy;
  String? updatedBy;
  String? createdAt;
  String? updatedAt;
  bool? isActive;
  bool? isDeleted;

  Orders(
      {this.id,
      this.customerId,
      this.customerName,
      this.customerPhoneNumber,
      this.customerEmail,
      this.approvedById,
      this.approvedByName,
      this.addressId,
      this.address,
      this.orderCode,
      this.deliveryType,
      this.paymentType,
      this.orderApproveStatus,
      this.orderStatus,
      this.invoiceRefrenceCodeERP,
      this.invoiceRefrenceIdERP,
      this.total,
      this.totalDiscount,
      this.customerNote,
      this.couponId,
      this.date,
      this.isWholesaleOrder,
      this.isComplated,
      this.orderItem,
      this.orderStatusHistory,
      this.createdBy,
      this.updatedBy,
      this.createdAt,
      this.updatedAt,
      this.isActive,
      this.isDeleted});

  Orders.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    customerId = json['customerId'];
    customerName = json['customerName'];
    customerPhoneNumber = json['customerPhoneNumber'];
    customerEmail = json['customerEmail'];
    approvedById = json['approvedById'];
    approvedByName = json['approvedByName'];
    addressId = json['addressId'];
    address = json['address'];
    orderCode = json['orderCode'];
    deliveryType = json['deliveryType'];
    paymentType = json['paymentType'];
    orderApproveStatus = json['orderApproveStatus'];
    orderStatus = json['orderStatus'];
    invoiceRefrenceCodeERP = json['invoiceRefrenceCodeERP'];
    invoiceRefrenceIdERP = json['invoiceRefrenceIdERP'];
    total = json['total'];
    totalDiscount = json['totalDiscount'];
    customerNote = json['customerNote'];
    couponId = json['couponId'];
    date = json['date'] != null
        ? DateTime.parse(json['date'].toString())
        : DateTime.now();
    isWholesaleOrder = json['isWholesaleOrder'];
    isComplated = json['isComplated'];
    if (json['orderItem'] != null) {
      orderItem = <OrderItem>[];
      json['orderItem'].forEach((v) {
        orderItem!.add(new OrderItem.fromJson(v));
      });
    }
    if (json['orderStatusHistory'] != null) {
      orderStatusHistory = <OrderStatusHistory>[];
      json['orderStatusHistory'].forEach((v) {
        orderStatusHistory!.add(new OrderStatusHistory.fromJson(v));
      });
    }
    createdBy = json['createdBy'];
    updatedBy = json['updatedBy'];
    createdAt = json['createdAt'];
    updatedAt = json['updatedAt'];
    isActive = json['isActive'];
    isDeleted = json['isDeleted'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['customerId'] = this.customerId;
    data['customerName'] = this.customerName;
    data['customerPhoneNumber'] = this.customerPhoneNumber;
    data['customerEmail'] = this.customerEmail;
    data['approvedById'] = this.approvedById;
    data['approvedByName'] = this.approvedByName;
    data['addressId'] = this.addressId;
    data['address'] = this.address;
    data['orderCode'] = this.orderCode;
    data['deliveryType'] = this.deliveryType;
    data['paymentType'] = this.paymentType;
    data['orderApproveStatus'] = this.orderApproveStatus;
    data['orderStatus'] = this.orderStatus;
    data['invoiceRefrenceCodeERP'] = this.invoiceRefrenceCodeERP;
    data['invoiceRefrenceIdERP'] = this.invoiceRefrenceIdERP;
    data['total'] = this.total;
    data['totalDiscount'] = this.totalDiscount;
    data['customerNote'] = this.customerNote;
    data['couponId'] = this.couponId;
    data['date'] = this.date?.toIso8601String();
    data['isWholesaleOrder'] = this.isWholesaleOrder;
    data['isComplated'] = this.isComplated;
    if (this.orderItem != null) {
      data['orderItem'] = this.orderItem!.map((v) => v.toJson()).toList();
    }
    if (this.orderStatusHistory != null) {
      data['orderStatusHistory'] =
          this.orderStatusHistory!.map((v) => v.toJson()).toList();
    }
    data['createdBy'] = this.createdBy;
    data['updatedBy'] = this.updatedBy;
    data['createdAt'] = this.createdAt;
    data['updatedAt'] = this.updatedAt;
    data['isActive'] = this.isActive;
    data['isDeleted'] = this.isDeleted;
    return data;
  }
}

class OrderItem {
  int? id;
  int? orderId;
  int? productId;
  String? productName;
  int? quantity;
  double? productPrice;
  int? productCombinationId;
  String? productCombinationName;
  String? productImage;
  List<int>? selectedProductAttributeOptionIds;

  OrderItem(
      {this.id,
      this.orderId,
      this.productId,
      this.productName,
      this.quantity,
      this.productPrice,
      this.productCombinationId,
      this.productCombinationName,
      this.productImage,
      this.selectedProductAttributeOptionIds});

  OrderItem.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    orderId = json['orderId'];
    productId = json['productId'];
    productName = json['productName'];
    quantity = json['quantity'];
    productPrice = json['productPrice'];
    productCombinationId = json['productCombinationId'];
    productCombinationName = json['productCombinationName'];
    productImage = json['productImage'];
    selectedProductAttributeOptionIds =
        List<int>.from(json['selectedProductAttributeOptionIds'] ?? []); //
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['orderId'] = this.orderId;
    data['productId'] = this.productId;
    data['productName'] = this.productName;
    data['quantity'] = this.quantity;
    data['productPrice'] = this.productPrice;
    data['productCombinationId'] = this.productCombinationId;
    data['productCombinationName'] = this.productCombinationName;
    data['productImage'] = this.productImage;
    data['selectedProductAttributeOptionIds'] =
        this.selectedProductAttributeOptionIds;
    return data;
  }
}

class OrderStatusHistory {
  int? orderId;
  int? orderStatus;
  String? returnInvoiceERPCode;
  String? createdAt;
  Orders? order;

  OrderStatusHistory(
      {this.orderId,
      this.orderStatus,
      this.returnInvoiceERPCode,
      this.createdAt,
      this.order});

  OrderStatusHistory.fromJson(Map<String, dynamic> json) {
    orderId = json['orderId'];
    orderStatus = json['orderStatus'];
    returnInvoiceERPCode = json['returnInvoiceERPCode'];
    createdAt = json['createdAt'];
    order = json['orders'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['orderId'] = this.orderId;
    data['orderStatus'] = this.orderStatus;
    data['returnInvoiceERPCode'] = this.returnInvoiceERPCode;
    data['createdAt'] = this.createdAt;
    data['orders'] = this.order;
    return data;
  }
}
