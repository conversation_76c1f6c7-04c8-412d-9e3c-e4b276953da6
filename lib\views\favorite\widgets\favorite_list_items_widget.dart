import 'package:alderishop/components/common_cache_image.dart';
import 'package:alderishop/components/common_snckbar.dart';
import 'package:alderishop/controllers/cart_controller.dart';
import 'package:alderishop/controllers/favorites_controller.dart';
import 'package:alderishop/data/api/api.dart';
import 'package:alderishop/data/model/cart/cart_model.dart';
import 'package:alderishop/data/model/favorite_model.dart';
import 'package:alderishop/views/favorite/widgets/Icon_custom_Container.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../constants/constants.dart';
import '../../../controllers/app_controller.dart';

// ignore: must_be_immutable
class FavoriteListItemsWidget extends StatefulWidget {
  final favoriteModel model;
  final VoidCallback onDelete;
  const FavoriteListItemsWidget(
      {super.key, required this.onDelete, required this.model});

  @override
  State<FavoriteListItemsWidget> createState() =>
      _FavoriteListItemsWidgetState();
}
class _FavoriteListItemsWidgetState extends State<FavoriteListItemsWidget> {
  @override
  Widget build(BuildContext context) {
 
  return Stack(
    children: [
      Column(
        children: [
        Container(
            color: AppColors.WHITE_COLOR,
            width: AppController.W,
            child: CachedImage(
             height:  AppController.h * 0.24,
              fit: BoxFit.cover,
              borderRadius: BorderRadius.circular(5),
              width: double.infinity,
              imageUrl: (widget.model.itemImage?.isNotEmpty ?? false)
                  ? "$baseUrl1/${widget.model.itemImage!}"
                  : "",
            ),
          ),
        const  SizedBox(height:4,),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 12),
            child: Column(
              children: [
                Row(
                  children: [
                    Flexible(
                      child: Text(
                        widget.model.itemName ?? "",
                        overflow: TextOverflow.ellipsis,
                        style: TextStyle(
                          fontSize: 12,
                          color: AppColors.txtColor,
                          fontWeight: FontWeight.w600,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ],
                ),
                             const   SizedBox(height:4,),
                Container( 
                  padding: const EdgeInsets.symmetric(vertical: 5),
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(5),
                      color: AppColors.container_color.withOpacity(0.3)),
                  child: Center(
                    child: Text(
                      "${widget.model.itemPrice}",
                      style: TextStyle(
                        color: AppColors.BLACK_COLOR,
                        fontSize: 13,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
               
            
              ],
            ),
          )
        ],
      ),
 Positioned(
  bottom:70,
  right: 10,
  child:  InkWell(
          onTap: () async {
            await Provider.of<FavoritesController>(context,
                    listen: false)
                .removeItemFromFavorites(widget.model.id ?? 0);
            setState(() {});
          },
          child: IconCustomContainerWidget(
            color: AppColors.PRIMARY_COLOR,

            image: 'assets/img/delete.png',
          ),
        ),),
 Positioned(
  bottom:70                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           ,
  left: 10,
  child: InkWell(
          onTap: () async {
            pleaseWaitDialog(context: context, isShown: true);
    
            var result = await Provider.of<CartController>(context,
                    listen: false)
                .addItemToCart(
              CartHelperModel(
                  selectedProductAttributeOptionIds: [],
    
                  //  selectedAtter
                  //     .map(
                  //       (e) => e.id ?? 0,
                  //     )
                  //     .toList(), // Pass the full list of Options objects
                  productId: widget.model.id,
                  quantity: widget.model.itemCount),
            );
    
            // ignore: use_build_context_synchronously
            pleaseWaitDialog(context: context, isShown: false);
    
            if (result) {
              // ignore: use_build_context_synchronously
              successMsg(context: context);
            } else {
              // Show error message
              // ignore: use_build_context_synchronously
              errorMsg(context: context, title: '');
            }
          },
          child: Container(
                                height: 20,
                                width: 20,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(5),
                                  color: AppColors.PRIMARY_COLOR,
                                ),
                                child: Icon(
                                  size: 20,
                                  Icons.add,
                                  color: AppColors.WHITE_COLOR,
                                ),
                              ),
        ),)
   
    ],
  );
  }
}