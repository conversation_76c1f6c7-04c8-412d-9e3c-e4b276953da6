class ReviewModel {
  String? customerName;
  int? userId;
  int? id;
  int? productId;
  String? productName;
  String? createdAt;
  int? rate;
  String? comment;
  bool? isActive;
  bool? isDeleted;

  ReviewModel(
      {this.customerName,
      this.userId,
      this.id,
      this.productId,
      this.productName,
      this.createdAt,
      this.rate,
      this.comment,
      this.isActive,
      this.isDeleted});

  ReviewModel.fromJson(Map<String, dynamic> json) {
    customerName = json['customerName'];
    userId = json['userId'];
    id = json['id'];
    productId = json['productId'];
    productName = json['productName'];
    createdAt = json['createdAt'];
    rate = json['rate'];
    comment = json['comment'];
    isActive = json['isActive'];
    isDeleted = json['isDeleted'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['customerName'] = this.customerName;
    data['userId'] = this.userId;
    data['id'] = this.id;
    data['productId'] = this.productId;
    data['productName'] = this.productName;
    data['createdAt'] = this.createdAt;
    data['rate'] = this.rate;
    data['comment'] = this.comment;
    data['isActive'] = this.isActive;
    data['isDeleted'] = this.isDeleted;
    return data;
  }
}

class CreateReview {
  int? productId;
  int? rate;
  String? comment;

  CreateReview({this.productId, this.rate, this.comment});

  CreateReview.fromJson(Map<String, dynamic> json) {
    productId = json['ProductId'];
    rate = json['Rate'];
    comment = json['Comment'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['ProductId'] = this.productId;
    data['Rate'] = this.rate;
    data['Comment'] = this.comment;
    return data;
  }
}
