import 'package:alderishop/components/layout.dart';
import 'package:alderishop/constants/constants.dart';
import 'package:alderishop/controllers/app_controller.dart';
import "package:alderishop/controllers/products_controller.dart";
import 'package:alderishop/controllers/categores_controller.dart';
import 'package:alderishop/data/model/Products/productModel/productListModel.dart';
import 'package:alderishop/data/model/categories/categroy_model.dart';
import 'package:alderishop/services/helper.dart';
import 'package:alderishop/views/categories/items/items.dart';
import 'package:alderishop/views/categories/items/items_Details.dart';
import 'package:alderishop/views/categories/items/widgets/filterWidget.dart';
import 'package:alderishop/views/home/<USER>/SearchPage.dart';
import 'package:alderishop/views/home/<USER>/custom_categories_item.dart';
import 'package:alderishop/views/home/<USER>/home_header.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

class CategoryItem extends StatefulWidget {
  final CategoryModel category;

  const CategoryItem({
    super.key,
    required this.category,
  });

  @override
  State<CategoryItem> createState() => _CategoryItemState();
}

class _CategoryItemState extends State<CategoryItem>
    with TickerProviderStateMixin {
  AnimationController? animationController;
  final ScrollController _scrollController = ScrollController();
  final RefreshController _refreshController =
      RefreshController(initialRefresh: false);
  final sortOptions = [
    SortOption(
      value: 'price',
      label: 'السعر من الأقل إلى الأعلى',
      icon: Icons.arrow_upward,
      iconColor: AppColors.PRIMARY_COLOR,
    ),
    SortOption(
      value: 'price_desc',
      label: 'السعر من الأعلى إلى الأقل',
      icon: Icons.arrow_downward,
      iconColor: AppColors.PRIMARY_COLOR,
    ),
    SortOption(
      value: 'rating',
      label: "الترتيب حسب التقييم",
      icon: Icons.star,
      iconColor: AppColors.brown,
    ),
  ];
  // ignore: unused_field
  bool _isLoading = true;
  @override
  void initState() {
    animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
 // Always refresh products on first category screen load
  WidgetsBinding.instance.addPostFrameCallback((_) {
    _onRefresh();
  });
    super.initState();
  }

  String _sortBy = 'none'; // Options: 'none', 'price', 'rating'

  @override
  void dispose() {
    animationController?.dispose();
    _scrollController.dispose();
    _refreshController.dispose();
    super.dispose();
  }

  void _applySort(List<ProductListModel> products) {
    if (_sortBy == 'price') {
      products.sort((a, b) => (a.price ?? 0).compareTo(b.price ?? 0));
    } else if (_sortBy == 'price_desc') {
      products.sort((a, b) => (b.price ?? 0).compareTo(a.price ?? 0));
    }
  }

  void _onRefresh() async {
 setState(() {
  _isLoading = true; // triggers showing CircularProgressIndicator
});
   await Provider.of<ProductsController>(context, listen: false)
    .getProductCategories(
      categoryId: widget.category.id ?? 0,
      
    );


    // Apply sort after loading
    _applySort(
        Provider.of<ProductsController>(context, listen: false).productData);

    _refreshController.refreshCompleted();
    setState(() {
      _isLoading = false;
    });
  }

  bool isGrid = true;

  void _onLoading() async {
    await Provider.of<ProductsController>(context, listen: false)
        .getProductCategories(
      categoryId: widget.category.id ?? 0
    );
    if (mounted) {
      setState(() {});
    }
    _refreshController.loadComplete();
  }

  @override
  Widget build(BuildContext context) {
    final dataProvider = Provider.of<ProductsController>(context);
    final itemList = dataProvider.productData;
final isLoading = dataProvider.isLoading;
    return PopScope(
      onPopInvoked: (didPop) async {
        // When going back from products to subcategories, restore the previous subcategories
        if (didPop) {
          final controller =
              Provider.of<CategoryController>(context, listen: false);
          controller.popSubCategories();
        }
      },
      child: ApplicationLayout(
        content: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 15),
          child: Column(
            children: <Widget>[
              Expanded(
                child: NestedScrollView(
                  controller: _scrollController,
                  headerSliverBuilder:
                      (BuildContext context, bool innerBoxIsScrolled) {
                    return <Widget>[
                      SliverList(
                        delegate: SliverChildBuilderDelegate(
                          (BuildContext context, int index) {
                            return Column(
                              children: <Widget>[
                                //-------------------------------Search--------------------------
                                           Row(
                children: [
                  // Compact back button
                  Container(
                    margin: const EdgeInsets.symmetric(horizontal: 16),
                    decoration: BoxDecoration(
                      color: AppColors.PRIMARY_COLOR.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Material(
                      color: Colors.transparent,
                      child: InkWell(
                        onTap: () {
                          Navigator.of(context).pop();
                        },
                        borderRadius: BorderRadius.circular(8),
                        child: Container(
                          padding: const EdgeInsets.all(8),
                          child: Icon(
                            AppController.currentLangId == 2
                                ? Icons.arrow_forward_ios
                                : Icons.arrow_back_ios,
                            color: AppColors.PRIMARY_COLOR,
                            size: 18,
                          ),
                        ),
                      ),
                    ),
                  ),
                  // Expanded search header to take remaining space
                  const Expanded(
                    child: HomeHeader(
                      disPlayFilter: false,
                    ),
                  ),
                  const SizedBox(width: 12),
                ],
              ),
                              ],
                            );
                          },
                          childCount: 1,
                        ),
                      ),
                      SliverPersistentHeader(
                        pinned: false,
                        floating: true,
                        delegate: ContestTabHeader(
                          Padding(
                            padding: const EdgeInsets.symmetric(
                                vertical: 5, horizontal: 5),
                            child: TopFilterBar(
                              isGrid: isGrid,
                              onToggleView: () {
                                setState(() {
                                  isGrid = !isGrid;
                                });
                              },
                              onSort: (String value) {
                                setState(() {
                                  _sortBy = value; // update sort type
                                  _applySort(itemList); // apply sort
                                });
                              },
                              onFilter: () {
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) => const SearchPage()),
                                );
                              },
                              sortOptions: sortOptions,
                            ),
                          ),
                        ),
                      ),
                    ];
                  },
                  body: SmartRefresher(
                          enablePullDown: true,
                          enablePullUp: true,
                          header: const WaterDropHeader(),
                        footer: CustomFooter(
  builder: (context, mode) {
    Widget body;
    if (mode == LoadStatus.idle) {
      body = Text(T("Pull up to load"));
    } else if (mode == LoadStatus.failed) {
      body = Text(T("Load Failed! Click retry!"));
    } else if (mode == LoadStatus.loading) {
      body = Text(T("Please wait"));
    } else if (mode == LoadStatus.canLoading) {
      body = Center(
        child: SizedBox(
          width: 24,
          height: 24,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            color: AppColors.PRIMARY_COLOR,
          ),
        ),
      );
    } else {
      body = Text(T("No more data"));
    }
    return SizedBox(
      height: 55.0,
      child: Center(child: body),
    );
  },
),

                          controller: _refreshController,
                          onRefresh: _onRefresh,
                          onLoading: _onLoading,
                          child:itemList.isEmpty && isLoading
      ? const Center(child: CircularProgressIndicator())
      : AlignedGridView.count(
                            crossAxisCount:
                                isGrid ? (AppController.W > 700 ? 3 : 2) : 1,
                            mainAxisSpacing: 12,
                            crossAxisSpacing: 0,
                            physics: const NeverScrollableScrollPhysics(),
                            scrollDirection: Axis.vertical,
                            shrinkWrap: true,
                            itemCount: itemList.length,
                            itemBuilder: (BuildContext context, int index) {
                              final int count = itemList.length;

                              Tween<double>(begin: 0.0, end: 1.0).animate(
                                CurvedAnimation(
                                  parent: animationController!,
                                  curve: Interval((1 / count) * index, 1.0,
                                      curve: Curves.fastOutSlowIn),
                                ),
                              );
                              animationController?.forward();

                              return InkWell(
                                onTap: () {
                                  Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                      builder: (context) => ItemDetails(
                                        item: itemList[index].id,
                                      ),
                                    ),
                                  );
                                },
                                child: CustomCategoryItem(
                                  data: itemList.isNotEmpty == true
                                      ? itemList[index]
                                      : null,
                                  animationController: animationController,
                                  isGrid: isGrid,
                                ),
                              );
                            },
                          )),
                ),
              )
            ],
          ),
        ),
        selectedBottomNavbarItem: BottomNavbarItems.home,
      ),
    );
  }
}
