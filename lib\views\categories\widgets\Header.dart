import 'package:alderishop/views/home/<USER>';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:page_transition/page_transition.dart';
import '../../../constants/constants.dart';
import '../../../controllers/app_controller.dart';
import '../../home/<USER>/CustomText.dart';

class CategoriesHeader extends StatelessWidget {
  final String text;
  final Function()? onTap;
  const CategoriesHeader({super.key, required this.text, this.onTap});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 45,
     
decoration: BoxDecoration(
  color: AppColors.WHITE_COLOR,
  boxShadow: [
    BoxShadow(
      color: Colors.black.withOpacity(0.2), // shadow color with opacity
      spreadRadius: 1, // how much the shadow spreads
      blurRadius:1, // softness of the shadow
   // shadow position: horizontal and vertical offset
    ),
  ],
),

      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 25),
        child: Row(
          children: [
            InkWell(
              onTap: onTap ??
                  () {
                    Navigator.push(
                        context,
                        PageTransition(
                          type: AppController.currentLangId == 2
                              ? PageTransitionType.rightToLeft
                              : PageTransitionType.leftToRight,
                          child: const HomePage(),
                        ));
                  },
              child: FaIcon(
                AppController.currentLangId == 2
                    ? FontAwesomeIcons.arrowRight
                    : FontAwesomeIcons.arrowLeft,
                color: AppColors.BLACK_COLOR,
                size: 18,
              ),
            ),
            SizedBox(
              width: AppController.W * 0.05,
            ),
            CustomText(
                text: text,
                fontSize: 12.sp,
                fontWeight: FontWeight.w700,
                color: AppColors.BLACK_COLOR)
          ],
        ),
      ),
    );
  }
}

// widget.itemsList.title,