class ResponseResultModel {
  int statusCode = 0;
  bool isSuccess = false;
  List<dynamic>? errors;
  dynamic data;

  ResponseResultModel({
    this.data,
    this.errors,
    required this.isSuccess,
  });

  ResponseResultModel.fromJson(Map<dynamic, dynamic> json)
      : statusCode = json['statusCode'],
        isSuccess = json['isSuccess'],
        errors = json['errors'],
        data = json['data'];
}

class ResponseResultModelSync {
  dynamic result;
  int count = 0;

  ResponseResultModelSync({this.result, this.count = 0});

  ResponseResultModelSync.fromJson(Map<dynamic, dynamic> json)
      : count = json['count'],
        result = json['result'];
}

class PaginationHelperModel {
  int? take = 10;
  int? skip;

  PaginationHelperModel({
    this.take,
    this.skip,
  });
}
