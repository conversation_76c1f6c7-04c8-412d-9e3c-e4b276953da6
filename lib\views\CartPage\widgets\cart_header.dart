import 'package:alderishop/constants/constants.dart';
import 'package:alderishop/controllers/app_controller.dart';
import 'package:alderishop/views/home/<USER>/CustomText.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

// ignore: must_be_immutable
class CartHeader extends StatefulWidget {
  final String text;

  CartHeader({
    super.key,
    required this.text,
  });

  @override
  State<CartHeader> createState() => _CartHeaderState();
}

class _CartHeaderState extends State<CartHeader> {
  @override
  Widget build(BuildContext context) {
    return Container(
      height: 40.h,
      color: AppColors.WHITE_COLOR,
      child: Padding(
        padding: EdgeInsets.only(right: 25.w, left: 25.w),
        child: Row(
          children: [
            InkWell(
              onTap: () {
                Navigator.of(context).pop();
              },
              child: FaIcon(
                AppController.currentLangId == 2
                    ? FontAwesomeIcons.arrowRight
                    : FontAwesomeIcons.arrowLeft,
                color: AppColors.PRIMARY_COLOR,
                size: 18,
              ),
            ),
            const SizedBox(width: 15),
            CustomText(
                text: widget.text,
                fontSize: 12.sp,
                fontWeight: FontWeight.w700,
                color: AppColors.PRIMARY_COLOR)
          ],
        ),
      ),
    );
  }
}
