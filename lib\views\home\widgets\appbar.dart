import 'package:alderishop/constants/constants.dart';
import 'package:alderishop/controllers/app_controller.dart';
import 'package:alderishop/views/notfication/notfication_page.dart';
import 'package:alderishop/views/profile/profile.dart';
import 'package:alderishop/views/auth/sign_in.dart';
import 'package:alderishop/views/home/<USER>';
import 'package:flutter/material.dart';
import 'package:page_transition/page_transition.dart';

class MyAppBar extends StatelessWidget implements PreferredSizeWidget {
  const MyAppBar({
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return  AppBar(
        backgroundColor: Colors.grey.shade100,
elevation: 8,
shadowColor: Colors.black.withOpacity(0.1),

      iconTheme:  IconThemeData(color: AppColors.PRIMARY_COLOR),
      title: Row(
    mainAxisAlignment: MainAxisAlignment.spaceBetween,
    children: [
      const Icon(Icons.add, color: Colors.transparent),
      GestureDetector(
        onTap: () {
          Navigator.of(context).pushReplacement(
            MaterialPageRoute(builder: (_) => const HomePage()),
          );
        },
        child: SizedBox(
          height: 35,
          child: Image.asset('assets/img/new/logo.png'),
        ),
      ),
      Row(
        children: [
          InkWell(
            onTap: () {
              if (AppController.isAuth) {
                // Navigate to chat
              }
            },
            child: SizedBox(
              height: 30,
              child: Image.asset(
                'assets/img/new/chat.png',
                color: AppColors.PRIMARY_COLOR,
              ),
            ),
          ),
          const SizedBox(width: 10),
          InkWell(
            onTap: () {
              Navigator.push(
                context,
                PageTransition(
                  type: AppController.currentLangId == 2
                      ? PageTransitionType.leftToRight
                      : PageTransitionType.rightToLeftWithFade,
                  child: AppController.isAuth
                      ? NotificationPage()
                      : const SignInPage(),
                ),
              );
            },
            child: SizedBox(
              height: 35,
              child: Image.asset(
                'assets/img/new/notifaction.png',
                color: AppColors.PRIMARY_COLOR,
              ),
            ),
          ),
          const SizedBox(width: 10),
          InkWell(
            onTap: () {
              Navigator.push(
                context,
                PageTransition(
                  type: AppController.currentLangId == 2
                      ? PageTransitionType.leftToRight
                      : PageTransitionType.rightToLeftWithFade,
                  child: AppController.isAuth
                      ? const ProfileScreen()
                      : const SignInPage(),
                ),
              );
            },
            child: Image.asset(
              'assets/img/new/User.png',
              color: AppColors.PRIMARY_COLOR,
            ),
          ),
        ],
      ),
    ],
      ),
    );

    
  }

  @override
  Size get preferredSize => const Size.fromHeight(50);
}
