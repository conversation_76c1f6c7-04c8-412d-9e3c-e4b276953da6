import 'package:alderishop/data/model/configration/configrationModel.dart';
import 'package:flutter/material.dart';

class ContainerList extends StatefulWidget {
  final List<ComboBoxDataModel> options;
  final Function(int?, bool) onChange;
  final int? initialValue;

  const ContainerList(
      {Key? key,
      required this.options,
      required this.onChange,
      this.initialValue})
      : super(key: key);

  @override
  _ContainerListState createState() => _ContainerListState();
}

class _ContainerListState extends State<ContainerList> {
  int? selectedValue;

  @override
  void initState() {
    super.initState();
    selectedValue = widget.initialValue; // Set initial value
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (BuildContext context, BoxConstraints constraints) {
        int crossAxisCount = constraints.maxWidth < 600 ? 5 : 7;

        return GridView.builder(
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: crossAxisCount,
            crossAxisSpacing: 5.0,
            mainAxisSpacing: 5.0,
            mainAxisExtent: 110,
          ),
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: widget.options.length,
          itemBuilder: (BuildContext context, int index) {
            final colorString = widget.options[index].name;
            final color = Color(int.parse(colorString.replaceAll('#', '0xFF')));

            return GestureDetector(
              onTap: () {
                setState(() {
                  selectedValue = widget.options[index].id;
                  widget.onChange(
                      selectedValue, true); // Call onChange with ID and true
                });
              },
              child: Stack(
                children: [
                  Padding(
                    padding: const EdgeInsets.symmetric(
                        vertical: 10.0, horizontal: 5),
                    child: Container(
                      height: 50,
                      width: 110,
                      color: color,
                    ),
                  ),
                  if (selectedValue == widget.options[index].id)
                    const Positioned(
                      top: 1,
                      right: -2,
                      child: Icon(
                        Icons.check_circle,
                        color: Colors.blue, // Change the color as needed
                        size: 20,
                      ),
                    ),
                ],
              ),
            );
          },
        );
      },
    );
  }
}
