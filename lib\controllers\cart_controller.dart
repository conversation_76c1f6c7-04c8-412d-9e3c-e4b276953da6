import 'dart:async';
import 'package:alderishop/data/api/api.dart';
import 'package:alderishop/data/model/cart/cart_model.dart';

import 'package:flutter/widgets.dart';

class CartController with ChangeNotifier {
  List<CartModel> cartItems = [];
  Timer? _cartUpdateTimer;
  Future<void> getCartItems() async {
    try {
      cartItems.clear();

      var url = 'api/Cart/GetCartByUserId';
      var result = await Api.getOne(
        action: url,
      );
      if (result.data != null) {
        var data = result.data["data"];

        if (data is Map<String, dynamic>) {
          var item = CartModel.fromJson(data);
          cartItems.add(item);
        } else if (data is List) {
          for (var element in data) {
            var item = CartModel.fromJson(element);
            cartItems.add(item);
          }
        } else {
          print("Unexpected type for 'data': ${data.runtimeType}");
        }
      }

      notifyListeners();

      // var sad = "";
    } catch (e) {
      print(e);

      // ignore: avoid_print
    }
  }

  //--------------------------------------------------------------------------

  Future<bool> addItemToCart(CartHelperModel model) async {
    try {
      model.cartItemId = 1;
      model.userId = 0;
      var url = 'api/Cart/AddItemToCart';
      var result = await Api.post(
        action: url,
        body: model.toJson(),
      );
      if (result != null) {
        if (result.isSuccess == true) {
          return true;
        } else {
          return false;
        }
      }
      notifyListeners();
      return false;
    } catch (e) {
      return false;
    }
  }

  //--------------------------------------------------------------------------
  Future<bool> onChangeProductQuantityInCart(
      {required CartHelperModel newquantity}) async {
    _cartUpdateTimer?.cancel();

    Completer<bool> completer = Completer<bool>();

    _cartUpdateTimer = Timer(const Duration(milliseconds: 400), () async {
      try {
        var url = 'api/Cart/UpdateItemInCart';
        var result = await Api.post(
          action: url,
          body: newquantity.toJson(),
        );

        if (result?.isSuccess == true) {
          // Refresh cart data after update
          await getCartItems(); // Call the correct method
          completer.complete(true);
        } else {
          notifyListeners();
          completer.complete(false);
        }
      } catch (e) {
        print(e);
        completer.complete(false);
      }
    });

    return completer.future;
  }

  //--------------------------------------------------------------------------

  Future<bool> removeItemFromCart(CartHelperModel model) async {
    try {
      var url = 'api/Cart/RemoveItemFromCart';
      var result = await Api.post(
        action: url,
        body: model.toJson(),
      );
      if (result != null) {
        if (result.isSuccess == true) {
          return true;
        } else {
          return false;
        }
      }
      notifyListeners();
      return false;
    } catch (e) {
      return false;
    }
  }

  //--------------------------------------------------------------------------*
  Future<bool> clearCart() async {
    try {
      var url = 'api/Cart/ClearCart';
      var result = await Api.post(
        action: url,
      );
      if (result != null) {
        cartItems.clear();
        notifyListeners();
        return true;
      }
      notifyListeners();
      return false;
    } catch (e) {
      return false;
    }
  }
}
