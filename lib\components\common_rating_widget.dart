import 'package:alderishop/data/model/Products/productModel/productListModel.dart';
import 'package:flutter/material.dart';

class CommonRatingWidget extends StatefulWidget {
  final List<ProductReview>? productReviews;
  const CommonRatingWidget({super.key, required this.productReviews});

  @override
  State<CommonRatingWidget> createState() => _CommonRatingWidgetState();
}

class _CommonRatingWidgetState extends State<CommonRatingWidget> {
  double calculateAverageRating() {
    if (widget.productReviews == null || widget.productReviews!.isEmpty) {
      return 0.0;
    }
    int totalRating = widget.productReviews!
        .map((review) => review.rating ?? 0)
        .reduce((value, element) => value + element);
    return totalRating / widget.productReviews!.length;
  }

  @override
  Widget build(BuildContext context) {
    final averageRating = calculateAverageRating();
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Row(
          children: List.generate(5, (index) {
            return Icon(
              index < averageRating ? Icons.star : Icons.star_border,
              color: Colors.amber,
              size: 16,
            );
          }),
        ),
        const SizedBox(
          width: 10,
        ),
      ],
    );
  }
}
