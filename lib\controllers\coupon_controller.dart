import 'package:alderishop/data/api/api.dart';
import 'package:alderishop/data/model/coupon_model.dart';
import 'package:alderishop/data/model/response.dart';
import 'package:flutter/widgets.dart';
import 'package:shared_preferences/shared_preferences.dart';

class CouponController with ChangeNotifier {
  List<CouponModel> myCoupons = [];
  List<CouponModel> couponsForAll = [];
  List<CouponModel> dataAfterCoupn = [];
 CouponModel? points ;
  Future<void> getMyCoupons({
    bool resetAndRefresh = true,
  }) async {
    try {
      var url = 'api/Coupon/MyCoupons';
      var result = await Api.getOne(
        action: url,
      );
      myCoupons.clear();
      if (result != null && result.data['data'] != null) {
        for (var element in result.data['data']) {
          var data = CouponModel.fromJson(element);
          myCoupons.add(data);
        }
      }

      notifyListeners();
    } catch (e) {
      // ignore: avoid_print
      print(e);
    }
  }
  //--------------------------------------------------------

  Future<void> getCouponsForAll({
    bool resetAndRefresh = true,
  }) async {
    try {
      var url = 'api/Coupon/GetCouponsForView';
      var result = await Api.getOne(
        action: url,
      );
      couponsForAll.clear();
      if (result != null && result.data['data'] != null) {
        for (var element in result.data['data']) {
          var data = CouponModel.fromJson(element);
          couponsForAll.add(data);
        }
      }

      notifyListeners();
    } catch (e) {
      // ignore: avoid_print
      print(e);
    }
  }

  //---------------------------------------------------------------------------
  Future<CouponModel?> checkCouponIsvalidate({
    bool resetAndRefresh = true,
    String? code,
  }) async {
    try {
      var url = 'api/Coupon/ValidateCoupon?couponCode=$code';
      var result = await Api.getOne(action: url);
      if (result != null && result.data != null) {
        var data = result.data['data'];
        if (data is Map<String, dynamic>) {
          var coupon = CouponModel.fromJson(data);

          return coupon;
        }
      }

      notifyListeners();
      return null;
    } catch (e) {
      return null;
    }
  }

  //---------------------------------------------------------------------------
  Future<ResponseResultModel> buyCoupon(int couponId,int couponPrice) async {
  try {
    var url = 'api/Coupon/BuyCoupon?couponId=$couponId';
    var result = await Api.post(action: url);
    if (result != null) {
      if (result.isSuccess == true) {
     SharedPreferences prefs = await SharedPreferences.getInstance();
      double currentPoints = prefs.getDouble('profitPointOnBuy') ?? 0.0;
       // Deduct coupon price
      double updatedPoints = currentPoints - couponPrice;
      if (updatedPoints < 0) updatedPoints = 0;

      // Save updated points back
      await prefs.setDouble('profitPointOnBuy', updatedPoints);

        notifyListeners();
        return result;
      } else {
        return ResponseResultModel(isSuccess: false);
      }
    }
    notifyListeners();
    return ResponseResultModel(isSuccess: false);
  } catch (e) {
    return ResponseResultModel(isSuccess: false);
  }
}

  //----------------------------------------------------------------------------
  
}
