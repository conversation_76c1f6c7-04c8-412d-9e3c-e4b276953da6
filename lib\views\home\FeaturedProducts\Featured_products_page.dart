import 'package:alderishop/components/layout.dart';
import 'package:alderishop/controllers/app_controller.dart';
import 'package:alderishop/controllers/products_controller.dart';
import 'package:alderishop/services/helper.dart';
import 'package:alderishop/views/categories/items/items.dart';
import 'package:alderishop/views/categories/items/items_Details.dart';
import 'package:alderishop/views/categories/items/widgets/headerWidget.dart';
import 'package:alderishop/views/home/<USER>';
import 'package:alderishop/views/home/<USER>/category/custom_categories_item.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

class FeaturedProductsPage extends StatefulWidget {
  const FeaturedProductsPage({super.key});

  @override
  State<FeaturedProductsPage> createState() => _FeaturedProductsPageState();
}

class _FeaturedProductsPageState extends State<FeaturedProductsPage>
    with TickerProviderStateMixin {
  AnimationController? animationController;
  final ScrollController _scrollController = ScrollController();
  final RefreshController _refreshController =
      RefreshController(initialRefresh: false);
  // ignore: unused_field
  bool _isLoading = true;
  @override
  void initState() {
    animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _onRefresh();
    super.initState();
  }

  @override
  void dispose() {
    animationController?.dispose();
    _scrollController.dispose();
    _refreshController.dispose();
    super.dispose();
  }

  void _onRefresh() async {
    setState(() {
      _isLoading = true;
    });
    await Provider.of<ProductsController>(context, listen: false)
        .getFeaturedProducts();
    _refreshController.refreshCompleted();
    setState(() {
      _isLoading = false;
    });
  }

  void _onLoading() async {
    await Provider.of<ProductsController>(context, listen: false)
        .getFeaturedProducts();
    if (mounted) {
      setState(() {});
    }
    _refreshController.loadComplete();
  }

  @override
  Widget build(BuildContext context) {
    final dataProvider = Provider.of<ProductsController>(context);
    final itemList = dataProvider.featuredProduct;
    return ApplicationLayout(
      content: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 15),
        child: Column(
          children: <Widget>[
            Expanded(
                child: NestedScrollView(
              controller: _scrollController,
              headerSliverBuilder:
                  (BuildContext context, bool innerBoxIsScrolled) {
                return <Widget>[
                  SliverList(
                    delegate: SliverChildBuilderDelegate(
                      (BuildContext context, int index) {
                        return const Column(
                          children: <Widget>[
                            SizedBox(),
                          ],
                        );
                      },
                      childCount: 1,
                    ),
                  ),
                  SliverPersistentHeader(
                    pinned: true,
                    floating: true,
                    delegate: ContestTabHeader(
                      HeaderWidget(
                        onTap: () {
                          Navigator.of(context).push(MaterialPageRoute(
                              builder: (context) => const HomePage()));
                        },
                        text: T('Featured Products'),
                      ),
                    ),
                  ),
                ];
              },
              body: Container(
                padding: const EdgeInsets.symmetric(horizontal: 5),
                color: Colors.white,
                child: SmartRefresher(
                  enablePullDown: true,
                  enablePullUp: true,
                  header: const WaterDropHeader(),
                  footer: CustomFooter(
                    builder: (context, mode) {
                      Widget body;
                      if (mode == LoadStatus.idle) {
                        body = Text(T("Pull up to load"));
                      } else if (mode == LoadStatus.failed) {
                        body = Text(T("Load Failed! Click retry!"));
                      } else if (mode == LoadStatus.canLoading) {
                        body = Text(T("Release to load more"));
                      } else {
                        body = Text(T("No more data"));
                      }
                      return SizedBox(
                        height: 55.0,
                        child: Center(child: body),
                      );
                    },
                  ),
                  controller: _refreshController,
                  onRefresh: _onRefresh,
                  onLoading: _onLoading,
                  child: AlignedGridView.count(
                    crossAxisCount: AppController.W > 700 ? 3 : 2,
                    mainAxisSpacing: 12,
                    crossAxisSpacing: 0,
                    physics: const NeverScrollableScrollPhysics(),
                    scrollDirection: Axis.vertical,
                    shrinkWrap: true,
                    itemCount: itemList.length,
                    itemBuilder: (BuildContext context, int index) {
                      final int count = itemList.length;

                      Tween<double>(begin: 0.0, end: 1.0).animate(
                        CurvedAnimation(
                          parent: animationController!,
                          curve: Interval((1 / count) * index, 1.0,
                              curve: Curves.fastOutSlowIn),
                        ),
                      );
                      animationController?.forward();

                      return InkWell(
                          onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => ItemDetails(
                                  item: itemList[index].id,
                                ),
                              ),
                            );
                          },
                          child: Padding(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 5, vertical: 0),
                            child: CustomCategoryItem(
                            
                              data: itemList[index],
                              animationController: animationController,
                            ),
                          ));
                    },
                  ),
                ),
              ),
            ))
          ],
        ),
      ),
      selectedBottomNavbarItem: BottomNavbarItems.home,
    );
  }
}
