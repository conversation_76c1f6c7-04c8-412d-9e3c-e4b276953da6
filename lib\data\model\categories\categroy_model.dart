class CategoryModel {
  int? id;
  int? parentId;
  String? parentName;
  String? name;
  int? erpId;
  String? imageUrl;
  int? order;
  bool? isActive;
  bool? hasChildren;
  List<Images>? images;
  List<SubCategories>? subCategories;

  CategoryModel({
    this.id,
    this.parentId,
    this.parentName,
    this.name,
    this.erpId,
    this.imageUrl,
    this.order,
    this.isActive,
    this.hasChildren,
    this.images,
    this.subCategories,
  });

  CategoryModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    parentId = json['parentId'];
    parentName = json['parentName'];
    name = json['name'];
    erpId = json['erpId'];
    imageUrl = json['imageUrl'];
    order = json['order'];
    isActive = json['isActive'];
    hasChildren = json['hasChildren'];

    if (json['images'] != null) {
      images = <Images>[];
      json['images'].forEach((v) {
        images!.add(Images.fromJson(v));
      });
    }

    if (json['subCategories'] != null) {
      subCategories = <SubCategories>[];
      json['subCategories'].forEach((v) {
        subCategories!.add(SubCategories.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['id'] = id;
    data['parentId'] = parentId;
    data['parentName'] = parentName;
    data['name'] = name;
    data['erpId'] = erpId;
    data['imageUrl'] = imageUrl;
    data['order'] = order;
    data['isActive'] = isActive;
    data['hasChildren'] = hasChildren;

    if (images != null) {
      data['images'] = images!.map((v) => v.toJson()).toList();
    }
    if (subCategories != null) {
      data['subCategories'] = subCategories!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Images {
  int? id;
  bool? isExistImage;
  String? fileName;
  String? base64File;
  int? categoryImageType;

  Images({
    this.id,
    this.isExistImage,
    this.fileName,
    this.base64File,
    this.categoryImageType,
  });

  Images.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    isExistImage = json['isExistImage'];
    fileName = json['fileName'];
    base64File = json['base64File'];
    categoryImageType = json['categoryImageType'];
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['id'] = id;
    data['isExistImage'] = isExistImage;
    data['fileName'] = fileName;
    data['base64File'] = base64File;
    data['categoryImageType'] = categoryImageType;
    return data;
  }
}

class SubCategories {
  int? id;
  int? parentId;
  String? parentName;
  String? name;
  int? erpId;
  String? imageUrl;
  int? order;
  bool? isActive;
  bool? hasChildren;
  List<Images>? images;
  List<SubCategories>? subCategories;

  SubCategories({
    this.id,
    this.parentId,
    this.parentName,
    this.name,
    this.erpId,
    this.imageUrl,
    this.order,
    this.isActive,
    this.hasChildren,
    this.images,
    this.subCategories,
  });

  SubCategories.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    parentId = json['parentId'];
    parentName = json['parentName'];
    name = json['name'];
    erpId = json['erpId'];
    imageUrl = json['imageUrl'];
    order = json['order'];
    isActive = json['isActive'];
    hasChildren = json['hasChildren'];

    if (json['images'] != null) {
      images = <Images>[];
      json['images'].forEach((v) {
        images!.add(Images.fromJson(v));
      });
    }

    if (json['subCategories'] != null) {
      subCategories = <SubCategories>[];
      json['subCategories'].forEach((v) {
        subCategories!.add(SubCategories.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['id'] = id;
    data['parentId'] = parentId;
    data['parentName'] = parentName;
    data['name'] = name;
    data['erpId'] = erpId;
    data['imageUrl'] = imageUrl;
    data['order'] = order;
    data['isActive'] = isActive;
    data['hasChildren'] = hasChildren;

    if (images != null) {
      data['images'] = images!.map((v) => v.toJson()).toList();
    }
    if (subCategories != null) {
      data['subCategories'] = subCategories!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}
