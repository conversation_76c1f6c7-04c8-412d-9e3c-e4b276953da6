// ignore: file_names
import 'package:alderishop/constants/constants.dart';
import 'package:alderishop/controllers/app_controller.dart';
import 'package:alderishop/views/home/<USER>/CustomText.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

class HeaderWidget extends StatelessWidget {
  final VoidCallback? onTap;
  final String? text;
  const HeaderWidget({super.key, this.onTap, this.text});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 5, horizontal: 5),
      color: AppColors.WHITE_COLOR,
      child: Row(
        children: [
          InkWell(
            onTap: onTap,
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: FaIcon(
                AppController.currentLangId == 2
                    ? FontAwesomeIcons.arrowRight
                    : FontAwesomeIcons.arrowLeft,
                color: AppColors.PRIMARY_COLOR,
                size: 18,
              ),
            ),
          ),
          const SizedBox(width: 10),
          Flexible(
            child: Container(
              width: AppController.W - 20,
              child: CustomText(
                text: text!,
                fontSize: 12.sp,
                fontWeight: FontWeight.w700,
                color: AppColors.PRIMARY_COLOR,
              ),
            ),
          )
        ],
      ),
    );
  }
}
