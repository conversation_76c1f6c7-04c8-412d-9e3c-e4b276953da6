import 'package:alderishop/components/common_cache_image.dart';
import 'package:alderishop/controllers/app_controller.dart';
import 'package:alderishop/controllers/products_controller.dart';
import 'package:alderishop/data/api/api.dart';
import 'package:alderishop/views/categories/items/category_item.dart';
import 'package:alderishop/views/home/<USER>/SubcategoriesPage.dart';
import 'package:alderishop/views/home/<USER>/Subcategory_And_Product_Page.dart';
import 'package:alderishop/views/home/<USER>/empty_page.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:page_transition/page_transition.dart';
import 'package:provider/provider.dart';
import 'package:alderishop/controllers/categores_controller.dart';

class CustomContainerList extends StatefulWidget {
  const CustomContainerList({
    Key? key,
  }) : super(key: key);

  @override
  State<CustomContainerList> createState() => _CustomContainerListState();
}

class _CustomContainerListState extends State<CustomContainerList> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final dataProvider = Provider.of<CategoryController>(context);
    final categories =
        dataProvider.category.where((cat) => cat.parentId == null).toList();

    if (categories.isEmpty) {
      return const Center(child: CircularProgressIndicator());
    }

    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: categories.length,
      itemBuilder: (BuildContext context, int index) {
        final category = categories[index];
        final images = category.images ?? [];

        return GestureDetector(
         onTap: () async {
  final controllerCate = Provider.of<CategoryController>(context, listen: false);
  final controllerProd = Provider.of<ProductsController>(context, listen: false);

  controllerCate.clearSubCategoryStack();

  // Fetch subcategories
  await controllerCate.getSubCategories(
    parentId: category.id ?? 0,
    pushToStack: false,
  );

  // Fetch products
  final products = await controllerProd.getProductCategories(categoryId: category.id ?? 0);

  if (!mounted) return;

  final hasSubcategories = controllerCate.subcategories.isNotEmpty;
  final hasProducts = products.isNotEmpty;

  if (hasSubcategories && hasProducts) {
    Navigator.push(
      // ignore: use_build_context_synchronously
      context,
      PageTransition(
        type: AppController.currentLangId == 2
            ? PageTransitionType.leftToRight
            : PageTransitionType.rightToLeftWithFade,
        child: SubcategoryAndProductPage(category: category),
      ),
    );
  } else if (!hasSubcategories && hasProducts) {
    Navigator.push(
      // ignore: use_build_context_synchronously
      context,
      PageTransition(
        type: AppController.currentLangId == 2
            ? PageTransitionType.leftToRight
            : PageTransitionType.rightToLeftWithFade,
        child: CategoryItem(category: category),
      ),
    );
  } else if(hasSubcategories && !hasProducts){
    Navigator.push(
      // ignore: use_build_context_synchronously
      context,
      PageTransition(
        type: AppController.currentLangId == 2
            ? PageTransitionType.leftToRight
            : PageTransitionType.rightToLeftWithFade,
        child: Subcategoriespage(parentCategory: category),
      ),
    );
  }else{
     Navigator.push(
      // ignore: use_build_context_synchronously
      context,
      PageTransition(
        type: AppController.currentLangId == 2
            ? PageTransitionType.leftToRight
            : PageTransitionType.rightToLeftWithFade,
        child:const NoDataPage(),
      ),
    );
  }
}
,
          child: Container(
            decoration: BoxDecoration(
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.4),
                  blurRadius: 4,
                  offset: const Offset(2, 2),
                ),
              ],
            ),
            child: Stack(
              children: [
                images.length > 1
                    ? CarouselSlider(
                        options: CarouselOptions(
                          viewportFraction: 1.0,
                          autoPlay: true,
                          enlargeCenterPage: false,
                        ),
                        items: images.map((imageObj) {
                          return CachedImage(
                            imageUrl: "$baseUrl1/${imageObj.fileName ?? ''}",
                            fit: BoxFit.contain,
                            width: double.infinity,
                          );
                        }).toList(),

                      )
                    : CachedImage(
                        imageUrl:
                            "$baseUrl1/${images.isNotEmpty ? images.first.fileName : ''}",
                        fit: BoxFit.cover,
                        width: double.infinity,
                      ),
                Positioned(
                  bottom: 1,
                  child: Container(
                    padding: const EdgeInsets.symmetric(vertical: 20),
                    width: AppController.W,
                    decoration: const BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.bottomCenter,
                        end: Alignment.topCenter,
                        colors: [
                          Colors.black87,
                          Colors.black45,
                          Colors.black12,
                        ],
                      ),
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(20),
                        topRight: Radius.circular(20),
                      ),
                    ),
                    child: Center(
                      child: Text(
                        category.name ?? "",
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          shadows: [
                            Shadow(
                              blurRadius: 2,
                              color: Colors.black54,
                              offset: Offset(1, 1),
                            )
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}