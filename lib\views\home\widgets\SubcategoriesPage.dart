import 'package:alderishop/components/common_snckbar.dart';
import 'package:alderishop/components/layout.dart';
import 'package:alderishop/constants/constants.dart';
import 'package:alderishop/controllers/app_controller.dart';
import 'package:alderishop/controllers/categores_controller.dart';
import 'package:alderishop/controllers/products_controller.dart';
import 'package:alderishop/data/model/categories/categroy_model.dart';
import 'package:alderishop/services/helper.dart';
import 'package:alderishop/views/categories/items/category_item.dart';
import 'package:alderishop/views/categories/items/widgets/filterWidget.dart';
import 'package:alderishop/views/home/<USER>/SearchPage.dart';
import 'package:alderishop/views/home/<USER>/Subcategory_And_Product_Page.dart';
import 'package:alderishop/views/home/<USER>/subCategoriesitem.dart';
import 'package:alderishop/views/home/<USER>/home_header.dart';
import 'package:alderishop/views/home/<USER>/home_imag_slider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:page_transition/page_transition.dart';
import 'package:provider/provider.dart';

// ignore: must_be_immutable
class Subcategoriespage extends StatefulWidget {
  CategoryModel parentCategory;

  Subcategoriespage({super.key, required this.parentCategory});

  @override
  State<Subcategoriespage> createState() => _SubcategoriespageState();
}

class _SubcategoriespageState extends State<Subcategoriespage>
    with WidgetsBindingObserver {
  final sortOptions = [
    SortOption(
      value: 'az',
      label: 'الاسم من A إلى Z',
      icon: Icons.sort_by_alpha,
      iconColor: AppColors.PRIMARY_COLOR,
    ),
    SortOption(
      value: 'za',
      label: 'الاسم من Z إلى A',
      icon: Icons.sort_by_alpha,
      iconColor: AppColors.PRIMARY_COLOR,
    ),
  ];
  bool isGrid = true;
  String _sortBy = 'none';
  void _applySort(List<CategoryModel> products) {
    if (_sortBy == 'az') {
      products.sort((a, b) => (a.name ?? '').compareTo(b.name ?? ''));
    } else if (_sortBy == 'za') {
      products.sort((a, b) => (b.name ?? '').compareTo(a.name ?? ''));
    }
  }

  @override
  initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);

    final controller = Provider.of<CategoryController>(context, listen: false);
    // Don't push to stack on initial load
    controller.getSubCategories(
        parentId: widget.parentCategory.id ?? 0, pushToStack: false);
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      // Reload subcategories when app becomes active again
      _reloadSubcategories();
    }
  }

  void _reloadSubcategories() {
    final controller = Provider.of<CategoryController>(context, listen: false);
    controller.getSubCategories(parentId: widget.parentCategory.id ?? 0);
  }

  @override
  Widget build(BuildContext context) {
    final dataProvider = Provider.of<CategoryController>(context).subcategories;

    return PopScope(
      onPopInvoked: (didPop) async {
        // When going back, restore previous subcategories from stack
        if (didPop) {
          final controller =
              Provider.of<CategoryController>(context, listen: false);
          controller.popSubCategories();
        }
      },
      child: ApplicationLayout(
        content: SingleChildScrollView(
          child: Column(
            children: [
              SizedBox(
                height: AppController.h * 0.015,
              ),
              //-------------------------------Search--------------------------
                            Row(
                children: [
                  // Compact back button
                  Container(
                    margin: const EdgeInsets.symmetric(horizontal: 16),
                    decoration: BoxDecoration(
                      color: AppColors.PRIMARY_COLOR.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Material(
                      color: Colors.transparent,
                      child: InkWell(
                        onTap: () {
                          Navigator.of(context).pop();
                        },
                        borderRadius: BorderRadius.circular(8),
                        child: Container(
                          padding: const EdgeInsets.all(8),
                          child: Icon(
                            AppController.currentLangId == 2
                                ? Icons.arrow_forward_ios
                                : Icons.arrow_back_ios,
                            color: AppColors.PRIMARY_COLOR,
                            size: 18,
                          ),
                        ),
                      ),
                    ),
                  ),
                  // Expanded search header to take remaining space
                  const Expanded(
                    child: HomeHeader(
                      disPlayFilter: false,
                    ),
                  ),
                  const SizedBox(width: 12),
                ],
              ),
              //-------------------------------SliderImage--------------------------
              SizedBox(
                height: AppController.h * 0.03,
              ),
              const HomeBanner(
                autoPlay: true,
              ),
              SizedBox(
                height: AppController.h * 0.015,
              ),
              TopFilterBar(
                isGrid: isGrid,
                onToggleView: () {
                  setState(() {
                    isGrid = !isGrid;
                  });
                },
                onSort: (String value) {
                  setState(() {
                    _sortBy = value; // update sort type
                    _applySort(dataProvider); // apply sort
                  });
                },
                onFilter: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(builder: (context) => const SearchPage()),
                  );
                },
                sortOptions: sortOptions,
              ),
              //-------------------------------Subcategories--------------------------

              Text(
                T('SubCategories'),
                style: TextStyle(
                  color: AppColors.BLACK_COLOR,
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
              const SizedBox(
                height: 5,
              ),
              AlignedGridView.count(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                crossAxisCount: isGrid ? (AppController.W > 700 ? 3 : 2) : 1,
                mainAxisSpacing: 0,
                crossAxisSpacing: 0,
                scrollDirection: Axis.vertical,
                itemCount: dataProvider.length,
                itemBuilder: (BuildContext context, int index) {
                  final category = dataProvider[index];

                  return InkWell(
                onTap: () async {
  final controller = Provider.of<CategoryController>(context, listen: false);
  final productCtrl = Provider.of<ProductsController>(context, listen: false);
  final categoryId = category.id ?? 0;

  // تحفظ الحالة الحالية قبل التنقل
  controller.pushCurrentStateToStack(widget.parentCategory.id ?? 0);

  // تجيب التصنيفات الفرعية (ساب كاتيجوريز)
  await controller.getSubCategories(parentId: categoryId, pushToStack: false);

  // تجيب المنتجات
  final products = await productCtrl.getProductCategories(categoryId: categoryId);

  if (!mounted) return;

  final hasSubcategories = controller.subcategories.isNotEmpty;
  final hasProducts = products.isNotEmpty;

  if (hasSubcategories && hasProducts) {
    // اذا في تصنيفات فرعية ومنتجات مع بعض
    Navigator.push(
      context,
      PageTransition(
        type: AppController.currentLangId == 2
            ? PageTransitionType.leftToRight
            : PageTransitionType.rightToLeft,
        child: SubcategoryAndProductPage(category: category),
      ),
    );
  } else if (hasProducts) {
    // اذا في منتجات بس بدون تصنيفات فرعية
    Navigator.push(
      context,
      PageTransition(
        type: AppController.currentLangId == 2
            ? PageTransitionType.leftToRight
            : PageTransitionType.rightToLeft,
        child: CategoryItem(category: category),
      ),
    );
  } else if (hasSubcategories) {
    // اذا في تصنيفات فرعية بس بدون منتجات
    Navigator.push(
      context,
      PageTransition(
        type: AppController.currentLangId == 2
            ? PageTransitionType.leftToRight
            : PageTransitionType.rightToLeft,
        child: Subcategoriespage(parentCategory: category),
      ),
    );
  } else {
    // لا يوجد لا تصنيفات فرعية ولا منتجات
    errorMsg(context: context, title: T("لا توجد منتجات في هذا القسم"));
  }
},


                    child:
                        Subcategoriesitem(category: category, isGrid: isGrid),
                  );
                },
              ),
              const SizedBox(
                height: 15,
              ),
            ],
          ),
        ),
        selectedBottomNavbarItem: BottomNavbarItems.home,
      ),
    );
  }
}
