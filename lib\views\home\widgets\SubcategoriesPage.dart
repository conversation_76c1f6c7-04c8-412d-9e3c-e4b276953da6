import 'package:alderishop/components/layout.dart';
import 'package:alderishop/constants/constants.dart';
import 'package:alderishop/controllers/app_controller.dart';
import 'package:alderishop/controllers/categores_controller.dart';
import 'package:alderishop/data/model/categories/categroy_model.dart';
import 'package:alderishop/services/helper.dart';
import 'package:alderishop/views/categories/items/category_item.dart';
import 'package:alderishop/views/categories/items/widgets/filterWidget.dart';
import 'package:alderishop/views/home/<USER>/SearchPage.dart';
import 'package:alderishop/views/home/<USER>/category/subCategoriesitem.dart';
import 'package:alderishop/views/home/<USER>/home_header.dart';
import 'package:alderishop/views/home/<USER>/home_imag_slider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:page_transition/page_transition.dart';
import 'package:provider/provider.dart';

// ignore: must_be_immutable
class Subcategoriespage extends StatefulWidget {
  CategoryModel parentCategory;

  Subcategoriespage({super.key, required this.parentCategory});

  @override
  State<Subcategoriespage> createState() => _SubcategoriespageState();
}

class _SubcategoriespageState extends State<Subcategoriespage> {
  final sortOptions = [
    SortOption(
      value: 'az',
      label: 'الاسم من A إلى Z',
      icon: Icons.sort_by_alpha,
      iconColor: AppColors.PRIMARY_COLOR,
    ),
    SortOption(
      value: 'za',
      label: 'الاسم من Z إلى A',
      icon: Icons.sort_by_alpha,
      iconColor: AppColors.PRIMARY_COLOR,
    ),
  ];
  bool isGrid = true;
  String _sortBy = 'none';
  void _applySort(List<CategoryModel> products) {
    if (_sortBy == 'az') {
      products.sort((a, b) => (a.name ?? '').compareTo(b.name ?? ''));
    } else if (_sortBy == 'za') {
      products.sort((a, b) => (b.name ?? '').compareTo(a.name ?? ''));
    }
  }

  @override
  initState() {
    super.initState();
    
    final controller = Provider.of<CategoryController>(context, listen: false);
    controller.getSubCategories(parentId: widget.parentCategory.id ?? 0);
  }

  @override
  Widget build(BuildContext context) {
    final dataProvider = Provider.of<CategoryController>(context).subcategories;

    return ApplicationLayout(
      content: SingleChildScrollView(
        child: Column(
          children: [
            SizedBox(
              height: AppController.h * 0.015,
            ),
            //-------------------------------Search--------------------------
            Row(
              children: [
                const HomeHeader(
                  disPlayFilter: false,
                ),
                const SizedBox(
                  width: 5,
                ),
                InkWell(
                    onTap: () {
                      Navigator.of(context).pop();
                    },
                    child: Icon(Icons.arrow_forward)),
              ],
            ),
            //-------------------------------SliderImage--------------------------
            SizedBox(
              height: AppController.h * 0.03,
            ),
            const HomeBanner(
              autoPlay: true,
            ),
            SizedBox(
              height: AppController.h * 0.015,
            ),
            TopFilterBar(
              isGrid: isGrid,
              onToggleView: () {
                setState(() {
                  isGrid = !isGrid;
                });
              },
              onSort: (String value) {
                setState(() {
                  _sortBy = value; // update sort type
                  _applySort(dataProvider); // apply sort
                });
              },
              onFilter: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => const SearchPage()),
                );
              },
              sortOptions: sortOptions,
            ),
            //-------------------------------Subcategories--------------------------

            Text(
              T('SubCategories'),
              style: TextStyle(
                color: AppColors.BLACK_COLOR,
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
            const SizedBox(
              height: 5,
            ),
            AlignedGridView.count(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              crossAxisCount: isGrid ? (AppController.W > 700 ? 3 : 2) : 1,
              mainAxisSpacing: 0,
              crossAxisSpacing: 0,
              scrollDirection: Axis.vertical,
              itemCount: dataProvider.length,
              itemBuilder: (BuildContext context, int index) {
                final category = dataProvider[index];

                return InkWell(
                  onTap: () async {
                    final controller =
                        Provider.of<CategoryController>(context, listen: false);
                    await controller.getSubCategories(
                        parentId: category.id ?? 0);

                    if (controller.subcategories.isNotEmpty) {
                      Navigator.push(
                        context,
                        PageTransition(
                          type: AppController.currentLangId == 2
                              ? PageTransitionType.leftToRight
                              : PageTransitionType.rightToLeft,
                          child: Subcategoriespage(parentCategory: category),
                        ),
                      );
                    } else {
                      Navigator.push(
                        context,
                        PageTransition(
                          type: AppController.currentLangId == 2
                              ? PageTransitionType.leftToRight
                              : PageTransitionType.rightToLeft,
                          child: CategoryItem(category: category),
                        ),
                      );
                    }
                  },
                  child: Subcategoriesitem(category: category, isGrid: isGrid),
                );
              },
            ),
          ],
        ),
      ),
      selectedBottomNavbarItem: BottomNavbarItems.home,
    );
  }
}
