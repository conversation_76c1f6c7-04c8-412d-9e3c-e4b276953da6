import 'package:alderishop/constants/constants.dart';
import 'package:alderishop/controllers/products_controller.dart';
import 'package:alderishop/data/model/Products/attributesDto.dart';
import 'package:alderishop/data/model/categories/categroy_model.dart';

import 'package:alderishop/data/model/search/search_model.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class SelectCategoryForFilter extends StatefulWidget {
  final List<CategoryModel> items; // Accept List<String> for items
final ValueChanged<CategoryModel?> onSelectionChanged;


  const SelectCategoryForFilter({
    Key? key,
    required this.items,
    required this.onSelectionChanged,
  }) : super(key: key);

  @override
  // ignore: library_private_types_in_public_api
  _SelectCategoryForFilterState createState() =>
      _SelectCategoryForFilterState();
}

class _SelectCategoryForFilterState extends State<SelectCategoryForFilter> {
  int? selectedItem;
List<CategoryModel> subCategories = [];

  @override
  void initState() {
    selectedItem = Provider.of<ProductsController>(context, listen: false)
            .searchFilter
            .categoryId ??
        0;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Wrap(
      spacing: 8,
      children: widget.items.map((item) {
        bool isSelected = selectedItem == item.id;
        return GestureDetector(
          onTap: () {
  setState(() {
    if (isSelected) {
      selectedItem = null;
      widget.onSelectionChanged(null); // no category selected
    } else {
      selectedItem = item.id;
      widget.onSelectionChanged(item); // send full category
    }
  });
},

          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 3),
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 5, horizontal: 15),
              decoration: BoxDecoration(
                color: isSelected ? AppColors.PRIMARY_COLOR : Colors.white,
                border: Border.all(color: Colors.grey),
                borderRadius: BorderRadius.circular(5),
              ),
              child: Text(
                item.name ?? "",
                style: TextStyle(
                  color: isSelected ? Colors.white : Colors.black,
                  fontSize: 14,
                ),
              ),
            ),
          ),
        );
      }).toList(),
    );
  }
}

class MultiSelectChip extends StatefulWidget {
  final String atterName;
  final List<OptionsDTo> items;
  final Function(AttrFilter) onSelectionChanged;

  const MultiSelectChip({
    Key? key,
    required this.items,
    required this.onSelectionChanged,
    required this.atterName,
  }) : super(key: key);

  @override
  // ignore: library_private_types_in_public_api
  _MultiSelectChipState createState() => _MultiSelectChipState();
}

class _MultiSelectChipState extends State<MultiSelectChip> {
  List<int> selectedIds = [];

  @override
  void initState() {
    selectedIds = Provider.of<ProductsController>(context, listen: false)
            .searchFilter
            .attrFilter
            ?.map((e) => e.optionId ?? 0)
            .toList() ??
        [];
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Wrap(
      spacing: 8,
      children: widget.items.map((item) {
        bool isSelected = selectedIds.any((e) => e == item.optionId);

        return GestureDetector(
          onTap: () {
            setState(() {
              if (isSelected) {
                selectedIds.removeWhere((e) => e == item.optionId);
              } else {
                selectedIds.add(item.optionId ?? 0);
              }
              setState(() {});
              // provider.notifyListeners(); // Notify provider of changes
            });

            // Notify parent widget
            widget.onSelectionChanged(AttrFilter(
              name: widget.atterName,
              optionId: item.optionId,
              value: item.optionName,
            ));
          },
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 3),
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 5, horizontal: 15),
              decoration: BoxDecoration(
                color: isSelected ? AppColors.PRIMARY_COLOR : Colors.white,
                border: Border.all(color: Colors.grey),
                borderRadius: BorderRadius.circular(5),
              ),
              child: Text(
                item.optionName ?? "",
                style: TextStyle(
                  color: isSelected ? Colors.white : Colors.black,
                  fontSize: 14,
                ),
              ),
            ),
          ),
        );
      }).toList(),
    );
  }
}
