class TranslateModel {
  int? entityId;
  int? languageId;
  String? localeKeyGroup;
  String? localeKey;
  String? localeValue;
  bool? isHidden;
  String? createdOnUtc;
  String? updatedOnUtc;

  String? updatedBy;

  int? productId;

  int? id;

  TranslateModel(
      {this.entityId,
      this.languageId,
      this.localeKeyGroup,
      this.localeKey,
      this.localeValue,
      this.isHidden,
      this.createdOnUtc,
      this.updatedOnUtc,
      this.updatedBy,
      this.productId,
      this.id});

  TranslateModel.fromJson(Map<String, dynamic> json) {
    entityId = json['EntityId'];
    languageId = json['LanguageId'];
    localeKeyGroup = json['LocaleKeyGroup'];
    localeKey = json['LocaleKey'];
    localeValue = json['LocaleValue'];
    isHidden = json['IsHidden'];
    createdOnUtc = json['CreatedOnUtc'];
    updatedOnUtc = json['UpdatedOnUtc'];

    updatedBy = json['UpdatedBy'];

    productId = json['ProductId'];

    id = json['Id'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['EntityId'] = entityId;
    data['LanguageId'] = languageId;
    data['LocaleKeyGroup'] = localeKeyGroup;
    data['LocaleKey'] = localeKey;
    data['LocaleValue'] = localeValue;
    data['IsHidden'] = isHidden;
    data['CreatedOnUtc'] = createdOnUtc;
    data['UpdatedOnUtc'] = updatedOnUtc;

    data['UpdatedBy'] = updatedBy;

    data['ProductId'] = productId;

    data['Id'] = id;
    return data;
  }
}
